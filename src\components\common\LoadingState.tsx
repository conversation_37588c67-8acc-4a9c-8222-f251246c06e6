import React from 'react';
import { motion } from 'framer-motion';

interface LoadingStateProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  fullScreen?: boolean;
  transparent?: boolean;
}

/**
 * Loading state component with animation
 */
const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  size = 'medium',
  fullScreen = false,
  transparent = false,
}) => {
  // Size configurations
  const sizeConfig = {
    small: {
      container: 'p-4',
      spinner: 'w-6 h-6 border-2',
      text: 'text-sm mt-2',
    },
    medium: {
      container: 'p-6',
      spinner: 'w-10 h-10 border-3',
      text: 'text-base mt-3',
    },
    large: {
      container: 'p-8',
      spinner: 'w-16 h-16 border-4',
      text: 'text-lg mt-4',
    },
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.3,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    },
  };

  const spinnerVariants = {
    animate: {
      rotate: 360,
      transition: {
        repeat: Infinity,
        duration: 1,
        ease: "linear"
      }
    }
  };

  const textVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  // Render the loading state
  const renderContent = () => (
    <motion.div 
      className={`flex flex-col items-center justify-center ${sizeConfig[size].container}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div 
        className={`${sizeConfig[size].spinner} border-t-primary border-r-primary border-b-transparent border-l-transparent rounded-full`}
        variants={spinnerVariants}
        animate="animate"
      />
      {message && (
        <motion.p 
          className={`${sizeConfig[size].text} text-gray-700 dark:text-gray-300`}
          variants={textVariants}
        >
          {message}
        </motion.p>
      )}
    </motion.div>
  );

  // Full screen loading state
  if (fullScreen) {
    return (
      <div className={`fixed inset-0 flex items-center justify-center z-50 ${transparent ? 'bg-white/70 dark:bg-gray-900/70' : 'bg-white dark:bg-gray-900'}`}>
        {renderContent()}
      </div>
    );
  }

  // Inline loading state
  return renderContent();
};

export default LoadingState;
