{"name": "productwhisper-api", "version": "1.0.0", "description": "Backend API for ProductWhisper - A sentiment analysis platform for product reviews", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "node-pg-migrate", "migrate:up": "node-pg-migrate up", "migrate:down": "node-pg-migrate down", "migrate:create": "node-pg-migrate create", "db:init": "ts-node src/scripts/initDb.ts"}, "keywords": ["sentiment-analysis", "product-reviews", "reddit-api", "express", "typescript"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "joi": "^17.11.0", "morgan": "^1.10.0", "node-pg-migrate": "^6.2.2", "pg": "^8.11.3", "redis": "^4.6.11", "socket.io": "^4.7.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^11.1.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.10", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}}