{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "node-pg-migrate", "migrate:up": "node-pg-migrate up", "migrate:down": "node-pg-migrate down", "migrate:create": "node-pg-migrate create", "db:init": "node src/scripts/initDb.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.10.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-pg-migrate": "^7.9.1", "pg": "^8.15.6", "prom-client": "^15.1.3", "redis": "^5.0.1", "response-time": "^2.3.3", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/csurf": "^1.11.5", "@types/express": "^5.0.1", "@types/express-rate-limit": "^5.1.3", "@types/express-validator": "^2.20.33", "@types/hpp": "^0.2.6", "@types/node": "^22.15.14", "@types/pg": "^8.15.0", "@types/socket.io": "^3.0.1", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "dotenv-cli": "^8.0.0", "nodemon": "^3.1.10", "pg-migrate": "^2.0.1", "typescript": "^5.8.3"}}