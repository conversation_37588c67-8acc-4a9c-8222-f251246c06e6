import { query } from '../config/database';
import { getCache, setCache } from '../config/redis';
import logger from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';
import { ChatMessage, ChatResponse } from '../models/types';

// FAQ data
const faqData = [
  {
    question: 'What is ProductWhisper?',
    answer: 'ProductWhisper is a sentiment analysis platform for product reviews. It helps you discover products through sentiment analysis of reviews and discussions from various sources like Reddit.',
  },
  {
    question: 'How does ProductWhisper work?',
    answer: 'ProductWhisper analyzes product reviews and discussions from various sources using natural language processing and sentiment analysis. It extracts insights about products, identifies trends, and helps you make informed purchasing decisions.',
  },
  {
    question: 'Where does ProductWhisper get its data?',
    answer: 'ProductWhisper collects data from various sources including Reddit discussions, product review websites, and other public forums where people discuss products.',
  },
  {
    question: 'How accurate is the sentiment analysis?',
    answer: 'Our sentiment analysis uses state-of-the-art natural language processing techniques. While no sentiment analysis is perfect, we continuously improve our algorithms to provide the most accurate results possible.',
  },
  {
    question: 'Can I compare products?',
    answer: 'Yes! ProductWhisper allows you to compare products based on various factors including sentiment analysis, feature comparison, and price.',
  },
  {
    question: 'How do I search for products?',
    answer: 'You can use the search bar at the top of the page to search for products by name, brand, or category.',
  },
  {
    question: 'What are trending products?',
    answer: 'Trending products are products that are currently being discussed frequently or have significant changes in sentiment. You can find trending products on our Trends page.',
  },
  {
    question: 'How can I provide feedback?',
    answer: 'We value your feedback! You can provide feedback through the chat interface or by contacting us through the Contact page.',
  },
];

/**
 * Process a chat message
 */
export const processMessage = async (message: string, sessionId?: string): Promise<ChatResponse> => {
  try {
    // Generate a new session ID if not provided
    const chatSessionId = sessionId || uuidv4();

    // Generate a message ID
    const messageId = uuidv4();

    // Save user message
    await saveMessage({
      id: uuidv4(),
      sessionId: chatSessionId,
      content: message,
      role: 'user',
      timestamp: new Date(),
    });

    // Find best matching FAQ
    const response = findBestResponse(message);

    // Save assistant message
    await saveMessage({
      id: messageId,
      sessionId: chatSessionId,
      content: response,
      role: 'assistant',
      timestamp: new Date(),
    });

    return {
      messageId,
      text: response,
      sessionId: chatSessionId,
    };
  } catch (error) {
    logger.error('Error processing chat message:', error);
    throw error;
  }
};

/**
 * Save chat message to database
 */
const saveMessage = async (message: ChatMessage): Promise<void> => {
  try {
    await query(
      `INSERT INTO chat_messages (id, session_id, content, role, timestamp)
       VALUES ($1, $2, $3, $4, $5)`,
      [message.id, message.sessionId, message.content, message.role, message.timestamp]
    );
  } catch (error) {
    logger.error('Error saving chat message:', error);
    // Don't throw error to avoid breaking the chat flow
  }
};

/**
 * Find best matching response from FAQ
 */
const findBestResponse = (message: string): string => {
  // Convert message to lowercase for better matching
  const lowerMessage = message.toLowerCase();

  // Check for exact matches first
  for (const faq of faqData) {
    if (lowerMessage.includes(faq.question.toLowerCase())) {
      return faq.answer;
    }
  }

  // Check for keyword matches
  const keywords = [
    { words: ['what', 'productwhisper'], response: faqData[0].answer },
    { words: ['how', 'work'], response: faqData[1].answer },
    { words: ['where', 'data'], response: faqData[2].answer },
    { words: ['accurate', 'sentiment'], response: faqData[3].answer },
    { words: ['compare', 'products'], response: faqData[4].answer },
    { words: ['search', 'find'], response: faqData[5].answer },
    { words: ['trend', 'trending'], response: faqData[6].answer },
    { words: ['feedback', 'suggest'], response: faqData[7].answer },
  ];

  for (const keyword of keywords) {
    if (keyword.words.every(word => lowerMessage.includes(word))) {
      return keyword.response;
    }
  }

  // Default response if no match found
  return "I'm not sure I understand your question. You can ask me about ProductWhisper, how it works, where we get our data, or how to use the platform.";
};

/**
 * Save feedback for a chat message
 */
export const saveFeedback = async (messageId: string, helpful: boolean): Promise<void> => {
  try {
    await query(
      `INSERT INTO chat_feedback (message_id, helpful, timestamp)
       VALUES ($1, $2, NOW())
       ON CONFLICT (message_id) DO UPDATE
       SET helpful = $2, timestamp = NOW()`,
      [messageId, helpful]
    );
  } catch (error) {
    logger.error('Error saving chat feedback:', error);
    throw error;
  }
};

/**
 * Get chat analytics
 */
export const getChatAnalytics = async (): Promise<any> => {
  try {
    // Get most common questions
    const commonQuestionsResult = await query(
      `SELECT content, COUNT(*) as count
       FROM chat_messages
       WHERE role = 'user'
       GROUP BY content
       ORDER BY count DESC
       LIMIT 10`
    );

    // Get feedback statistics
    const feedbackResult = await query(
      `SELECT
        COUNT(*) as total,
        SUM(CASE WHEN helpful THEN 1 ELSE 0 END) as helpful,
        SUM(CASE WHEN NOT helpful THEN 1 ELSE 0 END) as unhelpful
       FROM chat_feedback`
    );

    return {
      commonQuestions: commonQuestionsResult.rows.map((row: any) => ({
        question: row.content,
        count: parseInt(row.count),
      })),
      feedback: {
        total: parseInt(feedbackResult.rows[0].total) || 0,
        helpful: parseInt(feedbackResult.rows[0].helpful) || 0,
        unhelpful: parseInt(feedbackResult.rows[0].unhelpful) || 0,
      },
    };
  } catch (error) {
    logger.error('Error getting chat analytics:', error);
    throw error;
  }
};
