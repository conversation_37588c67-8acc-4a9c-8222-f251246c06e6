import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useSearchParams, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiTrendingUp, FiTrendingDown, FiAlertCircle, FiFilter, FiCalendar, FiSearch, FiBarChart2, FiMessageCircle, FiGlobe, FiVideo, FiShoppingBag } from 'react-icons/fi';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { apiService } from '../services/api';
import { useToast } from '../components/common/Toast';
import { <PERSON><PERSON><PERSON>, Bar<PERSON>hart } from '../components/charts';
// import { AnimatedCircles } from '../components/ui';
import RedditTrendPanel from '../components/trends/RedditTrendPanel';
import DataSourceSelector, { type DataSource } from '../components/trends/DataSourceSelector';

interface TrendDataPoint {
  date: string;
  value: number;
  confidence?: number;
}

interface TrendResponse {
  product_id: number;
  trend_type: string;
  period: string;
  data_points: TrendDataPoint[];
  error?: string;
}

interface AspectTrendDataPoint {
  date: string;
  aspects: {
    [key: string]: {
      count: number;
      sentiment: number;
    }
  }
}

interface AspectTrendResponse {
  product_id: number;
  trend_type: string;
  period: string;
  data_points: AspectTrendDataPoint[];
  error?: string;
}

interface AllTrendsResponse {
  product_id: number;
  period: string;
  sentiment: TrendResponse;
  mentions: TrendResponse;
  aspects: AspectTrendResponse;
}

interface Product {
  id: number;
  name: string;
  brand: string;
  category: string;
  description: string;
  price: number;
  rating: number;
  reviewCount: number;
  sentimentScore: number;
}

const TrendsPage: React.FC = () => {
  const { productId } = useParams<{ productId?: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const { showToast } = useToast();

  const [product, setProduct] = useState<Product | null>(null);
  const [trendData, setTrendData] = useState<AllTrendsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [period, setPeriod] = useState(searchParams.get('period') || 'month'); // day, week, month, quarter, year
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [dataSource, setDataSource] = useState<DataSource>('reddit');
  const [redditTrendData, setRedditTrendData] = useState<any>(null);
  const [redditLoading, setRedditLoading] = useState(false);

  // Fetch product details and trend data
  useEffect(() => {
    const fetchData = async () => {
      if (!productId) {
        setError('No product selected');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Update URL with period
        if (period !== searchParams.get('period')) {
          setSearchParams({ period });
        }

        // Fetch product details and trend data in parallel
        const [productDetails, trendsData] = await Promise.all([
          fetchProductDetails(parseInt(productId)),
          fetchTrendData(parseInt(productId), period)
        ]);

        setProduct(productDetails);
        setTrendData(trendsData);
      } catch (err) {
        setError('Failed to load trend data');
        console.error('Error fetching trend data:', err);
        showToast({
          type: 'error',
          title: 'Error',
          message: 'Failed to load trend data'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [productId, period, searchParams, setSearchParams, showToast]);

  // Fetch Reddit trend data
  useEffect(() => {
    const fetchRedditTrends = async () => {
      try {
        setRedditLoading(true);

        // Fetch Reddit trend data
        const timeframe = period === 'day' ? 'day' : period === 'week' ? 'week' : 'month';
        // If product is available, use its category, otherwise use a general category
        const category = product ? product.category : 'electronics';
        const data = await apiService.getRedditTrendingProducts(category, timeframe, 10);

        setRedditTrendData(data);
      } catch (err) {
        console.error('Error fetching Reddit trend data:', err);
        // Don't show toast for this as it's not critical
      } finally {
        setRedditLoading(false);
      }
    };

    fetchRedditTrends();
  }, [product, period]);

  // Fetch product details
  const fetchProductDetails = async (productId: number): Promise<Product> => {
    try {
      const response = await apiService.getProductDetails(productId);
      return response;
    } catch (error) {
      console.error('Error fetching product details:', error);

      // Return mock data for development
      return {
        id: productId,
        name: "Premium Wireless Headphones",
        brand: "SoundMaster",
        category: "Electronics",
        description: "Experience crystal-clear audio with our premium wireless headphones.",
        price: 249.99,
        rating: 4.7,
        reviewCount: 1243,
        sentimentScore: 0.85
      };
    }
  };

  // Fetch trend data
  const fetchTrendData = async (productId: number, period: string): Promise<AllTrendsResponse> => {
    try {
      const response = await apiService.get(`/trends/all/${productId}?period=${period}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching trend data:', error);

      // Generate mock trend data for development
      const generateMockTrendData = (days: number): TrendDataPoint[] => {
        const data: TrendDataPoint[] = [];
        const today = new Date();

        let baseValue = 0.7 + Math.random() * 0.1;

        for (let i = days; i >= 0; i--) {
          const date = new Date(today);
          date.setDate(date.getDate() - i);

          // Add some randomness to the data
          const randomChange = (Math.random() - 0.5) * 0.05;
          baseValue = Math.max(0, Math.min(1, baseValue + randomChange));

          data.push({
            date: date.toISOString().split('T')[0],
            value: baseValue,
            confidence: 0.8 + (Math.random() * 0.15)
          });
        }

        return data;
      };

      const generateMockMentionData = (days: number): TrendDataPoint[] => {
        const data: TrendDataPoint[] = [];
        const today = new Date();

        let baseMentions = 100 + Math.floor(Math.random() * 50);

        for (let i = days; i >= 0; i--) {
          const date = new Date(today);
          date.setDate(date.getDate() - i);

          // Add some randomness to the data
          const mentionChange = Math.floor((Math.random() - 0.5) * 20);
          baseMentions = Math.max(50, baseMentions + mentionChange);

          data.push({
            date: date.toISOString().split('T')[0],
            value: baseMentions,
            confidence: 0.9
          });
        }

        return data;
      };

      const generateMockAspectData = (days: number): AspectTrendDataPoint[] => {
        const data: AspectTrendDataPoint[] = [];
        const today = new Date();
        const aspects = ['quality', 'price', 'design', 'performance', 'support'];

        for (let i = days; i >= 0; i--) {
          const date = new Date(today);
          date.setDate(date.getDate() - i);

          const aspectData: { [key: string]: { count: number; sentiment: number } } = {};

          aspects.forEach(aspect => {
            aspectData[aspect] = {
              count: 10 + Math.floor(Math.random() * 40),
              sentiment: 0.4 + Math.random() * 0.5
            };
          });

          data.push({
            date: date.toISOString().split('T')[0],
            aspects: aspectData
          });
        }

        return data;
      };

      const days = getPeriodDays(period);

      // Mock response
      return {
        product_id: productId,
        period: period,
        sentiment: {
          product_id: productId,
          trend_type: 'sentiment',
          period: period,
          data_points: generateMockTrendData(days)
        },
        mentions: {
          product_id: productId,
          trend_type: 'mentions',
          period: period,
          data_points: generateMockMentionData(days)
        },
        aspects: {
          product_id: productId,
          trend_type: 'aspects',
          period: period,
          data_points: generateMockAspectData(days)
        }
      };
    }
  };

  // Helper function to get number of days from period
  const getPeriodDays = (period: string): number => {
    switch (period) {
      case 'day': return 1;
      case 'week': return 7;
      case 'month': return 30;
      case 'quarter': return 90;
      case 'year': return 365;
      default: return 30;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <LoadingSpinner size="large" text="Loading trend data..." />
      </div>
    );
  }

  // If there's an error but we have a product ID, show error message
  if (error && productId) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <FiAlertCircle className="mr-2" />
            {error}
          </p>
        </div>
        <Link to="/search" className="mt-4 inline-flex items-center text-blue-600 hover:text-blue-800">
          <FiSearch className="mr-1" />
          Browse Other Products
        </Link>
      </div>
    );
  }

  // If no product is selected, show a default trends view
  if (!productId) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"
        >
          {/* Hero Section */}
          <div className="relative mb-8 bg-gradient-primary rounded-2xl overflow-hidden shadow-lg">
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-10 left-10 w-64 h-64 rounded-full bg-secondary/30 blur-3xl"></div>
              <div className="absolute bottom-10 right-10 w-80 h-80 rounded-full bg-accent/20 blur-3xl"></div>
              <div className="absolute top-40 right-20 w-40 h-40 rounded-full bg-primary/20 blur-3xl"></div>
            </div>

            <div className="relative z-10 px-6 py-12 sm:px-12 sm:py-16">
              <div className="max-w-3xl">
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="text-3xl sm:text-4xl font-bold text-white font-display"
                >
                  Product Trend Analysis
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="mt-4 text-lg text-white/90"
                >
                  Discover trending products and analyze sentiment across multiple platforms
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="mt-8"
                >
                  <Link
                    to="/search"
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-primary bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <FiSearch className="mr-2" />
                    Search Products
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>

          {/* Time range filter */}
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-gray-100 p-5 mb-8">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center text-gray-700">
                <FiCalendar className="text-primary mr-2" size={18} />
                <span className="font-medium">Time Period:</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {['day', 'week', 'month', 'quarter', 'year'].map((p) => (
                  <button
                    key={p}
                    onClick={() => setPeriod(p)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      period === p
                        ? 'bg-primary/10 text-primary shadow-sm'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {p === 'day' ? 'Day' :
                     p === 'week' ? 'Week' :
                     p === 'month' ? 'Month' :
                     p === 'quarter' ? 'Quarter' : 'Year'}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Trending Products Section */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div className="bg-gradient-to-r from-primary-dark to-primary p-4 text-white">
              <h2 className="text-xl font-semibold">Trending Products</h2>
              <p className="text-sm text-white/80 mt-1">
                Select a product to view detailed trend analysis
              </p>
            </div>

            <div className="p-6">
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <LoadingSpinner size="medium" text="Loading trending products..." />
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* We'll show some trending products here */}
                  {[1, 2, 3, 4, 5, 6].map((id) => (
                    <Link
                      key={id}
                      to={`/trends/${id}`}
                      className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <h3 className="font-medium text-gray-900">Sample Product {id}</h3>
                      <div className="flex justify-between text-sm text-gray-500 mt-2">
                        <span>Category: Electronics</span>
                        <span className="text-green-600">85% positive</span>
                      </div>
                      <div className="mt-3 text-xs text-gray-500">
                        Click to view trend analysis
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* External Data Sources Section */}
          <div className="mb-10">
            <h2 className="text-2xl font-display font-semibold text-gray-900 mb-6 flex items-center">
              <FiGlobe className="mr-3 text-primary" />
              External Data Sources
              <span className="ml-3 text-sm px-2 py-1 bg-primary/10 text-primary rounded-full">
                Beta
              </span>
            </h2>

            {/* Data Source Selector */}
            <DataSourceSelector
              selectedSource={dataSource}
              onSourceChange={setDataSource}
              className="mb-6"
            />

            {/* Data Source Content */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
              <div className="bg-gradient-to-r from-primary-dark to-primary p-4 text-white">
                <h3 className="text-lg font-semibold flex items-center">
                  {dataSource === 'reddit' ? (
                    <>
                      <FiMessageCircle className="mr-2" />
                      Reddit Trend Analysis
                    </>
                  ) : dataSource === 'tiktok' ? (
                    <>
                      <FiVideo className="mr-2" />
                      TikTok Trend Analysis
                    </>
                  ) : dataSource === 'google' ? (
                    <>
                      <FiSearch className="mr-2" />
                      Google Trend Analysis
                    </>
                  ) : dataSource === 'amazon' ? (
                    <>
                      <FiShoppingBag className="mr-2" />
                      Amazon Trend Analysis
                    </>
                  ) : (
                    <>
                      <FiGlobe className="mr-2" />
                      All Sources Trend Analysis
                    </>
                  )}
                </h3>
                <p className="text-sm text-white/80 mt-1">
                  {dataSource === 'reddit'
                    ? 'Discover trending products and discussions from Reddit'
                    : dataSource === 'all'
                    ? 'Combined trend analysis from all available data sources'
                    : `${dataSource.charAt(0).toUpperCase() + dataSource.slice(1)} integration coming soon`}
                </p>
              </div>

              {redditLoading ? (
                <div className="flex justify-center items-center py-12">
                  <LoadingSpinner size="medium" text="Loading trend data..." />
                </div>
              ) : (
                <div className="p-6">
                  {dataSource !== 'reddit' && dataSource !== 'all' ? (
                    <div className="bg-blue-50 text-blue-700 p-4 rounded-lg mb-6">
                      <p className="font-medium">
                        {dataSource.charAt(0).toUpperCase() + dataSource.slice(1)} integration is coming soon.
                        Currently showing Reddit data as a preview.
                      </p>
                    </div>
                  ) : null}

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Reddit Trending Products */}
                    <RedditTrendPanel
                      category="electronics"
                      initialTimeframe={period === 'day' ? 'day' : period === 'week' ? 'week' : 'month'}
                      limit={5}
                      showSentiment={true}
                    />

                    {/* Reddit Trending Discussions */}
                    <RedditTrendPanel
                      category="electronics"
                      initialTimeframe={period === 'day' ? 'day' : period === 'week' ? 'week' : 'month'}
                      limit={5}
                      showSentiment={false}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  // If we have a product but no trend data yet, show loading
  if (!product || !trendData) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <LoadingSpinner size="large" text="Loading trend data..." />
      </div>
    );
  }

  // Calculate sentiment trend direction and percentage change
  const calculateSentimentTrend = () => {
    const dataPoints = trendData.sentiment.data_points;
    if (dataPoints.length < 2) return { direction: 'neutral', change: 0 };

    const firstScore = dataPoints[0].value;
    const lastScore = dataPoints[dataPoints.length - 1].value;
    const change = ((lastScore - firstScore) / firstScore) * 100;

    return {
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral',
      change: Math.abs(change)
    };
  };

  // Calculate mention trend direction and percentage change
  const calculateMentionTrend = () => {
    const dataPoints = trendData.mentions.data_points;
    if (dataPoints.length < 2) return { direction: 'neutral', change: 0 };

    const firstCount = dataPoints[0].value;
    const lastCount = dataPoints[dataPoints.length - 1].value;
    const change = ((lastCount - firstCount) / firstCount) * 100;

    return {
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral',
      change: Math.abs(change)
    };
  };

  // Get average sentiment score
  const getAverageSentiment = () => {
    const dataPoints = trendData.sentiment.data_points;
    if (dataPoints.length === 0) return 0;

    const sum = dataPoints.reduce((total, point) => total + point.value, 0);
    return sum / dataPoints.length;
  };

  // Get total mentions
  const getTotalMentions = () => {
    return trendData.mentions.data_points.reduce((total, point) => total + point.value, 0);
  };

  // Calculate average mentions per day (not currently used but kept for future reference)
  /*
  const getAverageDailyMentions = () => {
    const dataPoints = trendData.mentions.data_points;
    if (dataPoints.length === 0) return 0;

    return getTotalMentions() / dataPoints.length;
  };
  */

  // Get top aspects by mention count
  const getTopAspects = () => {
    const aspectCounts: Record<string, { count: number, sentiment: number }> = {};

    trendData.aspects.data_points.forEach(point => {
      Object.entries(point.aspects).forEach(([aspect, data]) => {
        if (!aspectCounts[aspect]) {
          aspectCounts[aspect] = { count: 0, sentiment: 0 };
        }
        aspectCounts[aspect].count += data.count;
        aspectCounts[aspect].sentiment += data.sentiment;
      });
    });

    // Calculate average sentiment for each aspect
    Object.keys(aspectCounts).forEach(aspect => {
      const dataPoints = trendData.aspects.data_points.filter(point => point.aspects[aspect]).length;
      if (dataPoints > 0) {
        aspectCounts[aspect].sentiment /= dataPoints;
      }
    });

    // Sort by count and return top aspects
    return Object.entries(aspectCounts)
      .sort((a, b) => b[1].count - a[1].count)
      .map(([aspect, data]) => ({
        name: aspect,
        count: data.count,
        sentiment: data.sentiment
      }));
  };

  const sentimentTrend = calculateSentimentTrend();
  const mentionTrend = calculateMentionTrend();
  const topAspects = getTopAspects();



  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"
      >
        {/* Hero Section */}
        <div className="relative mb-8 bg-gradient-primary rounded-2xl overflow-hidden shadow-lg">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 w-64 h-64 rounded-full bg-secondary/30 blur-3xl"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 rounded-full bg-accent/20 blur-3xl"></div>
          </div>

          <div className="relative z-10 px-6 py-8 sm:px-12 sm:py-10 text-white">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-3xl md:text-4xl font-display font-bold text-white mb-2">{product.name}</h1>
                <p className="text-white/80 text-lg flex items-center">
                  <span className="font-medium">{product.brand}</span>
                  <span className="mx-2 text-white/40">•</span>
                  <span>{product.category}</span>
                </p>
              </div>

              <div className="mt-6 md:mt-0">
                <div className={`inline-flex items-center px-4 py-2 rounded-xl backdrop-blur-sm ${
                  sentimentTrend.direction === 'up'
                    ? 'bg-green-500/20 text-white border border-green-500/30'
                    : sentimentTrend.direction === 'down'
                    ? 'bg-red-500/20 text-white border border-red-500/30'
                    : 'bg-white/20 text-white border border-white/30'
                }`}>
                  {sentimentTrend.direction === 'up' ? (
                    <FiTrendingUp className="mr-2" size={20} />
                  ) : sentimentTrend.direction === 'down' ? (
                    <FiTrendingDown className="mr-2" size={20} />
                  ) : null}
                  <span className="font-medium">{sentimentTrend.change.toFixed(1)}% {sentimentTrend.direction === 'up' ? 'increase' : sentimentTrend.direction === 'down' ? 'decrease' : 'change'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Time range filter */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-gray-100 p-5 mb-8">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center text-gray-700">
              <FiCalendar className="text-primary mr-2" size={18} />
              <span className="font-medium">Time Period:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {['day', 'week', 'month', 'quarter', 'year'].map((p) => (
                <button
                  key={p}
                  onClick={() => setPeriod(p)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    period === p
                      ? 'bg-primary/10 text-primary shadow-sm'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {p === 'day' ? 'Day' :
                   p === 'week' ? 'Week' :
                   p === 'month' ? 'Month' :
                   p === 'quarter' ? 'Quarter' : 'Year'}
                </button>
              ))}
            </div>

            <div className="ml-auto">
              <button
                onClick={() => setShowSearch(!showSearch)}
                className="flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <FiSearch size={16} />
                Search Products
              </button>
            </div>
          </div>

          {showSearch && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search for a product..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/30"
                />
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
          {/* Enhanced Sentiment Score Card */}
          <motion.div
            className="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300 overflow-hidden relative"
            whileHover={{
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
            }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            {/* Background decoration */}
            <div className="absolute -right-10 -top-10 w-40 h-40 bg-primary/5 rounded-full blur-2xl"></div>

            <div className="flex items-start justify-between mb-4 relative z-10">
              <h2 className="text-lg font-display font-semibold text-gray-900">Average Sentiment</h2>
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
                <FiTrendingUp size={20} />
              </div>
            </div>

            <div className="flex items-end justify-between mb-6 relative z-10">
              <motion.div
                className="text-4xl font-bold text-gray-900"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {(getAverageSentiment() * 100).toFixed(1)}%
              </motion.div>
              <motion.div
                className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  sentimentTrend.direction === 'up' ? 'bg-green-100 text-green-700' :
                  sentimentTrend.direction === 'down' ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-700'
                }`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                {sentimentTrend.direction === 'up' ? (
                  <FiTrendingUp className="mr-1.5" size={14} />
                ) : sentimentTrend.direction === 'down' ? (
                  <FiTrendingDown className="mr-1.5" size={14} />
                ) : null}
                <span>{sentimentTrend.change.toFixed(1)}%</span>
              </motion.div>
            </div>

            <div className="h-40 w-full relative z-10">
              {/* Enhanced line chart visualization */}
              <LineChart
                data={trendData.sentiment.data_points}
                height={160}
                lineColor={sentimentTrend.direction === 'up' ? '#22c55e' : sentimentTrend.direction === 'down' ? '#ef4444' : '#3b82f6'}
                areaColor={sentimentTrend.direction === 'up' ? 'rgba(34, 197, 94, 0.1)' : sentimentTrend.direction === 'down' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(59, 130, 246, 0.1)'}
                showConfidence={true}
                formatValue={(value) => `${(value * 100).toFixed(1)}%`}
                formatDate={(date) => {
                  const d = new Date(date);
                  return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                }}
              />
            </div>
          </motion.div>

          {/* Enhanced Mentions Card */}
          <motion.div
            className="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300 overflow-hidden relative"
            whileHover={{
              y: -5,
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
            }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            {/* Background decoration */}
            <div className="absolute -left-10 -bottom-10 w-40 h-40 bg-secondary/5 rounded-full blur-2xl"></div>

            <div className="flex items-start justify-between mb-4 relative z-10">
              <h2 className="text-lg font-display font-semibold text-gray-900">Total Mentions</h2>
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-secondary/10 text-secondary">
                <FiMessageCircle size={20} />
              </div>
            </div>

            <div className="flex items-end justify-between mb-6 relative z-10">
              <motion.div
                className="text-4xl font-bold text-gray-900"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {getTotalMentions().toLocaleString()}
              </motion.div>
              <motion.div
                className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  mentionTrend.direction === 'up' ? 'bg-green-100 text-green-700' :
                  mentionTrend.direction === 'down' ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-700'
                }`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                {mentionTrend.direction === 'up' ? (
                  <FiTrendingUp className="mr-1.5" size={14} />
                ) : mentionTrend.direction === 'down' ? (
                  <FiTrendingDown className="mr-1.5" size={14} />
                ) : null}
                <span>{mentionTrend.change.toFixed(1)}%</span>
              </motion.div>
            </div>

            <div className="h-40 w-full relative z-10">
              {/* Enhanced bar chart visualization */}
              <BarChart
                data={trendData.mentions.data_points.map(point => ({
                  label: new Date(point.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
                  value: point.value,
                  color: mentionTrend.direction === 'up' ? '#8b5cf6' : mentionTrend.direction === 'down' ? '#a78bfa' : '#8b5cf6'
                }))}
                height={160}
                showValues={false}
                showLabels={true}
                formatValue={(value) => value.toLocaleString()}
                barRadius={4}
                defaultColor="#8b5cf6"
              />
            </div>

            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="text-sm text-gray-600">
                Across {trendData.mentions.data_points.length} days of data
              </div>
            </div>
          </motion.div>

          {/* Top Aspects Card */}
          <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-shadow duration-300">
            <div className="flex items-start justify-between mb-4">
              <h2 className="text-lg font-display font-semibold text-gray-900">Top Aspects</h2>
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-accent/10 text-accent">
                <FiFilter size={20} />
              </div>
            </div>

            <div className="space-y-4">
              {topAspects.slice(0, 4).map((aspect) => {
                // Calculate sentiment color
                const sentimentColor = aspect.sentiment > 0.7 ? 'text-green-600' :
                                      aspect.sentiment > 0.4 ? 'text-yellow-600' : 'text-red-600';

                return (
                  <div key={aspect.name}>
                    <div className="flex justify-between items-center mb-1.5">
                      <span className="text-sm font-medium text-gray-800 capitalize">
                        {aspect.name}
                      </span>
                      <span className={`text-sm font-medium ${sentimentColor}`}>
                        {(aspect.sentiment * 100).toFixed(0)}%
                      </span>
                    </div>
                    <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className={`h-full ${
                          aspect.sentiment > 0.7 ? 'bg-green-500' :
                          aspect.sentiment > 0.4 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${aspect.sentiment * 100}%` }}
                      ></div>
                    </div>
                    <div className="mt-1 text-xs text-gray-500">
                      {aspect.count} mentions
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="mt-4 pt-4 border-t border-gray-100">
              <button className="text-sm text-primary font-medium hover:text-primary-dark transition-colors">
                View all aspects →
              </button>
            </div>
          </div>
        </div>

        {/* Trend Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-10">
          {/* Sentiment Trend Chart */}
          <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-shadow duration-300">
            <div className="flex items-start justify-between mb-6">
              <div>
                <h2 className="text-lg font-display font-semibold text-gray-900">Sentiment Trend</h2>
                <p className="text-sm text-gray-500 mt-1">How sentiment has changed over time</p>
              </div>
              <button className="flex items-center gap-2 px-3 py-1.5 rounded-lg text-xs font-medium bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 transition-colors">
                <FiFilter size={14} />
                Filter
              </button>
            </div>

            <div className="h-64 bg-gray-50 rounded-xl border border-gray-100 flex items-center justify-center relative overflow-hidden">
              {/* Decorative gradient background */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary via-transparent to-transparent"></div>
              </div>

              {/* Placeholder for actual chart */}
              <div className="relative z-10 flex flex-col items-center justify-center">
                <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <FiTrendingUp className="text-primary" size={24} />
                </div>
                <p className="text-gray-500 text-sm">Interactive sentiment trend chart would appear here</p>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-100 flex justify-between items-center">
              <div className="text-sm text-gray-600">
                {trendData.sentiment.data_points.length} data points
              </div>
              <button className="text-sm text-primary font-medium hover:text-primary-dark transition-colors">
                View detailed report →
              </button>
            </div>
          </div>

          {/* Mentions Trend Chart */}
          <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-shadow duration-300">
            <div className="flex items-start justify-between mb-6">
              <div>
                <h2 className="text-lg font-display font-semibold text-gray-900">Mentions Trend</h2>
                <p className="text-sm text-gray-500 mt-1">How mention volume has changed over time</p>
              </div>
              <button className="flex items-center gap-2 px-3 py-1.5 rounded-lg text-xs font-medium bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 transition-colors">
                <FiFilter size={14} />
                Filter
              </button>
            </div>

            <div className="h-64 bg-gray-50 rounded-xl border border-gray-100 flex items-center justify-center relative overflow-hidden">
              {/* Decorative gradient background */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-secondary via-transparent to-transparent"></div>
              </div>

              {/* Placeholder for actual chart */}
              <div className="relative z-10 flex flex-col items-center justify-center">
                <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <FiBarChart2 className="text-secondary" size={24} />
                </div>
                <p className="text-gray-500 text-sm">Interactive mentions trend chart would appear here</p>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-100 flex justify-between items-center">
              <div className="text-sm text-gray-600">
                {trendData.mentions.data_points.length} data points
              </div>
              <button className="text-sm text-secondary font-medium hover:text-secondary-dark transition-colors">
                View detailed report →
              </button>
            </div>
          </div>
        </div>

        {/* External Data Sources Section */}
        <div className="mb-10">
          <h2 className="text-2xl font-display font-semibold text-gray-900 mb-6 flex items-center">
            <FiGlobe className="mr-3 text-primary" />
            External Data Sources
            <span className="ml-3 text-sm px-2 py-1 bg-primary/10 text-primary rounded-full">
              Beta
            </span>
          </h2>

          {/* Data Source Selector */}
          <DataSourceSelector
            selectedSource={dataSource}
            onSourceChange={setDataSource}
            className="mb-6"
          />

          {/* Data Source Content */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
            <div className="bg-gradient-to-r from-primary-dark to-primary p-4 text-white">
              <h3 className="text-lg font-semibold flex items-center">
                {dataSource === 'reddit' ? (
                  <>
                    <FiMessageCircle className="mr-2" />
                    Reddit Trend Analysis
                  </>
                ) : dataSource === 'tiktok' ? (
                  <>
                    <FiVideo className="mr-2" />
                    TikTok Trend Analysis
                  </>
                ) : dataSource === 'google' ? (
                  <>
                    <FiSearch className="mr-2" />
                    Google Trend Analysis
                  </>
                ) : dataSource === 'amazon' ? (
                  <>
                    <FiShoppingBag className="mr-2" />
                    Amazon Trend Analysis
                  </>
                ) : (
                  <>
                    <FiGlobe className="mr-2" />
                    All Sources Trend Analysis
                  </>
                )}
              </h3>
              <p className="text-sm text-white/80 mt-1">
                {dataSource === 'reddit'
                  ? 'Discover trending products and discussions from Reddit'
                  : dataSource === 'all'
                  ? 'Combined trend analysis from all available data sources'
                  : `${dataSource.charAt(0).toUpperCase() + dataSource.slice(1)} integration coming soon`}
              </p>
            </div>

            {redditLoading ? (
              <div className="flex justify-center items-center py-12">
                <LoadingSpinner size="medium" text="Loading trend data..." />
              </div>
            ) : (
              <div className="p-6">
                {dataSource !== 'reddit' && dataSource !== 'all' ? (
                  <div className="bg-blue-50 text-blue-700 p-4 rounded-lg mb-6">
                    <p className="font-medium">
                      {dataSource.charAt(0).toUpperCase() + dataSource.slice(1)} integration is coming soon.
                      Currently showing Reddit data as a preview.
                    </p>
                  </div>
                ) : null}

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Reddit Trending Products */}
                  <RedditTrendPanel
                    category={product.category}
                    initialTimeframe={period === 'day' ? 'day' : period === 'week' ? 'week' : 'month'}
                    limit={5}
                    showSentiment={true}
                  />

                  {/* Reddit Trending Discussions */}
                  <RedditTrendPanel
                    category={product.category}
                    initialTimeframe={period === 'day' ? 'day' : period === 'week' ? 'week' : 'month'}
                    limit={5}
                    showSentiment={false}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Platform Comparison */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="bg-gradient-to-r from-secondary-dark to-secondary p-4 text-white">
              <h3 className="text-lg font-semibold flex items-center">
                <FiBarChart2 className="mr-2" />
                Cross-Platform Trend Comparison
              </h3>
              <p className="text-sm text-white/80 mt-1">
                Compare trends across different platforms
              </p>
            </div>

            <div className="p-6">
              <div className="bg-blue-50 text-blue-700 p-4 rounded-lg mb-6">
                <p className="font-medium">
                  Cross-platform comparison is coming soon. This feature will allow you to compare trends
                  across Reddit, TikTok, Google, and Amazon.
                </p>
              </div>

              <div className="h-64 bg-gray-50 rounded-xl border border-gray-100 flex items-center justify-center">
                <div className="text-center p-6">
                  <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-3">
                    <FiBarChart2 className="text-secondary" size={24} />
                  </div>
                  <h4 className="text-gray-700 font-medium mb-2">Cross-Platform Comparison</h4>
                  <p className="text-gray-500 text-sm">
                    This feature will allow you to compare sentiment and mentions across different platforms.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Top Keywords */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-shadow duration-300 mb-10">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-lg font-display font-semibold text-gray-900">Top Keywords</h2>
              <p className="text-sm text-gray-500 mt-1">Most frequently mentioned terms in reviews</p>
            </div>
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-accent/10 text-accent">
              <FiSearch size={20} />
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            {/* Generate some mock keywords with varying sizes based on frequency */}
            {['quality', 'design', 'performance', 'battery life', 'price', 'comfort', 'durability',
              'features', 'sound quality', 'value', 'customer service', 'reliability', 'easy to use',
              'stylish', 'lightweight', 'premium', 'innovative'].map((keyword, i) => {
              // Calculate a size factor based on "importance"
              const importance = 1 - (i / 20); // Will range from 1 to ~0.15
              const fontSize = 0.8 + (importance * 0.7); // Will range from ~0.9rem to ~1.5rem
              const opacity = 0.7 + (importance * 0.3); // Will range from ~0.75 to 1

              return (
                <div
                  key={keyword}
                  className={`px-4 py-2 rounded-full text-sm font-medium ${
                    i % 3 === 0 ? 'bg-primary/10 text-primary' :
                    i % 3 === 1 ? 'bg-secondary/10 text-secondary' :
                    'bg-accent/10 text-accent'
                  } hover:shadow-sm transition-shadow cursor-pointer`}
                  style={{
                    fontSize: `${fontSize}rem`,
                    opacity
                  }}
                >
                  {keyword}
                </div>
              );
            })}
          </div>

          <div className="mt-6 pt-4 border-t border-gray-100 flex justify-between items-center">
            <div className="text-sm text-gray-600">
              Based on {getTotalMentions().toLocaleString()} total mentions
            </div>
            <button className="text-sm text-primary font-medium hover:text-primary-dark transition-colors">
              Analyze keywords →
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default TrendsPage;
