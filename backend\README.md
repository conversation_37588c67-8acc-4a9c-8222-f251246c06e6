# ProductWhisper Backend

This is the backend API for ProductWhisper, a platform that helps users discover products through sentiment analysis of reviews and discussions from various sources.

## Technology Stack

- **Node.js + Express**: Main API server with TypeScript
- **PostgreSQL**: Database for structured data
- **Redis**: Caching layer
- **Python Flask**: Sentiment analysis microservice
- **Docker**: Containerization for all services

## Project Structure

```
backend/
├── src/
│   ├── config/       # Configuration files
│   ├── controllers/  # Request handlers
│   ├── middleware/   # Express middleware
│   ├── models/       # Database models and types
│   ├── routes/       # API routes
│   ├── services/     # Business logic
│   ├── utils/        # Utility functions
│   └── scripts/      # Database scripts
├── migrations/       # Database migrations
├── python-nlp-service/ # Python sentiment analysis service
└── .env.example      # Environment variables template
```

## Getting Started

### Prerequisites

- Node.js (v16+)
- Docker and Docker Compose
- PostgreSQL (if running locally)
- Redis (if running locally)
- Python (3.9+) (if running the sentiment analysis service locally)

### Installation

#### Using Docker (Recommended)

1. Clone the repository
2. Copy the `.env.example` file to `.env` and update the values as needed
3. Start the services using Docker Compose:

```bash
docker-compose up
```

This will start all the required services:
- API server on port 8000
- Sentiment analysis service on port 5000
- PostgreSQL on port 5432
- Redis on port 6379
- pgAdmin on port 5050

#### Manual Installation

1. Clone the repository
2. Install Node.js dependencies:

```bash
cd backend
npm install
```

3. Install Python dependencies:

```bash
cd python-nlp-service
pip install -r requirements.txt
```

4. Create a PostgreSQL database:

```bash
createdb productwhisper
```

5. Set up environment variables:
   - Copy `.env.example` to `.env`
   - Update the values as needed

### Database Setup

Initialize the database with the schema and sample data:

```bash
cd backend
npm run db:init
```

### Running the Application

1. Start the Node.js server:

```bash
cd backend
npm run dev
```

2. Start the Python sentiment analysis service:

```bash
cd backend/python-nlp-service
python app.py
```

## API Documentation

Once the server is running, you can access the API documentation at:

```
http://localhost:8000/api-docs
```

## Key Features

### Sentiment Analysis

The sentiment analysis service provides the following endpoints:

- `/analyze`: Analyze sentiment of a single text
- `/analyze-aspects`: Extract aspects and their sentiment from text
- `/analyze-batch`: Analyze sentiment of multiple texts
- `/compare`: Compare sentiment between two texts

### Reddit Integration

The backend integrates with the Reddit API to fetch product discussions and analyze sentiment. Key features include:

- OAuth2 authentication with Reddit API
- Searching for product mentions across relevant subreddits
- Extracting product information from posts
- Caching responses to avoid rate limiting

### Trend Analysis

The trend analysis service provides insights into product sentiment and mentions over time:

- Sentiment trends
- Mention trends
- Aspect-based sentiment analysis

## Development

### Running Tests

```bash
# Run backend tests
cd backend
npm test

# Run sentiment analysis service tests
cd backend/python-nlp-service
pytest
```

### Linting

```bash
cd backend
npm run lint
```

### Building for Production

```bash
cd backend
npm run build
```

## Deployment

The application is containerized using Docker and can be deployed to any container orchestration platform like Kubernetes or Docker Swarm.

For a simple deployment, you can use Docker Compose:

```bash
docker-compose -f docker-compose.yml up -d
```

## License

This project is licensed under the ISC License.
