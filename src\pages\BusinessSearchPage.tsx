import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion, useInView } from 'framer-motion';
import { apiService } from '../services/api';
import { Loading<PERSON>pinner, ApiErrorFallback, Card } from '../components/common';
import { Search, MapPin, Star, Filter, RefreshCw, Building, TrendingUp } from 'lucide-react';
import {
  AnimatedCircles,
  ScrollFadeIn,
  GradientButton,
  Input
} from '../components/ui';

interface BusinessResult {
  source: string;
  source_id: string;
  name: string;
  address: string;
  rating?: number;
  user_ratings_total?: number;
  types?: string[];
  photo_reference?: string;
  business_status?: string;
  price_level?: number;
}

const BusinessSearchPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  const [query, setQuery] = useState<string>(searchParams.get('query') || '');
  const [source, setSource] = useState<string>(searchParams.get('source') || 'all');
  const [results, setResults] = useState<BusinessResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch results when query or source changes
  useEffect(() => {
    const searchQuery = searchParams.get('query');
    if (searchQuery) {
      setQuery(searchQuery);
      performSearch(searchQuery, searchParams.get('source') || 'all');
    }
  }, [searchParams]);

  // Perform search
  const performSearch = async (searchQuery: string, searchSource: string) => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.searchBusinesses(searchQuery, searchSource, 10);
      setResults(response.results || []);
    } catch (err) {
      console.error('Error searching businesses:', err);
      setError('Failed to search businesses. Please try again.');
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;

    // Update URL params
    setSearchParams({ query, source });
  };

  // Handle source change
  const handleSourceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSource(e.target.value);
    if (query.trim()) {
      setSearchParams({ query, source: e.target.value });
    }
  };

  // Navigate to business details
  const navigateToBusinessDetails = (business: BusinessResult) => {
    navigate(`/business/${business.source}/${business.source_id}`);
  };

  // References for animations
  const heroRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Check if sections are in view
  const heroInView = useInView(heroRef, { once: false, amount: 0.3 });
  const formInView = useInView(formRef, { once: false, amount: 0.3 });
  const resultsInView = useInView(resultsRef, { once: false, amount: 0.3 });

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section with Premium Gradient */}
      <div
        ref={heroRef}
        className="relative bg-gradient-primary rounded-premium shadow-premium overflow-hidden mb-6"
      >
        {/* Animated background circles */}
        <AnimatedCircles
          variant="secondary"
          count={12}
          maxOpacity={0.3}
          animationStyle="mixed"
          blurAmount={1.5}
          speed="slow"
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
          <ScrollFadeIn
            direction="up"
            className="text-center"
            threshold={0.2}
            duration={0.7}
            distance={30}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex items-center justify-center mb-3">
                <motion.div
                  className="flex items-center justify-center h-14 w-14 rounded-full bg-white/20 text-white mb-3 relative z-10"
                  whileHover={{ scale: 1.05 }}
                >
                  <Building className="h-7 w-7" />
                </motion.div>
              </div>
              <h1 className="text-3xl sm:text-4xl font-bold text-white font-display mb-2">Business Search</h1>
              <p className="mt-2 text-lg text-white/80 leading-relaxed max-w-2xl mx-auto">
                Discover businesses and analyze their reviews with our advanced sentiment analysis
              </p>
            </motion.div>
          </ScrollFadeIn>

          {/* Search Form */}
          <div ref={formRef} className="mt-6 max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={formInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-white/10 backdrop-blur-sm rounded-premium shadow-premium p-4 border border-white/20"
            >
              <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-3">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/70 z-10" size={18} />
                  <Input
                    type="text"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    placeholder="Search for businesses..."
                    className="pl-9 pr-3 py-3 text-white placeholder:text-white/60 bg-white/10 border-white/20 focus-visible:ring-2 focus-visible:ring-white/30"
                  />
                </div>
                <div className="w-full md:w-44">
                  <select
                    value={source}
                    onChange={handleSourceChange}
                    className="w-full px-3 py-3 text-white bg-white/10 border-white/20 rounded-lg focus:ring-2 focus:ring-white/30 focus:border-white/30"
                  >
                    <option value="all">All Sources</option>
                    <option value="google">Google Places</option>
                    <option value="facebook">Facebook</option>
                  </select>
                </div>
                <GradientButton
                  type="submit"
                  variant="secondary"
                  className="md:w-auto py-3 px-6"
                  rightIcon={<Search className="ml-1 h-4 w-4" />}
                >
                  Search
                </GradientButton>
              </form>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div ref={resultsRef} className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-10">
        <ScrollFadeIn
          direction="up"
          className="space-y-5"
          threshold={0.1}
          duration={0.5}
          distance={25}
        >
          {loading ? (
            <div className="flex justify-center items-center py-10">
              <LoadingSpinner size="large" text="Searching businesses..." />
            </div>
          ) : error ? (
            <ApiErrorFallback error={error} retry={() => performSearch(query, source)} />
          ) : results.length > 0 ? (
            <>
              <motion.div
                className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-5"
                initial={{ opacity: 0, y: 20 }}
                animate={resultsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex items-center">
                  <TrendingUp className="text-primary mr-2 h-5 w-5" />
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900 font-display">
                    {results.length} {results.length === 1 ? 'Result' : 'Results'} for "{query}"
                  </h2>
                </div>
                <GradientButton
                  variant="primary"
                  size="sm"
                  onClick={() => performSearch(query, source)}
                  className="flex items-center bg-white/10 text-white hover:bg-white/20 py-2 px-4 self-start sm:self-auto"
                  leftIcon={<RefreshCw className="w-4 h-4" />}
                >
                  Refresh
                </GradientButton>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
                {results.map((business, index) => (
                  <motion.div
                    key={`${business.source}-${business.source_id}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={resultsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.5, delay: index * 0.05 }}
                    whileHover={{ y: -5 }}
                    className="h-full"
                  >
                    <Card
                      onClick={() => navigateToBusinessDetails(business)}
                      className="h-full cursor-pointer rounded-premium shadow-premium hover:shadow-premium-hover transition-all duration-300 border border-transparent hover:border-primary/10 overflow-hidden"
                    >
                      <div className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <h3 className="text-lg font-bold text-gray-900 font-display mb-1 group-hover:text-primary transition-colors truncate">
                              {business.name}
                            </h3>
                            <div className="flex items-center text-sm text-gray-500 mb-2">
                              <MapPin className="w-4 h-4 mr-1 text-primary/70 flex-shrink-0" />
                              <span className="truncate">{business.address}</span>
                            </div>
                          </div>
                          {business.rating && (
                            <div className="flex items-center bg-yellow-50 text-yellow-700 px-2 py-1 rounded-full shadow-sm ml-2 flex-shrink-0">
                              <Star className="w-3.5 h-3.5 text-yellow-500 fill-yellow-500 mr-1" />
                              <span className="font-bold text-sm">{business.rating.toFixed(1)}</span>
                              {business.user_ratings_total && (
                                <span className="text-xs text-gray-500 ml-1">
                                  ({business.user_ratings_total})
                                </span>
                              )}
                            </div>
                          )}
                        </div>

                        {business.types && business.types.length > 0 && (
                          <div className="mt-2 flex flex-wrap gap-1.5">
                            {business.types.slice(0, 3).map((type) => (
                              <span
                                key={type}
                                className="text-xs bg-primary/5 text-primary px-2 py-0.5 rounded-full"
                              >
                                {type.replace(/_/g, ' ')}
                              </span>
                            ))}
                          </div>
                        )}

                        <div className="mt-3 pt-2 border-t border-gray-100 flex justify-between items-center">
                          <span className="text-xs text-gray-500">
                            Source: {business.source === 'google' ? 'Google Places' : 'Facebook'}
                          </span>
                          <span className="text-sm text-primary font-medium group-hover:translate-x-1 transition-transform">
                            View Details →
                          </span>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </>
          ) : query ? (
            <div className="text-center py-12 bg-white rounded-premium shadow-sm border border-gray-100 mt-6">
              <motion.div
                className="text-gray-400 mb-5"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="w-16 h-16 mx-auto mb-3 bg-gray-50 rounded-full flex items-center justify-center">
                  <Filter className="w-8 h-8 text-gray-300" />
                </div>
                <h3 className="text-xl font-bold text-gray-700 font-display">No results found</h3>
              </motion.div>
              <p className="text-gray-500 mb-6 max-w-md mx-auto">
                We couldn't find any businesses matching "{query}". Try adjusting your search terms or source.
              </p>
              <GradientButton variant="primary" onClick={() => setQuery('')} className="bg-white text-primary hover:bg-gray-50 py-2 px-5">
                Clear Search
              </GradientButton>
            </div>
          ) : null}
        </ScrollFadeIn>
      </div>
    </div>
  );
};

export default BusinessSearchPage;








