import React, { useState, useEffect } from 'react';
import { sentimentService } from '../services/sentimentService';
import { apiService } from '../services/api';
import {type Product } from '../types/api';
import type { SentimentComparisonResult, AspectSentiment } from '../types/sentiment';

interface SentimentComparisonProps {
  initialProductIds?: [number, number];
  className?: string;
}

/**
 * Component for comparing sentiment between two products
 */
const SentimentComparison: React.FC<SentimentComparisonProps> = ({
  initialProductIds,
  className = '',
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProductIds, setSelectedProductIds] = useState<[number, number] | null>(initialProductIds || null);
  const [comparisonResult, setComparisonResult] = useState<SentimentComparisonResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch trending products on mount
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const trendingProducts = await apiService.getTrendingProducts(10);
        setProducts(trendingProducts);

        // If no initial products are provided, select the first two
        if (!initialProductIds && trendingProducts.length >= 2) {
          setSelectedProductIds([trendingProducts[0].id, trendingProducts[1].id]);
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      }
    };

    fetchProducts();
  }, [initialProductIds]);

  // Compare products when selection changes
  useEffect(() => {
    if (selectedProductIds) {
      compareProducts(selectedProductIds[0], selectedProductIds[1]);
    }
  }, [selectedProductIds]);

  // Compare products
  const compareProducts = async (productId1: number, productId2: number) => {
    setLoading(true);
    setError(null);

    try {
      const result = await sentimentService.compareProductSentiment(productId1, productId2);
      setComparisonResult(result);
    } catch (err) {
      console.error('Error comparing products:', err);
      setError('Failed to compare products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle product selection
  const handleProductSelect = (index: number, productId: number) => {
    if (!selectedProductIds) {
      return;
    }

    const newSelectedProductIds = [...selectedProductIds] as [number, number];
    newSelectedProductIds[index] = productId;
    setSelectedProductIds(newSelectedProductIds);
  };

  // Get sentiment color based on score
  const getSentimentColor = (score: number): string => {
    if (score >= 0.6) return 'text-green-600';
    if (score >= 0.2) return 'text-green-400';
    if (score >= -0.2) return 'text-gray-500';
    if (score >= -0.6) return 'text-red-400';
    return 'text-red-600';
  };

  // Get sentiment background color based on score
  const getSentimentBgColor = (score: number): string => {
    if (score >= 0.6) return 'bg-green-100';
    if (score >= 0.2) return 'bg-green-50';
    if (score >= -0.2) return 'bg-gray-50';
    if (score >= -0.6) return 'bg-red-50';
    return 'bg-red-100';
  };

  // Get sentiment label based on score
  const getSentimentLabel = (score: number): string => {
    if (score >= 0.6) return 'Very Positive';
    if (score >= 0.2) return 'Positive';
    if (score >= -0.2) return 'Neutral';
    if (score >= -0.6) return 'Negative';
    return 'Very Negative';
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      <h2 className="text-xl font-semibold mb-4">Sentiment Comparison</h2>

      {/* Product Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {[0, 1].map(index => (
          <div key={index} className="border rounded-md p-4">
            <label className="block text-gray-700 mb-2 font-medium">
              {index === 0 ? 'First Product' : 'Second Product'}:
            </label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={selectedProductIds ? selectedProductIds[index] : ''}
              onChange={(e) => handleProductSelect(index, Number(e.target.value))}
            >
              <option value="">Select a product</option>
              {products.map(product => (
                <option key={product.id} value={product.id}>
                  {product.name} ({product.brand})
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          <div className="mt-2">Comparing products...</div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Comparison Results */}
      {comparisonResult && !loading && (
        <div className="mt-6">
          {/* Overall Comparison */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">Overall Sentiment Comparison</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {comparisonResult.products.map((product, index: number) => (
                <div key={index} className="border rounded-lg overflow-hidden">
                  <div className="bg-gray-50 p-3 border-b">
                    <div className="flex items-center">
                      {product.imageUrl && (
                        <img
                          src={product.imageUrl}
                          alt={product.name}
                          className="w-12 h-12 object-cover rounded-md mr-3"
                        />
                      )}
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-600">{product.brand}</div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4">
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className={`p-3 rounded-md ${getSentimentBgColor(product.averageSentiment)}`}>
                        <div className="text-sm text-gray-600">Average Sentiment</div>
                        <div className={`text-lg font-semibold ${getSentimentColor(product.averageSentiment)}`}>
                          {product.averageSentiment.toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {getSentimentLabel(product.averageSentiment)}
                        </div>
                      </div>

                      <div className="p-3 rounded-md bg-gray-50">
                        <div className="text-sm text-gray-600">Reviews Analyzed</div>
                        <div className="text-lg font-semibold">{product.analyzedReviews}</div>
                        <div className="text-xs text-gray-500">
                          of {product.reviewCount} total
                        </div>
                      </div>
                    </div>

                    {/* Top Aspects */}
                    {product.aspects && product.aspects.length > 0 && (
                      <div>
                        <div className="text-sm font-medium mb-2">Top Aspects:</div>
                        <div className="space-y-2">
                          {product.aspects.slice(0, 3).map((aspect: AspectSentiment, i: number) => (
                            <div key={i} className="flex justify-between items-center text-sm">
                              <span>{aspect.aspect}</span>
                              <span className={getSentimentColor(aspect.sentiment)}>
                                {aspect.sentiment.toFixed(2)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Aspect Comparison */}
          {comparisonResult.commonAspects && comparisonResult.commonAspects.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Aspect-by-Aspect Comparison</h3>
              <div className="overflow-hidden border rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Aspect
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {comparisonResult.products[0].name}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {comparisonResult.products[1].name}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Difference
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {comparisonResult.commonAspects.map((aspect, index: number) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {aspect.aspect}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={getSentimentColor(aspect.product1.sentiment)}>
                            {aspect.product1.sentiment.toFixed(2)}
                          </span>
                          <span className="text-gray-500 text-xs ml-1">
                            ({aspect.product1.count} mentions)
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={getSentimentColor(aspect.product2.sentiment)}>
                            {aspect.product2.sentiment.toFixed(2)}
                          </span>
                          <span className="text-gray-500 text-xs ml-1">
                            ({aspect.product2.count} mentions)
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`font-medium ${getSentimentColor(aspect.difference)}`}>
                            {aspect.difference > 0 ? '+' : ''}{aspect.difference.toFixed(2)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SentimentComparison;
