import React from 'react';

interface SearchSkeletonProps {
  count?: number;
}

const SearchSkeleton: React.FC<SearchSkeletonProps> = ({ count = 6 }) => {
  return (
    <div className="animate-pulse">
      {/* Search header skeleton */}
      <div className="mb-6">
        <div className="h-8 bg-gray-200 rounded-md w-1/3 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded-md w-1/2"></div>
      </div>

      {/* Grid of product skeletons */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: count }).map((_, index) => (
          <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            {/* Image skeleton */}
            <div className="w-full h-48 bg-gray-200"></div>
            
            {/* Content skeleton */}
            <div className="p-4">
              {/* Title */}
              <div className="h-6 bg-gray-200 rounded-md w-3/4 mb-2"></div>
              
              {/* Brand & Category */}
              <div className="h-4 bg-gray-200 rounded-md w-1/2 mb-4"></div>
              
              {/* Price */}
              <div className="h-6 bg-gray-200 rounded-md w-1/4 mb-4"></div>
              
              {/* Rating */}
              <div className="flex items-center mb-4">
                <div className="flex space-x-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="w-4 h-4 bg-gray-200 rounded-full"></div>
                  ))}
                </div>
                <div className="h-4 bg-gray-200 rounded-md w-12 ml-2"></div>
              </div>
              
              {/* Sentiment score */}
              <div className="flex justify-between items-center">
                <div className="h-8 bg-gray-200 rounded-md w-1/3"></div>
                <div className="h-8 bg-gray-200 rounded-full w-8"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SearchSkeleton;
