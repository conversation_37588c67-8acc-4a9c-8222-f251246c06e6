/**
 * API Constants for frontend
 */

/**
 * API base URL - Points to the ProductWhisper Backend Repository
 */
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL ||
  (import.meta.env.PROD
    ? 'https://api.productwhisper.com/api'  // Production backend URL
    : 'http://localhost:8000/api'          // Local development backend URL
  );

/**
 * API endpoints
 */
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: `${API_BASE_URL}/auth/login`,
    REGISTER: `${API_BASE_URL}/auth/register`,
    REFRESH_TOKEN: `${API_BASE_URL}/auth/refresh`,
    LOGOUT: `${API_BASE_URL}/auth/logout`,
    FORGOT_PASSWORD: `${API_BASE_URL}/auth/forgot-password`,
    RESET_PASSWORD: `${API_BASE_URL}/auth/reset-password`,
    VERIFY_EMAIL: `${API_BASE_URL}/auth/verify-email`,
  },

  // User endpoints
  USER: {
    PROFILE: `${API_BASE_URL}/user/profile`,
    PREFERENCES: `${API_BASE_URL}/user/preferences`,
    FAVORITES: `${API_BASE_URL}/user/favorites`,
  },

  // Product endpoints
  PRODUCT: {
    BASE: `${API_BASE_URL}/products`,
    DETAIL: (id: string | number) => `${API_BASE_URL}/products/${id}`,
    RELATED: (id: string | number) => `${API_BASE_URL}/products/${id}/related`,
    REVIEWS: (id: string | number) => `${API_BASE_URL}/products/${id}/reviews`,
  },

  // Review endpoints
  REVIEW: {
    BASE: `${API_BASE_URL}/reviews`,
    DETAIL: (id: string | number) => `${API_BASE_URL}/reviews/${id}`,
    HELPFUL: (id: string | number) => `${API_BASE_URL}/reviews/${id}/helpful`,
  },

  // Sentiment endpoints
  SENTIMENT: {
    ANALYZE: `${API_BASE_URL}/sentiment/analyze`,
    COMPARE: `${API_BASE_URL}/sentiment/compare`,
  },

  // Trend endpoints
  TREND: {
    BASE: `${API_BASE_URL}/trends`,
    COMPARE: `${API_BASE_URL}/trends/compare`,
    CATEGORIES: `${API_BASE_URL}/trends/categories`,
    BRANDS: `${API_BASE_URL}/trends/brands`,
  },

  // Reddit trend endpoints
  REDDIT_TREND: {
    DISCUSSIONS: `${API_BASE_URL}/reddit-trends/discussions`,
    PRODUCTS: `${API_BASE_URL}/reddit-trends/products`,
  },

  // Comparison endpoints
  COMPARISON: {
    BASE: `${API_BASE_URL}/compare`,
    DETAIL: (id: string | number) => `${API_BASE_URL}/compare/${id}`,
    SAVE: `${API_BASE_URL}/compare/save`,
    USER: `${API_BASE_URL}/compare/user`,
  },

  // Chat endpoints
  CHAT: {
    BASE: `${API_BASE_URL}/chat`,
    FEEDBACK: `${API_BASE_URL}/chat/feedback`,
  },
};

/**
 * API request timeout in milliseconds
 */
export const API_TIMEOUT = 30000;

/**
 * API request headers
 */
export const API_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

export default {
  API_BASE_URL,
  API_ENDPOINTS,
  API_TIMEOUT,
  API_HEADERS,
};
