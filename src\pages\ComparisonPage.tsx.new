import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiBarChart2, FiCheck, FiX, FiAlertCircle, FiPlus, FiSearch } from 'react-icons/fi';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { apiService } from '../services/api';
import { useToast } from '../components/common/Toast';
import { ContentContainer, FlexLayout, GridLayout } from '../components/layout';

interface Product {
  id: number;
  name: string;
  brand: string;
  category: string;
  description: string;
  price: number;
  rating: number;
  reviewCount: number;
  sentimentScore: number;
  features: {
    [key: string]: {
      value: string;
      score?: number;
    };
  };
  pros: string[];
  cons: string[];
}

const ComparisonPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { showToast } = useToast();

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [features, setFeatures] = useState<string[]>([]);

  // Search functionality
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const productIds = searchParams.get('ids')?.split(',').filter(id => id) || [];

        if (productIds.length < 2) {
          setError('Please select at least two products to compare');
          setLoading(false);
          return;
        }

        try {
          // Use the API service to fetch product details
          const productPromises = productIds.map(id =>
            apiService.getProductDetails(parseInt(id))
          );

          const productsData = await Promise.all(productPromises);
          setProducts(productsData);

          // Extract all unique features
          const allFeatures = new Set<string>();
          productsData.forEach(product => {
            if (product.features) {
              Object.keys(product.features).forEach(feature => {
                allFeatures.add(feature);
              });
            }
          });

          setFeatures(Array.from(allFeatures));
        } catch (apiError) {
          console.error('API error:', apiError);
          setError('Failed to fetch product details from the API');

          // Fallback to mock data for development
          const mockProducts: Product[] = [
            {
              id: 1,
              name: "Premium Wireless Headphones",
              brand: "SoundMaster",
              category: "Electronics",
              description: "Experience crystal-clear audio with our premium wireless headphones.",
              price: 249.99,
              rating: 4.7,
              reviewCount: 1243,
              sentimentScore: 0.85,
              features: {
                "Sound Quality": { value: "Excellent", score: 0.92 },
                "Battery Life": { value: "30 hours", score: 0.88 }
              },
              pros: ["Exceptional sound clarity", "Long battery life"],
              cons: ["Expensive", "Occasional Bluetooth connectivity issues"]
            },
            {
              id: 2,
              name: "Ultra Noise Cancelling Headphones",
              brand: "AudioPro",
              category: "Electronics",
              description: "Block out the world with our advanced noise cancelling technology.",
              price: 299.99,
              rating: 4.5,
              reviewCount: 876,
              sentimentScore: 0.82,
              features: {
                "Sound Quality": { value: "Very Good", score: 0.85 },
                "Battery Life": { value: "25 hours", score: 0.80 }
              },
              pros: ["Best-in-class noise cancellation", "Premium build quality"],
              cons: ["Heavy", "Less comfortable for long sessions"]
            }
          ];

          // Filter products based on IDs from URL
          const filteredProducts = mockProducts.filter(product =>
            productIds.includes(product.id.toString())
          );

          setProducts(filteredProducts);

          // Extract all unique features
          const allFeatures = new Set<string>();
          filteredProducts.forEach(product => {
            Object.keys(product.features).forEach(feature => {
              allFeatures.add(feature);
            });
          });

          setFeatures(Array.from(allFeatures));
        }
      } catch (err) {
        setError('Failed to load product data for comparison');
        console.error('Error fetching products for comparison:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [searchParams]);

  // Helper function to get sentiment color
  const getSentimentColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-green-500';
    if (score >= 0.4) return 'text-yellow-500';
    if (score >= 0.2) return 'text-orange-500';
    return 'text-red-500';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <LoadingSpinner size="large" text="Loading comparison data..." />
      </div>
    );
  }

  if (error || products.length < 2) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <FiAlertCircle className="mr-2" />
            {error || "Please select at least two products to compare"}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <ContentContainer maxWidth="full" padding="sm" center>
        <div>
          {/* Content will be added in the next part */}
        </div>
      </ContentContainer>
    </div>
  );
};

export default ComparisonPage;
