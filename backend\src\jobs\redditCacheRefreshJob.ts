import { redditCacheService } from '../services/redditCacheService';
import logger from '../utils/logger';

/**
 * Job to refresh Reddit cache and clean up expired entries
 */
class RedditCacheRefreshJob {
  private isRunning: boolean = false;
  private intervalId: NodeJS.Timeout | null = null;
  private readonly refreshIntervalMs: number;

  constructor(refreshIntervalMinutes: number = 60) {
    this.refreshIntervalMs = refreshIntervalMinutes * 60 * 1000;
  }

  /**
   * Start the job
   */
  start(): void {
    if (this.intervalId) {
      logger.warn('Reddit cache refresh job is already running');
      return;
    }

    logger.info(`Starting Reddit cache refresh job (interval: ${this.refreshIntervalMs / 60000} minutes)`);
    
    // Run immediately on start
    this.run();
    
    // Then schedule regular runs
    this.intervalId = setInterval(() => {
      this.run();
    }, this.refreshIntervalMs);
  }

  /**
   * Stop the job
   */
  stop(): void {
    if (!this.intervalId) {
      logger.warn('Reddit cache refresh job is not running');
      return;
    }

    clearInterval(this.intervalId);
    this.intervalId = null;
    logger.info('Reddit cache refresh job stopped');
  }

  /**
   * Run the job
   */
  private async run(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Reddit cache refresh job is already running, skipping this run');
      return;
    }

    this.isRunning = true;
    logger.info('Running Reddit cache refresh job');

    try {
      // Clean up expired cache entries
      await redditCacheService.cleanupExpiredCache();

      // Refresh trending discussions for different timeframes
      await this.refreshTrendingDiscussions();

      // Refresh trending products for different timeframes
      await this.refreshTrendingProducts();

      logger.info('Reddit cache refresh job completed successfully');
    } catch (error) {
      logger.error('Error in Reddit cache refresh job:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Refresh trending discussions for different timeframes
   */
  private async refreshTrendingDiscussions(): Promise<void> {
    try {
      const categories = ['electronics', 'audio', 'smartphones', 'gaming'];
      const timeframes = ['day', 'week', 'month'];

      // Refresh for each category and timeframe
      for (const category of categories) {
        for (const timeframe of timeframes) {
          logger.info(`Refreshing trending discussions for category: ${category}, timeframe: ${timeframe}`);
          
          try {
            await redditCacheService.getTrendingDiscussions(category, timeframe, 20);
          } catch (error) {
            logger.error(`Error refreshing trending discussions for category: ${category}, timeframe: ${timeframe}:`, error);
            // Continue with other categories/timeframes even if one fails
          }
        }
      }

      // Also refresh for 'all' categories
      for (const timeframe of timeframes) {
        logger.info(`Refreshing trending discussions for all categories, timeframe: ${timeframe}`);
        
        try {
          await redditCacheService.getTrendingDiscussions(undefined, timeframe, 20);
        } catch (error) {
          logger.error(`Error refreshing trending discussions for all categories, timeframe: ${timeframe}:`, error);
        }
      }
    } catch (error) {
      logger.error('Error refreshing trending discussions:', error);
    }
  }

  /**
   * Refresh trending products for different timeframes
   */
  private async refreshTrendingProducts(): Promise<void> {
    try {
      const categories = ['electronics', 'audio', 'smartphones', 'gaming'];
      const timeframes = ['day', 'week', 'month'];

      // Refresh for each category and timeframe
      for (const category of categories) {
        for (const timeframe of timeframes) {
          logger.info(`Refreshing trending products for category: ${category}, timeframe: ${timeframe}`);
          
          try {
            await redditCacheService.getTrendingProducts(category, timeframe, 10);
          } catch (error) {
            logger.error(`Error refreshing trending products for category: ${category}, timeframe: ${timeframe}:`, error);
            // Continue with other categories/timeframes even if one fails
          }
        }
      }

      // Also refresh for 'all' categories
      for (const timeframe of timeframes) {
        logger.info(`Refreshing trending products for all categories, timeframe: ${timeframe}`);
        
        try {
          await redditCacheService.getTrendingProducts(undefined, timeframe, 10);
        } catch (error) {
          logger.error(`Error refreshing trending products for all categories, timeframe: ${timeframe}:`, error);
        }
      }
    } catch (error) {
      logger.error('Error refreshing trending products:', error);
    }
  }
}

// Export singleton instance
export const redditCacheRefreshJob = new RedditCacheRefreshJob();
