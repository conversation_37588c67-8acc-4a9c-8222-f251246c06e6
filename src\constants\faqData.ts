/**
 * FAQ data for ProductWhisper
 * Used for the ChatBox component and FAQ sections
 */
import type { FAQItem } from '../types/faq';

export const faqData: FAQItem[] = [
  {
    id: 'faq-1',
    question: 'How does ProductWhisper work?',
    answer: 'ProductWhisper uses advanced sentiment analysis to analyze product reviews from multiple sources. Our AI processes thousands of reviews to identify what people really like and dislike about products, helping you make informed purchasing decisions.',
    keywords: ['how', 'work', 'sentiment', 'analysis', 'function'],
    category: 'general'
  },
  {
    id: 'faq-2',
    question: 'Is there a free trial available?',
    answer: 'Yes, we offer a 14-day free trial with full access to all features. No credit card is required to start your trial.',
    keywords: ['free', 'trial', 'cost', 'price', 'subscription'],
    category: 'pricing'
  },
  {
    id: 'faq-3',
    question: 'How accurate is the sentiment analysis?',
    answer: 'Our sentiment analysis has been trained on millions of reviews and achieves over 90% accuracy in most product categories. We continuously improve our algorithms to provide the most accurate insights.',
    keywords: ['accurate', 'accuracy', 'sentiment', 'analysis', 'reliable'],
    category: 'technical'
  },
  {
    id: 'faq-4',
    question: 'What sources do you use for product reviews?',
    answer: 'We analyze reviews from major e-commerce platforms, social media, forums, and specialized review sites. This gives us a comprehensive view of product sentiment across the web.',
    keywords: ['sources', 'reviews', 'data', 'platforms', 'websites'],
    category: 'product'
  },
  {
    id: 'faq-5',
    question: 'Can I compare multiple products?',
    answer: 'Yes, our comparison feature allows you to compare up to 5 products side by side. You can see sentiment scores, key features, pros and cons, and more for each product.',
    keywords: ['compare', 'comparison', 'multiple', 'products', 'side by side'],
    category: 'product'
  },
  {
    id: 'faq-6',
    question: 'How often is the data updated?',
    answer: 'We update our product data daily to ensure you have access to the most current sentiment analysis. Trending products are updated even more frequently.',
    keywords: ['update', 'frequency', 'current', 'data', 'recent'],
    category: 'technical'
  },
  {
    id: 'faq-7',
    question: 'Do I need to create an account?',
    answer: 'While you can browse basic product information without an account, creating a free account gives you access to saved products, comparison history, and personalized recommendations.',
    keywords: ['account', 'sign up', 'register', 'login', 'create'],
    category: 'account'
  },
  {
    id: 'faq-8',
    question: 'How do I contact support?',
    answer: 'You can reach our support team <NAME_EMAIL> or by using the contact form on our website. We typically respond within 24 hours.',
    keywords: ['contact', 'support', 'help', 'email', 'reach'],
    category: 'general'
  },
  {
    id: 'faq-9',
    question: 'What categories of products do you cover?',
    answer: 'We currently cover electronics, home appliances, beauty products, fitness equipment, and more. We\'re constantly expanding our coverage to include additional product categories.',
    keywords: ['categories', 'products', 'cover', 'types', 'range'],
    category: 'product'
  },
  {
    id: 'faq-10',
    question: 'Can I get alerts for price drops?',
    answer: 'Yes, you can set up price alerts for products you\'re interested in. We\'ll notify you when there\'s a significant price drop or when the sentiment score changes.',
    keywords: ['alerts', 'price', 'drops', 'notifications', 'changes'],
    category: 'product'
  }
];

// Export default faqData as well for easier imports
export default faqData;

