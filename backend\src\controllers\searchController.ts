import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { ApiError } from '../middleware/errorHandler';
import * as searchService from '../services/searchService';
import logger from '../utils/logger';

/**
 * Search for products
 */
export const searchProducts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { query, filters } = req.body;

    if (!query || typeof query !== 'string') {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Search query is required');
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const searchResults = await searchService.searchProducts(query, filters, page, limit);

    res.status(StatusCodes.OK).json({
      success: true,
      query,
      limit,
      ...searchResults,
    });
  } catch (error) {
    logger.error('Error in searchProducts controller:', error);
    next(error);
  }
};

/**
 * Get trending search terms
 */
export const getTrendingSearches = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;

    const trendingSearches = await searchService.getTrendingSearches(limit);

    res.status(StatusCodes.OK).json({
      success: true,
      count: trendingSearches.length,
      data: trendingSearches,
    });
  } catch (error) {
    logger.error('Error in getTrendingSearches controller:', error);
    next(error);
  }
};
