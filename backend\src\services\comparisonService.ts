import { query } from '../config/database';
import { getCache, setCache } from '../config/redis';
import logger from '../utils/logger';
import * as productService from './productService';
import axios from 'axios';
import { Product, ProductComparison, SentimentComparison } from '../models/types';

// Cache TTL in seconds
const COMPARISON_CACHE_TTL = 3600; // 1 hour

/**
 * Compare products
 */
export const compareProducts = async (productIds: number[]): Promise<ProductComparison> => {
  try {
    // Get products
    const products = await Promise.all(
      productIds.map(id => productService.getProductById(id))
    );

    // Filter out null products
    const validProducts = products.filter(product => product !== null) as Product[];

    if (validProducts.length < 2) {
      throw new Error('At least two valid products are required for comparison');
    }

    // Get common features
    const allFeatures = new Set<string>();

    for (const product of validProducts) {
      if (product.features) {
        for (const feature in product.features) {
          allFeatures.add(feature);
        }
      }
    }

    const commonFeatures = Array.from(allFeatures);

    // Build comparison data
    const comparison: ProductComparison = {
      products: validProducts.map(product => ({
        id: product.id,
        name: product.name,
        brand: product.brand,
        price: product.price,
        rating: product.rating,
        reviewCount: product.reviewCount,
        sentimentScore: product.sentimentScore || 0,
        imageUrl: product.imageUrl,
      })),
      features: commonFeatures.map(feature => ({
        name: feature,
        values: validProducts.map(product => {
          if (product.features && product.features[feature]) {
            return {
              productId: product.id,
              value: product.features[feature].value,
              score: product.features[feature].score,
            };
          }
          return {
            productId: product.id,
            value: 'N/A',
            score: 0,
          };
        }),
      })),
    };

    return comparison;
  } catch (error) {
    logger.error(`Error in compareProducts service for product IDs ${productIds.join(', ')}:`, error);
    throw error;
  }
};

/**
 * Compare product sentiment
 */
export const compareSentiment = async (productIds: number[]): Promise<SentimentComparison> => {
  try {
    // Try to get from cache first
    const cacheKey = `comparison:sentiment:${productIds.sort().join('-')}`;
    const cachedData = await getCache(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Get products
    const products = await Promise.all(
      productIds.map(id => productService.getProductById(id))
    );

    // Filter out null products
    const validProducts = products.filter(product => product !== null) as Product[];

    if (validProducts.length < 2) {
      throw new Error('At least two valid products are required for sentiment comparison');
    }

    // Get aspect sentiment data for each product
    const aspectData = await Promise.all(
      validProducts.map(async product => {
        // Get aspect sentiment from database
        const result = await query(
          `SELECT
            aspect,
            AVG(sentiment_score) as sentiment,
            COUNT(*) as count
          FROM review_aspects
          WHERE product_id = $1
          GROUP BY aspect
          ORDER BY count DESC
          LIMIT 10`,
          [product.id]
        );

        // If no data in database, try to get from sentiment analysis service
        if (result.rows.length === 0) {
          try {
            const response = await axios.post(
              `${process.env.SENTIMENT_API_URL || 'http://localhost:5000'}/analyze-aspects`,
              { product_name: product.name }
            );

            return {
              productId: product.id,
              aspects: response.data.aspects.map((aspect: any) => ({
                aspect: aspect.aspect,
                sentiment: aspect.sentiment,
                count: aspect.count,
              })),
            };
          } catch (error) {
            logger.error(`Error getting aspect data for product "${product.name}":`, error);
            return {
              productId: product.id,
              aspects: [],
            };
          }
        }

        return {
          productId: product.id,
          aspects: result.rows.map((row: any) => ({
            aspect: row.aspect,
            sentiment: parseFloat(row.sentiment),
            count: parseInt(row.count),
          })),
        };
      })
    );

    // Find common aspects
    const aspectsByProduct = new Map<number, Map<string, { sentiment: number; count: number }>>();
    const allAspects = new Set<string>();

    for (const data of aspectData) {
      const aspectMap = new Map<string, { sentiment: number; count: number }>();

      for (const aspect of data.aspects) {
        aspectMap.set(aspect.aspect, {
          sentiment: aspect.sentiment,
          count: aspect.count,
        });

        allAspects.add(aspect.aspect);
      }

      aspectsByProduct.set(data.productId, aspectMap);
    }

    // Build comparison data
    const comparison: SentimentComparison = {
      products: validProducts.map(product => ({
        id: product.id,
        name: product.name,
        brand: product.brand,
        imageUrl: product.imageUrl,
        reviewCount: product.reviewCount,
        overallSentiment: product.sentimentScore || 0,
        analyzedReviews: product.reviewCount,
        averageSentiment: product.sentimentScore || 0,
        aspects: aspectData.find(data => data.productId === product.id)?.aspects || [],
      })),
      commonAspects: Array.from(allAspects).map(aspect => {
        const aspectData: {
          aspect: string;
          product1: { sentiment: number; count: number };
          product2: { sentiment: number; count: number };
          difference: number;
        } = {
          aspect,
          product1: { sentiment: 0, count: 0 },
          product2: { sentiment: 0, count: 0 },
          difference: 0,
        };

        const product1Aspects = aspectsByProduct.get(validProducts[0].id);
        const product2Aspects = aspectsByProduct.get(validProducts[1].id);

        if (product1Aspects && product1Aspects.has(aspect)) {
          aspectData.product1 = product1Aspects.get(aspect)!;
        }

        if (product2Aspects && product2Aspects.has(aspect)) {
          aspectData.product2 = product2Aspects.get(aspect)!;
        }

        aspectData.difference = aspectData.product1.sentiment - aspectData.product2.sentiment;

        return aspectData;
      }).filter(aspect => aspect.product1.count > 0 && aspect.product2.count > 0),
    };

    // Cache the result
    await setCache(cacheKey, JSON.stringify(comparison), COMPARISON_CACHE_TTL);

    return comparison;
  } catch (error) {
    logger.error(`Error in compareSentiment service for product IDs ${productIds.join(', ')}:`, error);
    throw error;
  }
};
