import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from './badge';
import { X } from 'lucide-react';

interface AnimatedFilterBadgeProps {
  filter: string;
  onRemove: (filter: string) => void;
  className?: string;
  icon?: React.ReactNode;
}

const AnimatedFilterBadge: React.FC<AnimatedFilterBadgeProps> = ({
  filter,
  onRemove,
  className = '',
  icon,
}) => {
  return (
    <AnimatePresence mode="popLayout">
      <motion.div
        key={filter}
        initial={{ opacity: 0, scale: 0.8, y: -10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: -10 }}
        transition={{ 
          type: "spring", 
          stiffness: 500, 
          damping: 30 
        }}
        layout
        className="inline-block"
      >
        <Badge
          variant="secondary"
          className={`flex items-center gap-1.5 px-3 py-1.5 bg-primary/10 text-primary hover:bg-primary/20 transition-colors ${className}`}
        >
          {icon && <span className="mr-1">{icon}</span>}
          <span className="truncate max-w-[150px]">{filter}</span>
          <motion.button
            whileHover={{ scale: 1.2, rotate: 90 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => onRemove(filter)}
            className="cursor-pointer hover:text-primary-dark transition-colors"
          >
            <X size={14} />
          </motion.button>
        </Badge>
      </motion.div>
    </AnimatePresence>
  );
};

export default AnimatedFilterBadge;
