import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, FiBarChart2, FiUsers, FiTrendingUp, FiCheckCircle, FiTarget, FiSearch } from 'react-icons/fi';
import AnimatedCircles from '../components/ui/AnimatedCircles';

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
      {/* Background animated elements */}
      <div className="absolute inset-0 opacity-20 pointer-events-none">
        <AnimatedCircles variant="mixed" count={12} speed="slow" blurAmount={1.5} />
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.7 }}
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"
      >
        {/* Hero Section - Modern Premium Design */}
        <div className="relative mb-12 bg-gradient-primary rounded-2xl overflow-hidden shadow-premium">
          {/* Background gradient elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-dark/80 to-primary/90"></div>
            <div className="absolute top-10 left-10 w-64 h-64 rounded-full bg-secondary/30 blur-3xl"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 rounded-full bg-accent/20 blur-3xl"></div>
            <div className="absolute top-1/3 right-1/4 w-40 h-40 rounded-full bg-primary-light/30 blur-2xl"></div>
          </div>

          <div className="relative z-10 px-6 py-16 sm:px-12 sm:py-20 flex flex-col items-center justify-center">
            <motion.div
              className="inline-flex items-center justify-center p-2 bg-white/10 backdrop-blur-sm rounded-full mb-6"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: "spring", stiffness: 300, damping: 20, delay: 0.2 }}
            >
              <div className="bg-white/20 p-3 rounded-full">
                <FiTarget className="w-6 h-6 text-white" />
              </div>
            </motion.div>

            <motion.h1
              className="text-4xl md:text-6xl font-display font-bold text-white mb-6 text-center"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.3 }}
            >
              About ProductWhisper
            </motion.h1>

            <motion.p
              className="text-white/90 text-xl max-w-2xl mx-auto text-center leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
            >
              Transforming how people discover and choose products through advanced sentiment analysis
            </motion.p>

            <motion.div
              className="mt-8 w-24 h-1 bg-secondary rounded-full"
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 96, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            />
          </div>
        </div>

        {/* Stats Section - Modern Card Design */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16 -mt-24">
          {[
            {
              icon: FiUsers,
              value: "50K+",
              label: "Active Users",
              color: "primary",
              description: "Trusted by users worldwide"
            },
            {
              icon: FiBarChart2,
              value: "1M+",
              label: "Reviews Analyzed",
              color: "secondary",
              description: "Comprehensive data analysis"
            },
            {
              icon: FiAward,
              value: "95%",
              label: "Accuracy Rate",
              color: "accent",
              description: "Industry-leading precision"
            },
            {
              icon: FiTrendingUp,
              value: "3K+",
              label: "Products Tracked",
              color: "primary",
              description: "Across multiple categories"
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl shadow-premium p-6 flex flex-col items-center text-center relative overflow-hidden group"
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 20,
                delay: 0.3 + (index * 0.1)
              }}
              whileHover={{
                y: -5,
                boxShadow: "0 20px 40px -5px rgba(0, 0, 0, 0.1), 0 10px 15px -5px rgba(0, 0, 0, 0.05)"
              }}
            >
              {/* Background decoration */}
              <div className={`absolute top-0 left-0 w-full h-1 bg-${stat.color}`}></div>
              <div className={`absolute -right-12 -top-12 w-24 h-24 rounded-full bg-${stat.color}/5 group-hover:bg-${stat.color}/10 transition-colors duration-300`}></div>

              {/* Icon */}
              <motion.div
                className={`bg-${stat.color}/10 p-4 rounded-full mb-5 text-${stat.color} relative z-10`}
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <stat.icon size={28} />
              </motion.div>

              {/* Content */}
              <h3 className="text-4xl font-bold text-gray-900 mb-2 font-display">{stat.value}</h3>
              <p className={`text-${stat.color} font-medium mb-2`}>{stat.label}</p>
              <p className="text-gray-500 text-sm">{stat.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Mission and Values Section - Premium Design */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mb-20">
          <motion.div
            className="bg-white rounded-xl shadow-premium p-8 border border-gray-100 relative overflow-hidden group"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.2 }}
            whileHover={{
              boxShadow: "0 20px 40px -5px rgba(0, 0, 0, 0.1), 0 10px 15px -5px rgba(0, 0, 0, 0.05)",
              y: -5
            }}
          >
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-primary/5 rounded-bl-full transform group-hover:scale-110 transition-transform duration-500"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-primary/5 rounded-tr-full transform group-hover:scale-110 transition-transform duration-500"></div>
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary to-primary-light"></div>

            <div className="flex items-center mb-8">
              <motion.div
                className="bg-primary/10 p-4 rounded-full text-primary mr-5 flex-shrink-0"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <FiCheckCircle size={28} />
              </motion.div>
              <div>
                <h2 className="text-3xl font-bold text-gray-900 font-display">Our Mission</h2>
                <div className="w-20 h-1 bg-primary/20 mt-2"></div>
              </div>
            </div>

            <div className="space-y-5 relative z-10">
              <p className="text-gray-700 leading-relaxed">
                At ProductWhisper, our mission is to help consumers make informed purchasing decisions by providing
                unbiased, data-driven insights into products across various categories. We believe in the power of
                authentic user experiences and leverage advanced sentiment analysis to cut through marketing hype.
              </p>
              <p className="text-gray-700 leading-relaxed">
                We're committed to transparency, accuracy, and continuously improving our platform to better serve
                our community of discerning shoppers.
              </p>

              <div className="pt-6 mt-6 border-t border-gray-100">
                <h3 className="font-semibold text-gray-900 mb-4 text-lg">Our Core Values</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {[
                    { name: "Data-Driven Insights", color: "primary" },
                    { name: "User Privacy", color: "secondary" },
                    { name: "Continuous Innovation", color: "accent" },
                    { name: "Transparency", color: "primary" }
                  ].map((value, i) => (
                    <motion.div
                      key={i}
                      className={`flex items-center p-3 rounded-lg bg-${value.color}/5 group-hover:bg-${value.color}/10 transition-colors duration-300`}
                      whileHover={{ x: 5 }}
                    >
                      <div className={`w-3 h-3 bg-${value.color} rounded-full mr-3 flex-shrink-0`}></div>
                      <span className="text-gray-800 font-medium">{value.name}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl shadow-premium p-8 border border-gray-100 relative overflow-hidden group"
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.4 }}
            whileHover={{
              boxShadow: "0 20px 40px -5px rgba(0, 0, 0, 0.1), 0 10px 15px -5px rgba(0, 0, 0, 0.05)",
              y: -5
            }}
          >
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-40 h-40 bg-secondary/5 rounded-br-full transform group-hover:scale-110 transition-transform duration-500"></div>
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-secondary/5 rounded-tl-full transform group-hover:scale-110 transition-transform duration-500"></div>
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-secondary-light to-secondary"></div>

            <div className="flex items-center mb-8">
              <motion.div
                className="bg-secondary/10 p-4 rounded-full text-secondary mr-5 flex-shrink-0"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <FiUsers size={28} />
              </motion.div>
              <div>
                <h2 className="text-3xl font-bold text-gray-900 font-display">Our Story</h2>
                <div className="w-20 h-1 bg-secondary/20 mt-2"></div>
              </div>
            </div>

            <div className="space-y-5 relative z-10">
              <p className="text-gray-700 leading-relaxed">
                ProductWhisper was founded in 2023 by a team of technology enthusiasts and consumer advocates who
                were frustrated with the lack of reliable product information online. We noticed that traditional
                review platforms were often filled with fake reviews, biased opinions, or simply lacked the depth
                needed to make confident purchasing decisions.
              </p>
              <p className="text-gray-700 leading-relaxed">
                We set out to build a platform that uses advanced natural language processing and machine learning
                to analyze thousands of authentic user reviews and comments from across the web. Our technology
                identifies patterns, sentiment, and specific product attributes that matter most to consumers.
              </p>

              <div className="pt-6 mt-6 border-t border-gray-100">
                <div className="grid grid-cols-3 gap-4">
                  {[
                    { value: "2023", label: "Founded", color: "secondary" },
                    { value: "15+", label: "Team Members", color: "primary" },
                    { value: "3", label: "Global Offices", color: "accent" }
                  ].map((stat, i) => (
                    <motion.div
                      key={i}
                      className={`text-center p-4 rounded-lg bg-${stat.color}/5 group-hover:bg-${stat.color}/10 transition-colors duration-300`}
                      whileHover={{ y: -3 }}
                    >
                      <div className={`text-2xl font-bold text-${stat.color}`}>{stat.value}</div>
                      <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Technology Section - Modern Premium Design */}
        <div className="relative mb-20">
          {/* Background gradient with enhanced visual effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-2xl"></div>
          <div className="absolute top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-secondary/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 transform translate-x-1/2 translate-y-1/2 w-48 h-48 bg-accent/5 rounded-full blur-3xl"></div>

          <motion.div
            className="relative bg-white/90 backdrop-blur-sm rounded-xl shadow-premium p-10 border border-gray-100"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.4 }}
          >
            {/* Section header with premium styling */}
            <div className="text-center mb-12">
              <motion.div
                className="inline-flex items-center justify-center p-2 bg-primary/10 backdrop-blur-sm rounded-full mb-4"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 20, delay: 0.5 }}
              >
                <div className="bg-primary/20 p-3 rounded-full">
                  <FiBarChart2 className="w-6 h-6 text-primary" />
                </div>
              </motion.div>
              <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">Our Technology</h2>
              <p className="text-gray-700 max-w-2xl mx-auto">
                At the heart of ProductWhisper is our proprietary sentiment analysis engine that processes millions of reviews to extract meaningful insights.
              </p>
              <motion.div
                className="w-24 h-1 bg-primary/30 mx-auto mt-6"
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 96, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              />
            </div>

            <div className="flex flex-col md:flex-row md:items-start gap-10">
              {/* Left column - Why it matters */}
              <div className="md:w-1/3">
                <div className="sticky top-8 bg-gradient-to-br from-primary/5 to-transparent p-6 rounded-xl border border-primary/10">
                  <h3 className="font-display font-semibold text-xl text-primary mb-4">Why It Matters</h3>
                  <p className="text-gray-700">
                    Our technology provides a comprehensive, nuanced understanding of products that goes far beyond simple star ratings, helping consumers make truly informed decisions.
                  </p>

                  <div className="mt-6 pt-6 border-t border-primary/10">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center shadow-sm mr-4">
                        <FiSearch className="text-primary" size={20} />
                      </div>
                      <div className="text-sm font-medium text-gray-800">Discover the truth behind products</div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center shadow-sm mr-4">
                        <FiTrendingUp className="text-primary" size={20} />
                      </div>
                      <div className="text-sm font-medium text-gray-800">Track sentiment changes over time</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right column - Technology steps */}
              <div className="md:w-2/3">
                <div className="space-y-6">
                  {[
                    {
                      title: "Data Collection",
                      description: "We collect and analyze thousands of authentic user reviews from across the internet",
                      details: "Our crawlers scan hundreds of e-commerce sites, forums, and social media platforms to gather diverse opinions.",
                      color: "primary"
                    },
                    {
                      title: "Feature Extraction",
                      description: "Our natural language processing algorithms identify key product attributes and features",
                      details: "We use advanced NLP techniques to identify what aspects of products matter most to consumers.",
                      color: "secondary"
                    },
                    {
                      title: "Sentiment Analysis",
                      description: "We measure sentiment around each attribute to understand what users truly love or dislike",
                      details: "Our sentiment engine can detect subtle nuances in language, including sarcasm and context-dependent opinions.",
                      color: "accent"
                    },
                    {
                      title: "Entity Recognition",
                      description: "Our entity recognition system distinguishes between different product models and versions",
                      details: "We can accurately differentiate between similar products and track sentiment for specific model variations.",
                      color: "primary"
                    },
                    {
                      title: "Trend Tracking",
                      description: "We track sentiment trends over time to identify quality changes or emerging issues",
                      details: "Our temporal analysis can detect when product quality changes after firmware updates or new releases.",
                      color: "secondary"
                    }
                  ].map((step, index) => (
                    <motion.div
                      key={index}
                      className={`bg-white p-6 rounded-xl shadow-premium border border-gray-100 hover:shadow-lg transition-all relative overflow-hidden group`}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.6 + (index * 0.1) }}
                      whileHover={{ y: -5 }}
                    >
                      {/* Decorative elements */}
                      <div className={`absolute top-0 left-0 w-1 h-full bg-${step.color}`}></div>
                      <div className={`absolute -right-12 -bottom-12 w-24 h-24 rounded-full bg-${step.color}/5 group-hover:bg-${step.color}/10 transition-colors duration-300`}></div>

                      <div className="flex items-center mb-4">
                        <motion.div
                          className={`flex-shrink-0 w-12 h-12 bg-${step.color}/10 rounded-full flex items-center justify-center text-${step.color} font-bold mr-4`}
                          whileHover={{ scale: 1.1, rotate: 10 }}
                          transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                          {index + 1}
                        </motion.div>
                        <h3 className="font-display font-semibold text-xl text-gray-900">{step.title}</h3>
                      </div>
                      <p className="text-gray-700 mb-3 pl-16">{step.description}</p>
                      <p className="text-gray-500 text-sm pl-16 border-l-2 border-gray-100 ml-6 py-2">{step.details}</p>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Team Section - Modern Premium Design */}
        <div className="relative mb-20">
          {/* Enhanced background effects */}
          <div className="absolute inset-0 bg-gradient-to-b from-secondary/10 via-accent/5 to-transparent rounded-2xl"></div>
          <div className="absolute top-1/3 right-1/4 transform translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-secondary/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/3 left-1/4 transform -translate-x-1/2 translate-y-1/2 w-48 h-48 bg-accent/5 rounded-full blur-3xl"></div>

          <motion.div
            className="relative bg-white/90 backdrop-blur-sm rounded-xl shadow-premium p-10 border border-gray-100"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.5 }}
          >
            {/* Section header with premium styling */}
            <div className="text-center mb-16">
              <motion.div
                className="inline-flex items-center justify-center p-2 bg-secondary/10 backdrop-blur-sm rounded-full mb-4"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 20, delay: 0.6 }}
              >
                <div className="bg-secondary/20 p-3 rounded-full">
                  <FiUsers className="w-6 h-6 text-secondary" />
                </div>
              </motion.div>
              <h2 className="text-3xl font-bold text-gray-900 font-display mb-4">Meet Our Team</h2>
              <p className="text-gray-700 max-w-2xl mx-auto">
                ProductWhisper is built by a diverse team of technologists, data scientists, product experts, and
                consumer advocates who are passionate about helping people make better purchasing decisions.
              </p>
              <motion.div
                className="w-24 h-1 bg-secondary/30 mx-auto mt-6"
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 96, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.7 }}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  name: "Alex Johnson",
                  role: "Founder & CEO",
                  bio: "Former product lead at Amazon with a passion for consumer advocacy.",
                  image: "https://randomuser.me/api/portraits/men/32.jpg",
                  color: "primary"
                },
                {
                  name: "Samantha Chen",
                  role: "Chief Data Scientist",
                  bio: "PhD in Machine Learning with expertise in natural language processing.",
                  image: "https://randomuser.me/api/portraits/women/44.jpg",
                  color: "secondary"
                },
                {
                  name: "Marcus Williams",
                  role: "Head of Product",
                  bio: "Consumer tech expert with 10+ years experience in product management.",
                  image: "https://randomuser.me/api/portraits/men/68.jpg",
                  color: "accent"
                },
                {
                  name: "Priya Patel",
                  role: "Lead Engineer",
                  bio: "Full-stack developer specializing in scalable data processing systems.",
                  image: "https://randomuser.me/api/portraits/women/65.jpg",
                  color: "primary"
                },
                {
                  name: "David Kim",
                  role: "UX Director",
                  bio: "Award-winning designer focused on creating intuitive user experiences.",
                  image: "https://randomuser.me/api/portraits/men/75.jpg",
                  color: "secondary"
                },
                {
                  name: "Olivia Rodriguez",
                  role: "Content Director",
                  bio: "Former tech journalist with a keen eye for product trends and insights.",
                  image: "https://randomuser.me/api/portraits/women/90.jpg",
                  color: "accent"
                }
              ].map((member, index) => (
                <motion.div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-premium border border-gray-100 hover:shadow-lg transition-all group relative overflow-hidden"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.7 + (index * 0.1) }}
                  whileHover={{ y: -5 }}
                >
                  {/* Decorative elements */}
                  <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-${member.color} to-${member.color}/70`}></div>
                  <div className={`absolute -right-12 -bottom-12 w-24 h-24 rounded-full bg-${member.color}/5 group-hover:bg-${member.color}/10 transition-colors duration-300`}></div>

                  <div className="flex flex-col items-center text-center mb-4">
                    <div className={`w-24 h-24 rounded-full overflow-hidden mb-4 border-2 border-${member.color}/30 shadow-md`}>
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                    </div>
                    <h3 className="font-display font-semibold text-xl text-gray-900 mb-1">{member.name}</h3>
                    <p className={`text-${member.color} text-sm font-medium mb-3`}>{member.role}</p>
                    <div className={`w-12 h-0.5 bg-${member.color}/30 mb-3`}></div>
                    <p className="text-gray-600">{member.bio}</p>
                  </div>

                  <div className="flex justify-center space-x-3 mt-4 pt-4 border-t border-gray-100">
                    {['linkedin', 'twitter', 'email'].map((social, i) => (
                      <motion.a
                        key={i}
                        href="#"
                        className={`w-8 h-8 rounded-full bg-${member.color}/10 flex items-center justify-center text-${member.color}`}
                        whileHover={{ y: -3, backgroundColor: `rgba(var(--color-${member.color}), 0.2)` }}
                      >
                        <span className="text-xs">{social[0].toUpperCase()}</span>
                      </motion.a>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="mt-16 text-center">
              <motion.button
                className="px-8 py-4 bg-gradient-to-r from-secondary to-secondary-light text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all inline-flex items-center"
                whileHover={{ scale: 1.03, y: -2 }}
                whileTap={{ scale: 0.98 }}
              >
                <FiUsers className="mr-2" />
                Join Our Team
              </motion.button>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default AboutPage;
