import React from 'react';
import { motion } from 'framer-motion';
import { XCircleIcon } from '@heroicons/react/24/outline';

interface ErrorStateProps {
  message?: string;
  details?: string;
  onRetry?: () => void;
  fullScreen?: boolean;
}

/**
 * Error state component with animation
 */
const ErrorState: React.FC<ErrorStateProps> = ({
  message = 'Something went wrong',
  details,
  onRetry,
  fullScreen = false,
}) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.3,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  // Render the error state
  const renderContent = () => (
    <motion.div 
      className="flex flex-col items-center justify-center p-6 text-center"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <XCircleIcon className="w-16 h-16 text-red-500 mb-4" />
      </motion.div>
      
      <motion.h3 
        className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2"
        variants={itemVariants}
      >
        {message}
      </motion.h3>
      
      {details && (
        <motion.p 
          className="text-gray-600 dark:text-gray-400 mb-4 max-w-md"
          variants={itemVariants}
        >
          {details}
        </motion.p>
      )}
      
      {onRetry && (
        <motion.button
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
          onClick={onRetry}
          variants={itemVariants}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Try Again
        </motion.button>
      )}
    </motion.div>
  );

  // Full screen error state
  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center z-50 bg-white dark:bg-gray-900">
        {renderContent()}
      </div>
    );
  }

  // Inline error state
  return (
    <div className="rounded-lg border border-red-200 dark:border-red-900 bg-red-50 dark:bg-red-900/20 p-4">
      {renderContent()}
    </div>
  );
};

export default ErrorState;
