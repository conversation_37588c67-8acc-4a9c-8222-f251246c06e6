import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';

// Import components
import SimpleErrorBoundary from './components/common/SimpleErrorBoundary';
import LoadingSpinner from './components/common/LoadingSpinner';
import ChatBox from './components/common/ChatBox';
import { MainLayout } from './layouts';

// Import contexts
import { ChatProvider } from './contexts/ChatContext';

/**
 * Lazy-loaded page components to improve initial load time
 * Using the index.ts file to avoid direct .tsx imports
 */
const HomePage = lazy(() => import('./pages').then(module => ({ default: module.HomePage })));
const SearchPage = lazy(() => import('./pages').then(module => ({ default: module.SearchPage })));
const TrendsPage = lazy(() => import('./pages').then(module => ({ default: module.TrendsPage })));
const ComparisonPage = lazy(() => import('./pages').then(module => ({ default: module.ComparisonPage })));
const AboutPage = lazy(() => import('./pages').then(module => ({ default: module.AboutPage })));
const ContactPage = lazy(() => import('./pages').then(module => ({ default: module.ContactPage })));
const ProductPage = lazy(() => import('./pages').then(module => ({ default: module.ProductPage })));
const SentimentAnalysisPage = lazy(() => import('./pages').then(module => ({ default: module.SentimentAnalysisPage })));
// Business pages
const BusinessSearchPage = lazy(() => import('./pages').then(module => ({ default: module.BusinessSearchPage })));
const BusinessDetailsPage = lazy(() => import('./pages').then(module => ({ default: module.BusinessDetailsPage })));
// Commented out pages that are not currently needed
// const ProfilePage = lazy(() => import('./pages/ProfilePage'));
// const NotificationsPage = lazy(() => import('./pages/NotificationsPage'));
// const DashboardPage = lazy(() => import('./pages/DashboardPage'));

/**
 * Loading fallback component shown while pages are loading
 */
const PageLoadingFallback = () => (
  <div className="flex justify-center items-center min-h-screen">
    <LoadingSpinner size="large" text="Loading page..." />
  </div>
);

/**
 * Main App component that sets up routing
 */
function App() {
  // Helper function to wrap page components with SimpleErrorBoundary and Suspense
  const withErrorBoundaryAndSuspense = (
    Component: React.LazyExoticComponent<React.ComponentType<object>>,
    name: string
  ) => (
    <SimpleErrorBoundary name={`Page-${name}`}>
      <Suspense fallback={<PageLoadingFallback />}>
        <Component />
      </Suspense>
    </SimpleErrorBoundary>
  );

  return (
    <SimpleErrorBoundary name="App-Root">
      <ChatProvider>
        <Router>
          <Routes>
            <Route path="/" element={<MainLayout />}>
              {/* Home Page */}
              <Route index element={withErrorBoundaryAndSuspense(HomePage, 'Home')} />

              {/* Main Pages */}
              <Route path="search" element={withErrorBoundaryAndSuspense(SearchPage, 'Search')} />
              <Route path="trends" element={withErrorBoundaryAndSuspense(TrendsPage, 'Trends')} />
              <Route path="compare" element={withErrorBoundaryAndSuspense(ComparisonPage, 'Comparison')} />
              <Route path="about" element={withErrorBoundaryAndSuspense(AboutPage, 'About')} />
              <Route path="contact" element={withErrorBoundaryAndSuspense(ContactPage, 'Contact')} />
              <Route path="product/:id" element={withErrorBoundaryAndSuspense(ProductPage, 'Product')} />
              <Route path="sentiment" element={withErrorBoundaryAndSuspense(SentimentAnalysisPage, 'Sentiment')} />
              <Route path="sentiment-test" element={withErrorBoundaryAndSuspense(SentimentAnalysisPage, 'Sentiment')} />

              {/* Business Pages */}
              <Route path="business/search" element={withErrorBoundaryAndSuspense(BusinessSearchPage, 'BusinessSearch')} />
              <Route path="business/:source/:id" element={withErrorBoundaryAndSuspense(BusinessDetailsPage, 'BusinessDetails')} />

              {/* Commented out pages that are not currently needed */}
              {/* <Route path="profile" element={withErrorBoundaryAndSuspense(ProfilePage, 'Profile')} /> */}
              {/* <Route path="notifications" element={withErrorBoundaryAndSuspense(NotificationsPage, 'Notifications')} /> */}
              {/* <Route path="dashboard" element={withErrorBoundaryAndSuspense(DashboardPage, 'Dashboard')} /> */}
            </Route>
          </Routes>

          {/* Chat Box Component */}
          <SimpleErrorBoundary name="ChatBox">
            <ChatBox position="bottom-right" />
          </SimpleErrorBoundary>
        </Router>
      </ChatProvider>
    </SimpleErrorBoundary>
  );
}

export default App;

