import React, { ReactNode } from 'react';
import { motion, useInView } from 'framer-motion';

interface ScrollFadeInProps {
  children: ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
  delay?: number;
  threshold?: number;
  distance?: number;
  once?: boolean;
  className?: string;
  staggerChildren?: boolean;
  staggerDelay?: number;
}

/**
 * A component that fades in its children when they enter the viewport
 */
const ScrollFadeIn: React.FC<ScrollFadeInProps> = ({
  children,
  direction = 'up',
  duration = 0.6,
  delay = 0,
  threshold = 0.2,
  distance = 50,
  once = true,
  className = '',
  staggerChildren = false,
  staggerDelay = 0.1,
}) => {
  const ref = React.useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { once, amount: threshold });

  // Determine initial position based on direction
  const getInitialPosition = () => {
    switch (direction) {
      case 'up':
        return { opacity: 0, y: distance };
      case 'down':
        return { opacity: 0, y: -distance };
      case 'left':
        return { opacity: 0, x: distance };
      case 'right':
        return { opacity: 0, x: -distance };
      default:
        return { opacity: 0, y: distance };
    }
  };

  // Determine animation target
  const getAnimationTarget = () => {
    switch (direction) {
      case 'up':
      case 'down':
        return { opacity: 1, y: 0 };
      case 'left':
      case 'right':
        return { opacity: 1, x: 0 };
      default:
        return { opacity: 1, y: 0 };
    }
  };

  // If staggering children, wrap each child in a motion.div
  const renderChildren = () => {
    if (!staggerChildren) {
      return children;
    }

    return React.Children.map(children, (child, index) => (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{
          duration: duration,
          delay: delay + index * staggerDelay,
          ease: 'easeOut',
        }}
      >
        {child}
      </motion.div>
    ));
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      initial={getInitialPosition()}
      animate={isInView ? getAnimationTarget() : getInitialPosition()}
      transition={{
        duration: duration,
        delay: delay,
        ease: 'easeOut',
      }}
    >
      {renderChildren()}
    </motion.div>
  );
};

export default ScrollFadeIn;
