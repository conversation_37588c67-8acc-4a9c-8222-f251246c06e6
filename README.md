# ProductWhisper

ProductWhisper is a sentiment analysis application for product reviews with modern design, using React, TypeScript, Tailwind CSS, and Framer Motion. The backend is built with Node.js, Express, PostgreSQL, and a Python Flask microservice for sentiment analysis.

## Features

- Real-time sentiment analysis of product reviews
- Entity and aspect extraction from reviews
- Reddit API integration for trend analysis
- Personalized product recommendations
- Product comparison with sentiment analysis
- Modern, premium UI with animations

## Project Architecture

The ProductWhisper application consists of the following components:

1. **Frontend (React + TypeScript + Vite)**

   - Modern UI built with React, TypeScript, and Tailwind CSS
   - Animations powered by Framer Motion
   - Located in the root directory

2. **Backend API (Node.js + Express + TypeScript)**

   - RESTful API for product data, search, and trend analysis
   - Reddit API integration for product discussions
   - Located in the `backend` directory

3. **Sentiment Analysis Service (Python + Flask)**

   - Natural language processing for sentiment analysis
   - Aspect-based sentiment extraction
   - Located in the `backend/python-nlp-service` directory

4. **Database (PostgreSQL)**

   - Stores product data, reviews, and trend information

5. **Cache (Redis)**
   - Caches API responses and Reddit data

## Getting Started

### Prerequisites

- Node.js (v16+)
- Docker and Docker Compose (recommended)
- PostgreSQL (if running locally)
- Redis (if running locally)
- Python 3.9+ (if running the sentiment analysis service locally)

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/ZILLABB/productwhisper.git
   cd productwhisper
   ```

2. Install frontend dependencies:

   ```bash
   npm install
   ```

3. Set up the backend:

   ```bash
   cd backend
   npm install
   ```

4. Set up the Python sentiment analysis service:

   ```bash
   cd backend/python-nlp-service
   pip install -r requirements.txt
   ```

### Running with Docker (Recommended)

The easiest way to run the entire application is using Docker Compose:

```bash
docker-compose up
```

This will start all the required services:

- Frontend on port 3000
- Backend API on port 8000
- Sentiment analysis service on port 5000
- PostgreSQL on port 5432
- Redis on port 6379
- pgAdmin on port 5050

### Running Locally

1. Start the frontend (from the root directory):

   ```bash
   npm run dev
   ```

2. Start the backend API:

   ```bash
   cd backend
   npm run dev
   ```

3. Start the sentiment analysis service:

   ```bash
   cd backend/python-nlp-service
   python app.py
   ```

The frontend will be available at http://localhost:3000
The backend API will be available at http://localhost:8000
The sentiment analysis service will be available at http://localhost:5000

## Documentation

- [Frontend Documentation](./src/README.md)
- [Backend Documentation](./backend/README.md)
- [Sentiment Analysis Service Documentation](./backend/python-nlp-service/README.md)

## License

This project is licensed under the MIT License.
