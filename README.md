# ProductWhisper Frontend

ProductWhisper Frontend is a modern React/TypeScript application for sentiment analysis of product reviews. It provides a premium user interface for discovering products through sentiment analysis of reviews and discussions from various sources.

## Features

- 🎨 **Modern UI/UX**: Premium design with Tailwind CSS and Framer Motion animations
- 🔍 **Advanced Search**: Product search with intelligent filters and suggestions
- 📊 **Sentiment Analysis**: Real-time sentiment visualization and analysis
- 📈 **Trend Analysis**: Product trends from Reddit and other sources
- ⚖️ **Product Comparison**: Side-by-side product comparison with sentiment insights
- 💬 **Interactive Chat**: FAQ-powered chatbot for user assistance
- 📱 **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- ⚡ **Performance**: Lazy loading, code splitting, and optimized bundle size

## Technology Stack

- **React 19.1.0** - Modern React with latest features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and development server
- **Tailwind CSS 3.4.17** - Utility-first CSS framework
- **Framer Motion 12.10.0** - Smooth animations and transitions
- **React Router DOM 7.5.3** - Client-side routing
- **Radix UI** - Accessible component primitives
- **Axios** - HTTP client for API communication
- **Lucide React** - Beautiful icon library

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Shared components (Button, Input, etc.)
│   ├── features/       # Feature-specific components
│   ├── layout/         # Layout components
│   ├── product/        # Product-related components
│   ├── search/         # Search functionality components
│   ├── sentiment/      # Sentiment analysis components
│   ├── trends/         # Trend analysis components
│   └── ui/            # UI primitives and animations
├── contexts/           # React contexts for state management
├── hooks/             # Custom React hooks
├── layouts/           # Page layout components
├── pages/             # Page components
├── services/          # API services and external integrations
├── types/             # TypeScript type definitions
├── utils/             # Utility functions and helpers
└── constants/         # Application constants
```

## Getting Started

### Prerequisites

- **Node.js** (v18+ recommended)
- **npm** or **yarn** package manager
- **ProductWhisper Backend** running (see [productwhisper-backend](https://github.com/ZILLABB/productwhisper-backend) repository)

### Installation

1. **Clone the frontend repository:**

   ```bash
   git clone https://github.com/ZILLABB/productwhisper-frontend.git
   cd productwhisper-frontend
   ```

2. **Install dependencies:**

   ```bash
   npm install
   ```

3. **Set up environment variables:**

   Create a `.env` file in the root directory:

   ```env
   # API Configuration
   VITE_API_BASE_URL=http://localhost:8000/api

   # Feature Flags
   VITE_USE_MOCK_DATA=false

   # Environment
   VITE_APP_ENV=development
   ```

4. **Start the development server:**

   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:5173`

## Available Scripts

- **`npm run dev`** - Start development server with hot reload
- **`npm run build`** - Build the application for production
- **`npm run preview`** - Preview the production build locally
- **`npm run lint`** - Run ESLint to check code quality
- **`npm run type-check`** - Run TypeScript type checking

## Environment Variables

The frontend supports the following environment variables:

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:8000/api  # Backend API URL

# Feature Flags
VITE_USE_MOCK_DATA=false                     # Use mock data instead of API calls

# Application Settings
VITE_APP_ENV=development                     # Application environment
VITE_APP_NAME=ProductWhisper                 # Application name
VITE_APP_VERSION=1.0.0                       # Application version
```

## Backend Integration

This frontend application requires the **ProductWhisper Backend** to be running. The backend provides:

- **Product Search API** - Search and filter products
- **Sentiment Analysis** - Analyze product reviews and discussions
- **Reddit Integration** - Fetch trending product discussions
- **Product Comparison** - Compare products with sentiment insights
- **Chat API** - FAQ and chatbot functionality

### Backend Repository

Make sure to clone and set up the backend repository:

```bash
git clone https://github.com/ZILLABB/productwhisper-backend.git
cd productwhisper-backend
# Follow backend setup instructions
```

## Development Workflow

1. **Start the backend services** (see backend repository)
2. **Start the frontend development server:**
   ```bash
   npm run dev
   ```
3. **Open your browser** to `http://localhost:5173`
4. **Make changes** - The development server will automatically reload

## Building for Production

1. **Build the application:**

   ```bash
   npm run build
   ```

2. **Preview the build:**

   ```bash
   npm run preview
   ```

3. **Deploy the `dist/` folder** to your hosting provider (Vercel, Netlify, AWS S3, etc.)

## Deployment

### Production Build

1. **Set production environment variables:**
   ```env
   VITE_API_BASE_URL=https://api.productwhisper.com/api
   VITE_USE_MOCK_DATA=false
   VITE_APP_ENV=production
   ```

2. **Build the application:**
   ```bash
   npm run build
   ```

### Environment-Specific Configurations

- **Development**: Uses `http://localhost:8000/api` for backend
- **Staging**: Configure staging backend URL
- **Production**: Uses production backend URL

## Troubleshooting

### Common Issues

1. **API Connection Errors**

   - Ensure the backend is running on the correct port
   - Check CORS settings in the backend
   - Verify API_BASE_URL in environment variables

2. **Build Errors**

   - Clear node_modules and reinstall: `rm -rf node_modules package-lock.json && npm install`
   - Check TypeScript errors: `npm run type-check`

3. **Development Server Issues**
   - Check if port 5173 is available
   - Try clearing Vite cache: `rm -rf node_modules/.vite`

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## Related Repositories

- **Backend**: [productwhisper-backend](https://github.com/ZILLABB/productwhisper-backend)
- **Documentation**: [productwhisper-docs](https://github.com/ZILLABB/productwhisper-docs) (if applicable)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
