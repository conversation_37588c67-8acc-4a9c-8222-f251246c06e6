import { Router } from 'express';
import productRoutes from './productRoutes';
import searchRoutes from './searchRoutes';
import trendRoutes from './trendRoutes';
import comparisonRoutes from './comparisonRoutes';
import chatRoutes from './chatRoutes';
import redditRoutes from './redditRoutes';
import recommendationRoutes from './recommendationRoutes';

const router = Router();

// Register routes
router.use('/products', productRoutes);
router.use('/search', searchRoutes);
router.use('/trends', trendRoutes);
router.use('/compare', comparisonRoutes);
router.use('/chat', chatRoutes);
router.use('/reddit', redditRoutes);
router.use('/recommendations', recommendationRoutes);

export default router;
