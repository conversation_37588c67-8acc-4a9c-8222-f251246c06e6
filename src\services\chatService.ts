import { apiService } from './api';
import faqData from '../constants/faqData';
import type { FAQItem } from '../types/faq';

// Cache for chat responses
interface ChatCache {
  [key: string]: {
    response: string;
    timestamp: number;
  };
}

// Analytics for tracking questions
interface ChatAnalytics {
  questions: {
    [key: string]: number;
  };
  matchedFaqs: {
    [key: string]: number;
  };
  noMatchCount: number;
  totalQuestions: number;
}

/**
 * Chat Service for ProductWhisper
 */
class ChatService {
  private cache: ChatCache = {};
  private analytics: ChatAnalytics = {
    questions: {},
    matchedFaqs: {},
    noMatchCount: 0,
    totalQuestions: 0
  };
  private cacheDuration = 30 * 60 * 1000; // 30 minutes
  private cacheEnabled = true;

  constructor() {
    // Load cache from localStorage if available
    this.loadCache();
    this.loadAnalytics();
  }

  /**
   * Load cache from localStorage
   */
  private loadCache(): void {
    try {
      const cachedData = localStorage.getItem('pw_chat_cache');
      if (cachedData) {
        this.cache = JSON.parse(cachedData);

        // Clean expired cache entries
        this.cleanCache();
      }
    } catch (error) {
      console.error('Error loading chat cache:', error);
      this.cache = {};
    }
  }

  /**
   * Save cache to localStorage
   */
  private saveCache(): void {
    try {
      localStorage.setItem('pw_chat_cache', JSON.stringify(this.cache));
    } catch (error) {
      console.error('Error saving chat cache:', error);
    }
  }

  /**
   * Load analytics from localStorage
   */
  private loadAnalytics(): void {
    try {
      const analyticsData = localStorage.getItem('pw_chat_analytics');
      if (analyticsData) {
        this.analytics = JSON.parse(analyticsData);
      }
    } catch (error) {
      console.error('Error loading chat analytics:', error);
      this.analytics = {
        questions: {},
        matchedFaqs: {},
        noMatchCount: 0,
        totalQuestions: 0
      };
    }
  }

  /**
   * Save analytics to localStorage
   */
  private saveAnalytics(): void {
    try {
      localStorage.setItem('pw_chat_analytics', JSON.stringify(this.analytics));
    } catch (error) {
      console.error('Error saving chat analytics:', error);
    }
  }

  /**
   * Clean expired cache entries
   */
  private cleanCache(): void {
    const now = Date.now();
    let hasChanges = false;

    Object.keys(this.cache).forEach(key => {
      if (now - this.cache[key].timestamp > this.cacheDuration) {
        delete this.cache[key];
        hasChanges = true;
      }
    });

    if (hasChanges) {
      this.saveCache();
    }
  }

  /**
   * Track question for analytics
   */
  private trackQuestion(query: string, matchedFaq: FAQItem | null): void {
    // Increment total questions
    this.analytics.totalQuestions++;

    // Track the question
    const normalizedQuery = query.toLowerCase().trim();
    this.analytics.questions[normalizedQuery] = (this.analytics.questions[normalizedQuery] || 0) + 1;

    // Track matched FAQ or no match
    if (matchedFaq) {
      const faqId = matchedFaq.question;
      this.analytics.matchedFaqs[faqId] = (this.analytics.matchedFaqs[faqId] || 0) + 1;
    } else {
      this.analytics.noMatchCount++;
    }

    // Save analytics
    this.saveAnalytics();
  }

  /**
   * Find the best matching FAQ for a user query
   */
  private findBestMatch(query: string): FAQItem | null {
    // Safety check: if faqData is null or undefined, return null
    if (!faqData || !Array.isArray(faqData) || faqData.length === 0) {
      return null;
    }

    // Convert query to lowercase for case-insensitive matching
    const normalizedQuery = query.toLowerCase();

    // Check for exact question matches first
    const exactMatch = faqData.find(
      item => item && item.question && item.question.toLowerCase() === normalizedQuery
    );

    if (exactMatch) return exactMatch;

    // Check for keyword matches
    let bestMatch: FAQItem | null = null;
    let highestScore = 0;

    faqData.forEach(item => {
      // Skip invalid items
      if (!item || !item.keywords || !Array.isArray(item.keywords) || !item.question) {
        return;
      }

      let score = 0;

      // Check if query contains keywords
      item.keywords.forEach(keyword => {
        if (keyword && normalizedQuery.includes(keyword.toLowerCase())) {
          score += 1;
        }
      });

      // Check if query contains words from the question
      const questionWords = item.question.toLowerCase().split(' ');
      questionWords.forEach(word => {
        if (word && word.length > 3 && normalizedQuery.includes(word)) {
          score += 0.5;
        }
      });

      if (score > highestScore) {
        highestScore = score;
        bestMatch = item;
      }
    });

    // Only return a match if the score is above a threshold
    return highestScore >= 1 ? bestMatch : null;
  }

  /**
   * Get response from API
   */
  private async getResponseFromApi(query: string): Promise<string> {
    try {
      // In development mode or if API is not available, use mock responses
      if (process.env.NODE_ENV === 'development' || process.env.REACT_APP_USE_MOCK_API === 'true') {
        console.log('Using mock chat response for query:', query);

        // Generate a mock response based on the query
        return this.generateMockResponse(query);
      }

      // Make sure query is properly encoded
      const encodedQuery = encodeURIComponent(query.trim());

      // Call the chat API endpoint
      const response = await apiService.get(`/chat?query=${encodedQuery}`);

      // Check if the response has the expected structure
      if (response.data && response.data.success && response.data.response) {
        return response.data.response;
      }

      // If response doesn't have the expected structure, return a fallback message
      return "I couldn't find an answer to that question.";
    } catch (error) {
      console.error('Error getting chat response from API:', error);
      // Instead of throwing the error, return a mock response
      return this.generateMockResponse(query);
    }
  }

  /**
   * Generate a mock response for development and fallback
   */
  private generateMockResponse(query: string): string {
    const normalizedQuery = query.toLowerCase().trim();

    // Product-related queries
    if (normalizedQuery.includes('product') || normalizedQuery.includes('recommend')) {
      return "Based on recent sentiment analysis, the most highly rated products in our database are wireless noise-cancelling headphones, smart home assistants, and ergonomic office chairs. Would you like more specific recommendations?";
    }

    // Review-related queries
    if (normalizedQuery.includes('review') || normalizedQuery.includes('rating')) {
      return "Our sentiment analysis shows that products with the most positive reviews tend to have excellent customer service, durability, and value for money. The most common complaints are about shipping delays and complicated setup processes.";
    }

    // Help-related queries
    if (normalizedQuery.includes('help') || normalizedQuery.includes('how to')) {
      return "I can help you find products, compare sentiment across different brands, analyze review trends, and answer questions about our platform. What would you like to know more about?";
    }

    // Greeting
    if (normalizedQuery.includes('hi') || normalizedQuery.includes('hello') || normalizedQuery.includes('hey')) {
      return "Hello! I'm ProductWhisper's assistant. I can help you find information about products, analyze reviews, and compare different options. How can I assist you today?";
    }

    // Default response
    return "That's an interesting question. While I'm still learning, I can tell you that ProductWhisper analyzes thousands of reviews to help you make informed purchasing decisions. Is there something specific about our sentiment analysis you'd like to know?";
  }

  /**
   * Generate a response based on user input
   */
  async generateResponse(query: string): Promise<string> {
    // Safety check for empty query
    if (!query || typeof query !== 'string' || query.trim() === '') {
      return "I didn't catch that. Could you please ask your question again?";
    }

    const normalizedQuery = query.toLowerCase().trim();

    // Check cache first if enabled
    if (this.cacheEnabled) {
      const cachedResponse = this.cache[normalizedQuery];
      if (cachedResponse && (Date.now() - cachedResponse.timestamp < this.cacheDuration)) {
        return cachedResponse.response;
      }
    }

    // Find best match in FAQ data
    const match = this.findBestMatch(normalizedQuery);

    // Track for analytics
    this.trackQuestion(normalizedQuery, match);

    let response: string;

    if (match && match.answer) {
      response = match.answer;
    } else {
      try {
        // Try to get response from API
        response = await this.getResponseFromApi(normalizedQuery);
      } catch (error) {
        // Fallback responses for when API fails
        const fallbackResponses = [
          "I'm not sure I understand. Could you rephrase your question?",
          "I don't have information on that specific topic. Is there something else I can help with?",
          "That's a great question! Unfortunately, I don't have the answer right now. You can contact our support team for more detailed information.",
          "I'm still learning and don't have an answer for that yet. Would you like to know about our product features instead?",
          "I'm not able to provide information on that. Would you like to know about how ProductWhisper works?"
        ];

        response = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
      }
    }

    // Cache the response if caching is enabled
    if (this.cacheEnabled) {
      this.cache[normalizedQuery] = {
        response,
        timestamp: Date.now()
      };
      this.saveCache();
    }

    return response;
  }

  /**
   * Clear the chat cache
   */
  clearCache(): void {
    this.cache = {};
    this.saveCache();
  }

  /**
   * Get analytics data
   */
  getAnalytics(): ChatAnalytics {
    return { ...this.analytics };
  }

  /**
   * Enable or disable caching
   */
  setCacheEnabled(enabled: boolean): void {
    this.cacheEnabled = enabled;
  }

  /**
   * Set cache duration in milliseconds
   */
  setCacheDuration(duration: number): void {
    this.cacheDuration = duration;
  }

  /**
   * Submit feedback for a chat response
   * @param messageId - The ID of the message to provide feedback for
   * @param helpful - Whether the response was helpful
   */
  async submitFeedback(messageId: string, helpful: boolean): Promise<boolean> {
    try {
      const response = await apiService.post('/chat/feedback', {
        messageId,
        helpful
      });

      return response.data && response.data.success;
    } catch (error) {
      console.error('Error submitting chat feedback:', error);
      return false;
    }
  }
}

// Create and export a singleton instance
export const chatService = new ChatService();
