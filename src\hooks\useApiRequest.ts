import { useState, useCallback } from 'react';
import { ApiError } from '../services/apiError';

interface ApiRequestState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | Error | null;
}

interface UseApiRequestResult<T> extends ApiRequestState<T> {
  execute: (...args: any[]) => Promise<T | null>;
  reset: () => void;
}

/**
 * Hook to handle API requests with loading and error states
 * 
 * @param apiFunction The API function to call
 * @param initialData Initial data (optional)
 * @returns Object with data, loading, error states and execute/reset functions
 */
function useApiRequest<T>(
  apiFunction: (...args: any[]) => Promise<T>,
  initialData: T | null = null
): UseApiRequestResult<T> {
  const [state, setState] = useState<ApiRequestState<T>>({
    data: initialData,
    loading: false,
    error: null,
  });

  /**
   * Execute the API request
   */
  const execute = useCallback(
    async (...args: any[]): Promise<T | null> => {
      try {
        setState(prevState => ({ ...prevState, loading: true, error: null }));
        
        const result = await apiFunction(...args);
        
        setState({
          data: result,
          loading: false,
          error: null,
        });
        
        return result;
      } catch (error) {
        const apiError = error instanceof ApiError 
          ? error 
          : error instanceof Error
            ? new ApiError(error.message, undefined, null, null, error)
            : new ApiError('An unknown error occurred');
        
        setState({
          data: null,
          loading: false,
          error: apiError,
        });
        
        return null;
      }
    },
    [apiFunction]
  );

  /**
   * Reset the state
   */
  const reset = useCallback(() => {
    setState({
      data: initialData,
      loading: false,
      error: null,
    });
  }, [initialData]);

  return {
    ...state,
    execute,
    reset,
  };
}

export default useApiRequest;
