import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { ApiError } from '../middleware/errorHandler';
import { redditCacheService } from '../services/redditCacheService';
import logger from '../utils/logger';

/**
 * Get trending Reddit discussions
 */
export const getTrendingDiscussions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const category = req.query.category as string | undefined;
    const timeframe = (req.query.timeframe as string) || 'week';
    const limit = parseInt(req.query.limit as string) || 20;
    
    // Validate timeframe
    if (!['day', 'week', 'month'].includes(timeframe)) {
      throw new ApiError(
        StatusCodes.BAD_REQUEST,
        `Invalid timeframe. Must be one of: day, week, month`
      );
    }
    
    // Validate limit
    if (isNaN(limit) || limit < 1 || limit > 50) {
      throw new ApiError(
        StatusCodes.BAD_REQUEST,
        'Limit must be a number between 1 and 50'
      );
    }
    
    // Get trending discussions
    const trendingDiscussions = await redditCacheService.getTrendingDiscussions(
      category,
      timeframe,
      limit
    );
    
    res.status(StatusCodes.OK).json({
      success: true,
      category: category || 'all',
      timeframe,
      count: trendingDiscussions.length,
      data: trendingDiscussions
    });
  } catch (error) {
    logger.error('Error in getTrendingDiscussions controller:', error);
    next(error);
  }
};

/**
 * Get trending Reddit products
 */
export const getTrendingProducts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const category = req.query.category as string | undefined;
    const timeframe = (req.query.timeframe as string) || 'week';
    const limit = parseInt(req.query.limit as string) || 10;
    
    // Validate timeframe
    if (!['day', 'week', 'month'].includes(timeframe)) {
      throw new ApiError(
        StatusCodes.BAD_REQUEST,
        `Invalid timeframe. Must be one of: day, week, month`
      );
    }
    
    // Validate limit
    if (isNaN(limit) || limit < 1 || limit > 50) {
      throw new ApiError(
        StatusCodes.BAD_REQUEST,
        'Limit must be a number between 1 and 50'
      );
    }
    
    // Get trending products
    const trendingProducts = await redditCacheService.getTrendingProducts(
      category,
      timeframe,
      limit
    );
    
    res.status(StatusCodes.OK).json({
      success: true,
      category: category || 'all',
      timeframe,
      count: trendingProducts.length,
      data: trendingProducts
    });
  } catch (error) {
    logger.error('Error in getTrendingProducts controller:', error);
    next(error);
  }
};

/**
 * Clean up expired cache entries
 */
export const cleanupCache = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const deletedCount = await redditCacheService.cleanupExpiredCache();
    
    res.status(StatusCodes.OK).json({
      success: true,
      message: `Cleaned up ${deletedCount} expired cache entries`,
      deletedCount
    });
  } catch (error) {
    logger.error('Error in cleanupCache controller:', error);
    next(error);
  }
};
