import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, useInView } from 'framer-motion';
import { BusinessDetails } from '../components/business';
import { LoadingSpinner, ApiErrorFallback } from '../components/common';
import { ArrowLeft, Search, Building, BarChart2 } from 'lucide-react';
import {
  AnimatedCircles,
  ScrollFadeIn,
  GradientButton
} from '../components/ui';

const BusinessDetailsPage: React.FC = () => {
  const { source, id } = useParams<{ source: string; id: string }>();
  const navigate = useNavigate();

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showSentiment, setShowSentiment] = useState<boolean>(true);

  useEffect(() => {
    // Validate params
    if (!source || !id) {
      setError('Invalid business parameters');
      setLoading(false);
      return;
    }

    // Reset state when params change
    setLoading(true);
    setError(null);
  }, [source, id]);

  // Handle back button click
  const handleBack = () => {
    navigate(-1);
  };

  // Toggle sentiment analysis
  const toggleSentiment = () => {
    setShowSentiment(!showSentiment);
  };

  // References for animations
  const heroRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Check if sections are in view
  const heroInView = useInView(heroRef, { once: false, amount: 0.3 });
  const contentInView = useInView(contentRef, { once: false, amount: 0.3 });

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section with Premium Gradient */}
      <div
        ref={heroRef}
        className="relative bg-gradient-primary rounded-b-premium shadow-premium overflow-hidden mb-6"
      >
        {/* Animated background circles */}
        <AnimatedCircles
          variant="secondary"
          count={8}
          maxOpacity={0.25}
          animationStyle="mixed"
          blurAmount={1.5}
          speed="slow"
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 relative z-10">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <ScrollFadeIn
              direction="left"
              threshold={0.2}
              duration={0.5}
              distance={25}
            >
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={heroInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                transition={{ duration: 0.4 }}
                className="flex items-center mb-2 md:mb-0"
              >
                <GradientButton
                  variant="primary"
                  size="sm"
                  onClick={handleBack}
                  className="mr-3 bg-white/10 text-white hover:bg-white/20 py-2 px-3"
                  leftIcon={<ArrowLeft className="w-4 h-4" />}
                  aria-label="Go back"
                >
                  Back
                </GradientButton>
                <div>
                  <div className="flex items-center">
                    <Building className="text-white/80 mr-2 h-5 w-5" />
                    <h1 className="text-2xl sm:text-3xl font-bold text-white font-display">Business Details</h1>
                  </div>
                  <p className="text-white/80 text-sm sm:text-base">
                    View business information and reviews with sentiment analysis
                  </p>
                </div>
              </motion.div>
            </ScrollFadeIn>

            <ScrollFadeIn
              direction="right"
              threshold={0.2}
              duration={0.5}
              distance={25}
              delay={0.1}
            >
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={heroInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 20 }}
                transition={{ duration: 0.4, delay: 0.1 }}
                className="flex items-center space-x-2"
              >
                <GradientButton
                  variant={showSentiment ? "secondary" : "primary"}
                  size="sm"
                  onClick={toggleSentiment}
                  className={`py-2 px-3 ${showSentiment ? "" : "bg-white/10 text-white hover:bg-white/20"}`}
                  leftIcon={<BarChart2 className="w-4 h-4 mr-1" />}
                >
                  {showSentiment ? 'Hide Sentiment' : 'Show Sentiment'}
                </GradientButton>
                <GradientButton
                  variant="primary"
                  size="sm"
                  onClick={() => navigate('/business/search')}
                  className="bg-white/10 text-white hover:bg-white/20 py-2 px-3"
                  leftIcon={<Search className="w-4 h-4 mr-1" />}
                >
                  New Search
                </GradientButton>
              </motion.div>
            </ScrollFadeIn>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div ref={contentRef} className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-10">
        <ScrollFadeIn
          direction="up"
          className="space-y-5"
          threshold={0.1}
          duration={0.4}
          distance={25}
        >
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner size="large" text="Loading business details..." />
            </div>
          ) : error ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
            >
              <ApiErrorFallback error={error} retry={() => window.location.reload()} />
            </motion.div>
          ) : source && id ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={contentInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.4 }}
              className="rounded-premium overflow-hidden"
            >
              <BusinessDetails
                businessId={id}
                source={source as 'google' | 'facebook'}
                showSentiment={showSentiment}
              />
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4 }}
              className="text-center py-12 bg-white rounded-premium shadow-sm border border-gray-100"
            >
              <div className="w-16 h-16 mx-auto mb-3 bg-gray-50 rounded-full flex items-center justify-center">
                <Building className="w-8 h-8 text-gray-300" />
              </div>
              <h3 className="text-xl font-bold text-gray-700 font-display mb-3">Business information not available</h3>
              <p className="text-gray-500 mb-6 max-w-md mx-auto">
                We couldn't find the business details you're looking for. Please try another business or go back to search.
              </p>
              <GradientButton
                variant="primary"
                onClick={() => navigate('/business/search')}
                leftIcon={<Search className="w-4 h-4 mr-1" />}
                className="py-2 px-4"
              >
                Back to Search
              </GradientButton>
            </motion.div>
          )}
        </ScrollFadeIn>
      </div>
    </div>
  );
};

export default BusinessDetailsPage;


