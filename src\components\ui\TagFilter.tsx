import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from './badge';
import { X } from 'lucide-react';

interface TagFilterProps {
  tags: string[];
  selectedTags: string[];
  onChange: (tags: string[]) => void;
  maxDisplay?: number;
  className?: string;
}

const TagFilter: React.FC<TagFilterProps> = ({
  tags,
  selectedTags,
  onChange,
  maxDisplay = 20,
  className = '',
}) => {
  const [showAll, setShowAll] = React.useState(false);
  
  // Toggle a tag selection
  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onChange(selectedTags.filter(t => t !== tag));
    } else {
      onChange([...selectedTags, tag]);
    }
  };
  
  // Clear all selected tags
  const clearAll = () => {
    onChange([]);
  };
  
  // Determine which tags to display
  const displayTags = showAll ? tags : tags.slice(0, maxDisplay);
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
      },
    },
  };
  
  const tagVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Selected tags section */}
      {selectedTags.length > 0 && (
        <div className="mb-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-gray-700">Selected Tags:</span>
            <button
              onClick={clearAll}
              className="text-xs text-primary hover:text-primary-dark transition-colors"
            >
              Clear All
            </button>
          </div>
          <motion.div 
            className="flex flex-wrap gap-2"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {selectedTags.map(tag => (
              <motion.div
                key={`selected-${tag}`}
                variants={tagVariants}
                layout
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Badge
                  variant="secondary"
                  className="flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary hover:bg-primary/20 transition-colors cursor-pointer"
                  onClick={() => toggleTag(tag)}
                >
                  {tag}
                  <X size={12} className="ml-1" />
                </Badge>
              </motion.div>
            ))}
          </motion.div>
        </div>
      )}
      
      {/* All tags section */}
      <motion.div 
        className="flex flex-wrap gap-2"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {displayTags.map(tag => (
          <motion.div
            key={tag}
            variants={tagVariants}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            layout
          >
            <Badge
              variant={selectedTags.includes(tag) ? "secondary" : "outline"}
              className={`px-2 py-1 cursor-pointer transition-colors ${
                selectedTags.includes(tag)
                  ? "bg-primary/10 text-primary hover:bg-primary/20"
                  : "hover:bg-gray-100"
              }`}
              onClick={() => toggleTag(tag)}
            >
              {tag}
            </Badge>
          </motion.div>
        ))}
        
        {/* Show more/less button */}
        {tags.length > maxDisplay && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <Badge
              variant="outline"
              className="px-2 py-1 cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => setShowAll(!showAll)}
            >
              {showAll ? "Show Less" : `+${tags.length - maxDisplay} More`}
            </Badge>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};

export default TagFilter;
