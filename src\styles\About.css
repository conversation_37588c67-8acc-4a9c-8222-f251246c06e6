.about-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.about-hero {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 3rem 2rem;
  margin: 2rem 0;
  text-align: center;
}

.about-hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.about-hero p {
  font-size: 1.2rem;
  color: #666;
}

.about-content {
  margin: 3rem 0;
}

.about-section {
  margin-bottom: 3rem;
}

.about-section h2 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 0.5rem;
  display: inline-block;
}

.about-section p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1.5rem;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.team-member {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
}

.member-photo {
  height: 200px;
  background-color: #f0f0f0;
}

.team-member h3 {
  margin: 1rem 0 0.5rem;
  font-size: 1.2rem;
}

.team-member p {
  margin: 0 0 1rem;
  color: #666;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .about-hero {
    padding: 2rem 1rem;
  }
  
  .about-hero h1 {
    font-size: 2rem;
  }
  
  .team-grid {
    grid-template-columns: 1fr;
  }
}
