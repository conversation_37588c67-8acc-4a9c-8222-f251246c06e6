import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FiBarChart2, FiX, FiHelpCircle, FiMessageSquare, FiSearch, FiRefreshCw } from 'react-icons/fi';
import { useChat } from '../../contexts/ChatContext';

interface ChatAnalyticsProps {
  onClose: () => void;
}

const ChatAnalytics: React.FC<ChatAnalyticsProps> = ({ onClose }) => {
  const { getAnalytics, clearCache } = useChat();
  const [analytics, setAnalytics] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'questions' | 'faqs'>('questions');

  // Fetch analytics data
  useEffect(() => {
    const data = getAnalytics();
    setAnalytics(data);
  }, [getAnalytics]);

  // Handle refresh
  const handleRefresh = () => {
    const data = getAnalytics();
    setAnalytics(data);
  };

  // Handle clear cache
  const handleClearCache = () => {
    clearCache();
    handleRefresh();
  };

  // If no analytics data, show loading
  if (!analytics) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        className="fixed bottom-24 right-6 w-80 bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden z-50"
      >
        <div className="p-4 flex justify-between items-center border-b border-gray-100">
          <div className="flex items-center">
            <FiBarChart2 className="mr-2 text-primary" size={18} />
            <h3 className="font-medium text-gray-800">Chat Analytics</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close analytics"
          >
            <FiX size={18} />
          </button>
        </div>
        <div className="p-8 flex justify-center items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </motion.div>
    );
  }

  // Get top questions
  const topQuestions = Object.entries(analytics.questions || {})
    .sort(([, countA]: [string, any], [, countB]: [string, any]) => (countB as number) - (countA as number))
    .slice(0, 5);

  // Get top FAQs
  const topFaqs = Object.entries(analytics.matchedFaqs || {})
    .sort(([, countA]: [string, any], [, countB]: [string, any]) => (countB as number) - (countA as number))
    .slice(0, 5);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="fixed bottom-24 right-6 w-80 bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden z-50"
    >
      <div className="p-4 flex justify-between items-center border-b border-gray-100">
        <div className="flex items-center">
          <FiBarChart2 className="mr-2 text-primary" size={18} />
          <h3 className="font-medium text-gray-800">Chat Analytics</h3>
        </div>
        <div className="flex items-center">
          <button
            onClick={handleRefresh}
            className="text-gray-400 hover:text-primary transition-colors mr-2"
            aria-label="Refresh analytics"
          >
            <FiRefreshCw size={16} />
          </button>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close analytics"
          >
            <FiX size={18} />
          </button>
        </div>
      </div>

      <div className="border-b border-gray-100">
        <div className="flex">
          <button
            className={`flex-1 py-2 px-4 text-sm font-medium ${
              activeTab === 'questions'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('questions')}
          >
            <div className="flex items-center justify-center">
              <FiMessageSquare className="mr-1.5" size={14} />
              <span>Questions</span>
            </div>
          </button>
          <button
            className={`flex-1 py-2 px-4 text-sm font-medium ${
              activeTab === 'faqs'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('faqs')}
          >
            <div className="flex items-center justify-center">
              <FiHelpCircle className="mr-1.5" size={14} />
              <span>FAQs</span>
            </div>
          </button>
        </div>
      </div>

      <div className="p-4 max-h-80 overflow-y-auto">
        {activeTab === 'questions' ? (
          <>
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-gray-700">Top Questions</h4>
                <span className="text-xs text-gray-500">
                  Total: {analytics.totalQuestions || 0}
                </span>
              </div>
              {topQuestions.length > 0 ? (
                <div className="space-y-2">
                  {topQuestions.map(([question, count]: [string, any], index: number) => (
                    <div
                      key={index}
                      className="p-2 bg-gray-50 rounded-lg border border-gray-100 text-sm"
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex items-start">
                          <FiSearch className="mt-0.5 mr-1.5 text-gray-400 flex-shrink-0" size={14} />
                          <span className="text-gray-700 line-clamp-2">{question}</span>
                        </div>
                        <span className="ml-2 bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full text-xs">
                          {count}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500 text-sm">
                  No questions asked yet
                </div>
              )}
            </div>
            <div className="mt-4">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-gray-700">No Matches</h4>
                <span className="text-xs text-gray-500">
                  Count: {analytics.noMatchCount || 0}
                </span>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Questions without answers</span>
                  <span className="bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded-full text-xs">
                    {analytics.noMatchCount || 0}
                  </span>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium text-gray-700">Top Matched FAQs</h4>
              <span className="text-xs text-gray-500">
                Total: {Object.keys(analytics.matchedFaqs || {}).length}
              </span>
            </div>
            {topFaqs.length > 0 ? (
              <div className="space-y-2">
                {topFaqs.map(([faq, count]: [string, any], index: number) => (
                  <div
                    key={index}
                    className="p-2 bg-gray-50 rounded-lg border border-gray-100 text-sm"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-start">
                        <FiHelpCircle className="mt-0.5 mr-1.5 text-gray-400 flex-shrink-0" size={14} />
                        <span className="text-gray-700 line-clamp-2">{faq}</span>
                      </div>
                      <span className="ml-2 bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full text-xs">
                        {count}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500 text-sm">
                No FAQs matched yet
              </div>
            )}
          </div>
        )}
      </div>

      <div className="p-3 border-t border-gray-100 bg-gray-50">
        <button
          onClick={handleClearCache}
          className="w-full py-1.5 px-3 bg-white border border-gray-200 rounded-lg text-sm text-gray-700 hover:bg-gray-100 transition-colors"
        >
          Clear Chat Cache
        </button>
      </div>
    </motion.div>
  );
};

export default ChatAnalytics;
