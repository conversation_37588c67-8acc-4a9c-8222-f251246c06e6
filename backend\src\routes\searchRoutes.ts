import { Router } from 'express';
import * as searchController from '../controllers/searchController';

const router = Router();

/**
 * @swagger
 * /search:
 *   post:
 *     summary: Search for products
 *     tags: [Search]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - query
 *             properties:
 *               query:
 *                 type: string
 *                 description: Search query
 *               filters:
 *                 type: object
 *                 properties:
 *                   category:
 *                     type: string
 *                   brand:
 *                     type: string
 *                   minPrice:
 *                     type: number
 *                   maxPrice:
 *                     type: number
 *                   minRating:
 *                     type: number
 *     responses:
 *       200:
 *         description: Search results
 */
router.post('/', searchController.searchProducts);

/**
 * @swagger
 * /search/trending:
 *   get:
 *     summary: Get trending search terms
 *     tags: [Search]
 *     responses:
 *       200:
 *         description: Trending search terms
 */
router.get('/trending', searchController.getTrendingSearches);

export default router;
