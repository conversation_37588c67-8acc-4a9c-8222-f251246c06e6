import React from 'react';
import { motion } from 'framer-motion';
import { FiGlobe, FiMessageCircle, FiShoppingBag, FiVideo, FiSearch } from 'react-icons/fi';

export type DataSource = 'reddit' | 'tiktok' | 'google' | 'amazon' | 'all';

interface DataSourceSelectorProps {
  selectedSource: DataSource;
  onSourceChange: (source: DataSource) => void;
  className?: string;
}

/**
 * Component for selecting data sources for trend analysis
 */
const DataSourceSelector: React.FC<DataSourceSelectorProps> = ({
  selectedSource,
  onSourceChange,
  className = ''
}) => {
  // Data source options with icons and labels
  const sources = [
    { id: 'all', label: 'All Sources', icon: FiGlobe },
    { id: 'reddit', label: 'Reddit', icon: FiMessageCircle },
    { id: 'tiktok', label: 'TikTok', icon: FiVideo },
    { id: 'google', label: 'Google', icon: FiSearch },
    { id: 'amazon', label: 'Amazon', icon: FiShoppingBag }
  ];

  return (
    <div className={`bg-white rounded-xl shadow-md overflow-hidden ${className}`}>
      <div className="bg-gradient-to-r from-primary-dark to-primary p-4 text-white">
        <h3 className="text-lg font-semibold">Data Sources</h3>
        <p className="text-sm text-white/80 mt-1">
          Select a platform to view trend data
        </p>
      </div>
      
      <div className="p-4">
        <div className="flex flex-wrap gap-2">
          {sources.map((source) => {
            const isSelected = selectedSource === source.id;
            const Icon = source.icon;
            
            return (
              <motion.button
                key={source.id}
                onClick={() => onSourceChange(source.id as DataSource)}
                className={`flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  isSelected
                    ? 'bg-primary text-white shadow-sm'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
              >
                <Icon className="mr-2" size={16} />
                {source.label}
                
                {source.id !== 'reddit' && source.id !== 'all' && (
                  <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-gray-200 text-gray-700">
                    Coming Soon
                  </span>
                )}
              </motion.button>
            );
          })}
        </div>
        
        {selectedSource !== 'reddit' && selectedSource !== 'all' && (
          <div className="mt-4 p-3 bg-blue-50 text-blue-700 rounded-lg text-sm">
            <p>
              <strong>Note:</strong> {selectedSource.charAt(0).toUpperCase() + selectedSource.slice(1)} integration is coming soon. 
              Currently showing Reddit data as a preview.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataSourceSelector;
