version: '3.8'

services:
  # Backend API service
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=development
      - PORT=8000
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=productwhisper
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - REDIS_URL=redis://redis:6379
      - SENTIMENT_API_URL=http://nlp-service:5000
      - REDDIT_CLIENT_ID=H44nTyJUgV4waOKnC9YA9w
      - REDDIT_CLIENT_SECRET=VROpHb3GSWpRde66dru9X6cRYDwjgQ
      - REDDIT_USER_AGENT=ProductWhisper/1.0
      - CORS_ORIGIN=http://localhost:3000
    depends_on:
      - postgres
      - redis
      - nlp-service
    volumes:
      - ./backend:/app
      - /app/node_modules
      - api-logs:/app/logs
    restart: unless-stopped
    networks:
      - app-network

  # Python NLP service
  nlp-service:
    build:
      context: ./backend/python-nlp-service
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    volumes:
      - ./backend/python-nlp-service:/app
    restart: unless-stopped
    networks:
      - app-network

  # Frontend service (development mode)
  frontend:
    image: node:18-alpine
    command: sh -c "npm install && npm run dev"
    ports:
      - "3000:3000"
    working_dir: /app
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      - VITE_API_URL=http://localhost:8000/api
      - VITE_SENTIMENT_API_URL=http://localhost:5000
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - app-network

  # PostgreSQL database
  postgres:
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=productwhisper
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - app-network

  # Redis cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - app-network

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4
    ports:
      - "5050:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres-data:
  redis-data:
  api-logs:

networks:
  app-network:
    driver: bridge
