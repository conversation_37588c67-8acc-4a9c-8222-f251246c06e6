import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { apiService } from '../../services/api';
import { LoadingSpinner, ApiErrorFallback } from '../common';
import BusinessReviews from './BusinessReviews';
import {
  MapPin,
  Phone,
  Globe,
  Clock,
  Star,
  DollarSign,
  Users,
  MessageSquare,
  Image
} from 'lucide-react';

interface BusinessPhoto {
  reference: string;
  width: number;
  height: number;
  url: string;
}

interface BusinessHours {
  weekday_text: string[];
  open_now?: boolean;
}

interface SentimentMetrics {
  average_score: number;
  positive_count: number;
  negative_count: number;
  neutral_count: number;
  total_analyzed: number;
}

interface BusinessData {
  source: string;
  source_id: string;
  name: string;
  address: string;
  phone?: string;
  website?: string;
  opening_hours?: BusinessHours;
  rating?: number;
  user_ratings_total?: number;
  summary?: string;
  reviews?: any[];
  photos?: BusinessPhoto[];
  price_level?: number;
  types?: string[];
  url?: string;
  sentiment_metrics?: SentimentMetrics;
}

interface BusinessDetailsProps {
  businessId: string;
  source: 'google' | 'facebook';
  showReviews?: boolean;
  showSentiment?: boolean;
  className?: string;
}

/**
 * Component to display business details
 */
const BusinessDetails: React.FC<BusinessDetailsProps> = ({
  businessId,
  source,
  showReviews = true,
  showSentiment = true,
  className = '',
}) => {
  const [business, setBusiness] = useState<BusinessData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activePhoto, setActivePhoto] = useState<number>(0);

  useEffect(() => {
    const fetchBusinessDetails = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await apiService.get(
          `/business/${source}/${businessId}`,
          {
            params: {
              with_sentiment: showSentiment,
            },
          }
        );

        if (response.data) {
          setBusiness(response.data);
        } else {
          setError('No business data received');
        }
      } catch (err) {
        console.error('Error fetching business details:', err);
        setError('Failed to load business details. Please try again.');
        
        // Use mock data for development
        setBusiness(getMockBusinessData());
      } finally {
        setLoading(false);
      }
    };

    fetchBusinessDetails();
  }, [businessId, source, showSentiment]);

  // Generate mock business data for development
  const getMockBusinessData = (): BusinessData => {
    return {
      source: source,
      source_id: businessId,
      name: 'Sample Business',
      address: '123 Main Street, Anytown, AN 12345',
      phone: '+****************',
      website: 'https://example.com',
      opening_hours: {
        weekday_text: [
          'Monday: 9:00 AM – 8:00 PM',
          'Tuesday: 9:00 AM – 8:00 PM',
          'Wednesday: 9:00 AM – 8:00 PM',
          'Thursday: 9:00 AM – 8:00 PM',
          'Friday: 9:00 AM – 9:00 PM',
          'Saturday: 10:00 AM – 9:00 PM',
          'Sunday: 11:00 AM – 6:00 PM'
        ],
        open_now: true
      },
      rating: 4.2,
      user_ratings_total: 123,
      summary: 'Sample Business offers a wide selection of products with competitive prices and friendly service.',
      photos: [
        {
          reference: 'mock-photo-1',
          width: 800,
          height: 600,
          url: 'https://via.placeholder.com/800x600.png?text=Sample+Business+Photo+1'
        },
        {
          reference: 'mock-photo-2',
          width: 800,
          height: 600,
          url: 'https://via.placeholder.com/800x600.png?text=Sample+Business+Photo+2'
        }
      ],
      price_level: 2,
      types: ['store', 'point_of_interest', 'establishment'],
      url: 'https://maps.google.com/?cid=123456789',
      sentiment_metrics: {
        average_score: 0.65,
        positive_count: 8,
        negative_count: 2,
        neutral_count: 3,
        total_analyzed: 13
      }
    };
  };

  // Render price level
  const renderPriceLevel = (level?: number) => {
    if (!level) return null;
    
    return (
      <div className="flex items-center text-gray-600">
        {Array.from({ length: level }).map((_, i) => (
          <DollarSign key={i} className="w-4 h-4 fill-current" />
        ))}
        {Array.from({ length: 4 - level }).map((_, i) => (
          <DollarSign key={i + level} className="w-4 h-4 text-gray-300" />
        ))}
      </div>
    );
  };

  // Get sentiment color
  const getSentimentColor = (score?: number) => {
    if (score === undefined) return 'text-gray-400';
    if (score > 0.2) return 'text-green-500';
    if (score < -0.2) return 'text-red-500';
    return 'text-yellow-500';
  };

  if (loading) {
    return (
      <div className={`flex justify-center items-center p-8 ${className}`}>
        <LoadingSpinner size="medium" text="Loading business details..." />
      </div>
    );
  }

  if (error || !business) {
    return (
      <div className={`p-4 ${className}`}>
        <ApiErrorFallback error={error || 'Business not found'} onRetry={() => window.location.reload()} />
      </div>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Business Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-md overflow-hidden"
      >
        {/* Photo Gallery */}
        {business.photos && business.photos.length > 0 && (
          <div className="relative h-64 bg-gray-100">
            <img
              src={business.photos[activePhoto].url}
              alt={business.name}
              className="w-full h-full object-cover"
            />
            {business.photos.length > 1 && (
              <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                {business.photos.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActivePhoto(index)}
                    className={`w-2 h-2 rounded-full ${
                      activePhoto === index ? 'bg-white' : 'bg-white/50'
                    }`}
                    aria-label={`View photo ${index + 1}`}
                  />
                ))}
              </div>
            )}
          </div>
        )}

        {/* Business Info */}
        <div className="p-6">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{business.name}</h1>
              <div className="flex items-center mt-1 text-gray-600">
                {business.rating && (
                  <div className="flex items-center mr-3">
                    <Star className="w-4 h-4 text-yellow-400 fill-yellow-400 mr-1" />
                    <span className="font-medium">{business.rating.toFixed(1)}</span>
                    {business.user_ratings_total && (
                      <span className="text-sm text-gray-500 ml-1">
                        ({business.user_ratings_total})
                      </span>
                    )}
                  </div>
                )}
                {renderPriceLevel(business.price_level)}
              </div>
              {business.types && business.types.length > 0 && (
                <div className="mt-1 text-sm text-gray-500">
                  {business.types.map(type => type.replace(/_/g, ' ')).join(' • ')}
                </div>
              )}
            </div>
            {showSentiment && business.sentiment_metrics && (
              <div className="flex flex-col items-end">
                <div className={`text-lg font-bold ${getSentimentColor(business.sentiment_metrics.average_score)}`}>
                  {business.sentiment_metrics.average_score.toFixed(2)}
                </div>
                <div className="text-xs text-gray-500">Sentiment Score</div>
              </div>
            )}
          </div>

          {/* Summary */}
          {business.summary && (
            <p className="mt-4 text-gray-700">{business.summary}</p>
          )}

          {/* Contact Info */}
          <div className="mt-6 space-y-2">
            {business.address && (
              <div className="flex items-start">
                <MapPin className="w-5 h-5 text-gray-500 mr-2 mt-0.5" />
                <span>{business.address}</span>
              </div>
            )}
            {business.phone && (
              <div className="flex items-center">
                <Phone className="w-5 h-5 text-gray-500 mr-2" />
                <a href={`tel:${business.phone}`} className="text-primary hover:underline">
                  {business.phone}
                </a>
              </div>
            )}
            {business.website && (
              <div className="flex items-center">
                <Globe className="w-5 h-5 text-gray-500 mr-2" />
                <a
                  href={business.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline truncate max-w-xs"
                >
                  {business.website.replace(/^https?:\/\//, '')}
                </a>
              </div>
            )}
            {business.opening_hours && (
              <div className="flex items-start">
                <Clock className="w-5 h-5 text-gray-500 mr-2 mt-0.5" />
                <div>
                  <div className={business.opening_hours.open_now ? 'text-green-600' : 'text-red-600'}>
                    {business.opening_hours.open_now ? 'Open now' : 'Closed'}
                  </div>
                  <details className="mt-1">
                    <summary className="text-sm text-primary cursor-pointer">Hours</summary>
                    <ul className="mt-2 text-sm text-gray-600 space-y-1">
                      {business.opening_hours.weekday_text.map((day, index) => (
                        <li key={index}>{day}</li>
                      ))}
                    </ul>
                  </details>
                </div>
              </div>
            )}
          </div>
        </div>
      </motion.div>

      {/* Reviews Section */}
      {showReviews && (
        <div className="mt-8">
          <BusinessReviews
            businessId={businessId}
            source={source}
            showSentiment={showSentiment}
            limit={10}
          />
        </div>
      )}
    </div>
  );
};

export default BusinessDetails;
