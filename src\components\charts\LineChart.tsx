import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface DataPoint {
  date: string;
  value: number;
  confidence?: number;
}

interface LineChartProps {
  data: DataPoint[];
  height?: number;
  width?: string;
  lineColor?: string;
  areaColor?: string;
  showConfidence?: boolean;
  showTooltip?: boolean;
  showAxis?: boolean;
  animate?: boolean;
  className?: string;
  formatValue?: (value: number) => string;
  formatDate?: (date: string) => string;
}

const LineChart: React.FC<LineChartProps> = ({
  data,
  height = 200,
  width = '100%',
  lineColor = '#3b82f6',
  areaColor = 'rgba(59, 130, 246, 0.1)',
  showConfidence = false,
  showTooltip = true,
  showAxis = true,
  animate = true,
  className = '',
  formatValue = (value) => value.toFixed(2),
  formatDate = (date) => new Date(date).toLocaleDateString(),
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [activePoint, setActivePoint] = React.useState<DataPoint | null>(null);
  const [tooltipPosition, setTooltipPosition] = React.useState({ x: 0, y: 0 });

  // Sort data by date
  const sortedData = [...data].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // Find min and max values
  const minValue = Math.min(...sortedData.map(d => d.value));
  const maxValue = Math.max(...sortedData.map(d => d.value));
  
  // Add padding to min and max values
  const paddedMinValue = Math.max(0, minValue - (maxValue - minValue) * 0.1);
  const paddedMaxValue = maxValue + (maxValue - minValue) * 0.1;

  // Calculate dimensions
  const margin = { top: 20, right: 20, bottom: 30, left: 40 };
  const chartWidth = 1000 - margin.left - margin.right;
  const chartHeight = height - margin.top - margin.bottom;

  // Create scales
  const xScale = (index: number) => (index / (sortedData.length - 1)) * chartWidth;
  const yScale = (value: number) => chartHeight - ((value - paddedMinValue) / (paddedMaxValue - paddedMinValue)) * chartHeight;

  // Generate path data
  const linePath = sortedData.map((d, i) => {
    const x = xScale(i);
    const y = yScale(d.value);
    return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
  }).join(' ');

  // Generate area path data
  const areaPath = `
    ${sortedData.map((d, i) => {
      const x = xScale(i);
      const y = yScale(d.value);
      return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
    }).join(' ')}
    L ${xScale(sortedData.length - 1)} ${chartHeight}
    L ${xScale(0)} ${chartHeight}
    Z
  `;

  // Generate confidence area path data if needed
  const confidenceAreaPath = showConfidence ? sortedData.map((d, i) => {
    const x = xScale(i);
    const confidence = d.confidence || 0.1;
    const confidenceRange = (1 - confidence) * (maxValue - minValue) * 0.5;
    const y1 = yScale(Math.min(paddedMaxValue, d.value + confidenceRange));
    const y2 = yScale(Math.max(paddedMinValue, d.value - confidenceRange));
    return i === 0 
      ? `M ${x} ${y1} L ${x} ${y2}`
      : `L ${x} ${y1} L ${x} ${y2}`;
  }).join(' ') : '';

  // Handle mouse movement for tooltip
  const handleMouseMove = (event: React.MouseEvent<SVGSVGElement>) => {
    if (!showTooltip || !svgRef.current) return;
    
    const svgRect = svgRef.current.getBoundingClientRect();
    const mouseX = event.clientX - svgRect.left - margin.left;
    
    // Find closest data point
    const index = Math.min(
      Math.max(0, Math.round((mouseX / chartWidth) * (sortedData.length - 1))),
      sortedData.length - 1
    );
    
    const point = sortedData[index];
    setActivePoint(point);
    
    const x = xScale(index) + margin.left;
    const y = yScale(point.value) + margin.top;
    
    setTooltipPosition({ x, y });
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    setActivePoint(null);
  };

  return (
    <div className={`relative ${className}`} style={{ width }}>
      <svg
        ref={svgRef}
        width="100%"
        height={height}
        viewBox={`0 0 ${chartWidth + margin.left + margin.right} ${chartHeight + margin.top + margin.bottom}`}
        preserveAspectRatio="none"
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        className="overflow-visible"
      >
        <g transform={`translate(${margin.left}, ${margin.top})`}>
          {/* X and Y axis */}
          {showAxis && (
            <>
              {/* X axis */}
              <line
                x1={0}
                y1={chartHeight}
                x2={chartWidth}
                y2={chartHeight}
                stroke="#e5e7eb"
                strokeWidth={1}
              />
              
              {/* Y axis */}
              <line
                x1={0}
                y1={0}
                x2={0}
                y2={chartHeight}
                stroke="#e5e7eb"
                strokeWidth={1}
              />
              
              {/* X axis ticks */}
              {sortedData.filter((_, i) => i % Math.ceil(sortedData.length / 5) === 0).map((d, i) => (
                <g key={`x-tick-${i}`} transform={`translate(${xScale(i * Math.ceil(sortedData.length / 5))}, ${chartHeight})`}>
                  <line y2={5} stroke="#e5e7eb" />
                  <text
                    dy="1em"
                    fontSize="10"
                    textAnchor="middle"
                    fill="#6b7280"
                  >
                    {formatDate(d.date)}
                  </text>
                </g>
              ))}
              
              {/* Y axis ticks */}
              {[0, 0.25, 0.5, 0.75, 1].map((t) => {
                const value = paddedMinValue + t * (paddedMaxValue - paddedMinValue);
                return (
                  <g key={`y-tick-${t}`} transform={`translate(0, ${yScale(value)})`}>
                    <line x2={-5} stroke="#e5e7eb" />
                    <text
                      dx="-0.5em"
                      dy="0.3em"
                      fontSize="10"
                      textAnchor="end"
                      fill="#6b7280"
                    >
                      {formatValue(value)}
                    </text>
                    <line
                      x1={0}
                      y1={0}
                      x2={chartWidth}
                      y2={0}
                      stroke="#e5e7eb"
                      strokeWidth={1}
                      strokeDasharray="4 4"
                    />
                  </g>
                );
              })}
            </>
          )}
          
          {/* Confidence area */}
          {showConfidence && (
            <path
              d={confidenceAreaPath}
              fill={areaColor}
              opacity={0.3}
            />
          )}
          
          {/* Area under the line */}
          <motion.path
            d={areaPath}
            fill={areaColor}
            initial={animate ? { opacity: 0 } : { opacity: 0.2 }}
            animate={{ opacity: 0.2 }}
            transition={{ duration: 1 }}
          />
          
          {/* Line */}
          <motion.path
            d={linePath}
            fill="none"
            stroke={lineColor}
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
            initial={animate ? { pathLength: 0 } : { pathLength: 1 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1.5, ease: "easeInOut" }}
          />
          
          {/* Data points */}
          {sortedData.map((d, i) => (
            <motion.circle
              key={`point-${i}`}
              cx={xScale(i)}
              cy={yScale(d.value)}
              r={activePoint === d ? 5 : 3}
              fill={activePoint === d ? lineColor : "white"}
              stroke={lineColor}
              strokeWidth={2}
              initial={animate ? { opacity: 0, scale: 0 } : { opacity: 1, scale: 1 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: animate ? 1 + (i / sortedData.length) * 0.5 : 0 }}
            />
          ))}
          
          {/* Active point indicator */}
          {activePoint && (
            <line
              x1={xScale(sortedData.findIndex(d => d === activePoint))}
              y1={0}
              x2={xScale(sortedData.findIndex(d => d === activePoint))}
              y2={chartHeight}
              stroke={lineColor}
              strokeWidth={1}
              strokeDasharray="4 4"
            />
          )}
        </g>
      </svg>
      
      {/* Tooltip */}
      {showTooltip && activePoint && (
        <div
          ref={tooltipRef}
          className="absolute bg-white shadow-lg rounded-md px-3 py-2 text-sm pointer-events-none z-10 border border-gray-200"
          style={{
            left: `${tooltipPosition.x}px`,
            top: `${tooltipPosition.y - 40}px`,
            transform: 'translateX(-50%)',
          }}
        >
          <div className="font-medium">{formatDate(activePoint.date)}</div>
          <div className="flex items-center justify-between gap-2">
            <span className="text-gray-600">Value:</span>
            <span className="font-semibold">{formatValue(activePoint.value)}</span>
          </div>
          {showConfidence && activePoint.confidence && (
            <div className="flex items-center justify-between gap-2">
              <span className="text-gray-600">Confidence:</span>
              <span className="font-semibold">{(activePoint.confidence * 100).toFixed(0)}%</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LineChart;
