import React, { useState, useEffect, useRef } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { motion, useInView } from 'framer-motion';
import { FiBarChart2, FiCheck, FiX, FiAlertCircle, FiPlus, FiSearch, FiArrowRight, FiStar } from 'react-icons/fi';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { apiService } from '../services/api';
import { useToast } from '../components/common/Toast';
import { AnimatedCircles, ScrollFadeIn, GradientButton } from '../components/ui';

interface Product {
  id: number;
  name: string;
  brand: string;
  category: string;
  description: string;
  price: number;
  rating: number;
  reviewCount: number;
  sentimentScore: number;
  features: {
    [key: string]: {
      value: string;
      score?: number;
    };
  };
  pros: string[];
  cons: string[];
}

const ComparisonPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { showToast } = useToast();

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [features, setFeatures] = useState<string[]>([]);

  // Search functionality
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);

        // Get product IDs from URL and validate them
        const productIdsParam = searchParams.get('ids');

        if (!productIdsParam) {
          setError('No products selected for comparison');
          setLoading(false);
          return;
        }

        // Parse and validate product IDs
        const productIds = productIdsParam
          .split(',')
          .filter(id => id && /^\d+$/.test(id)) // Ensure IDs are numeric
          .map(id => id.trim());

        if (productIds.length < 2) {
          setError('Please select at least two products to compare');
          setLoading(false);
          return;
        }

        if (productIds.length > 4) {
          // Limit to 4 products for better UX
          showToast({
            type: 'warning',
            title: 'Comparison Limit',
            message: 'Maximum 4 products can be compared at once. Showing the first 4.'
          });
          productIds.splice(4); // Keep only the first 4
        }

        try {
          // Use the API service to fetch product details with proper error handling
          const productPromises = productIds.map(id => {
            const parsedId = parseInt(id);
            if (isNaN(parsedId)) {
              throw new Error(`Invalid product ID: ${id}`);
            }
            return apiService.getProductDetails(parsedId)
              .catch(err => {
                console.error(`Error fetching product ${id}:`, err);
                // Return null for failed products, we'll filter them out later
                return null;
              });
          });

          const productsData = await Promise.all(productPromises);

          // Filter out any null results from failed API calls
          const validProductsData = productsData.filter(product => product !== null) as Product[];

          if (validProductsData.length < 2) {
            throw new Error('Could not retrieve enough valid products for comparison');
          }

          setProducts(validProductsData);

          // Extract all unique features with null checks
          const allFeatures = new Set<string>();
          validProductsData.forEach(product => {
            if (product?.features && typeof product.features === 'object') {
              Object.keys(product.features).forEach(feature => {
                if (feature) allFeatures.add(feature);
              });
            }
          });

          setFeatures(Array.from(allFeatures));
        } catch (apiError) {
          console.error('API error:', apiError);
          setError('Failed to fetch product details from the API');

          // Fallback to mock data for development
          const mockProducts: Product[] = [
            {
              id: 1,
              name: "Premium Wireless Headphones",
              brand: "SoundMaster",
              category: "Electronics",
              description: "Experience crystal-clear audio with our premium wireless headphones.",
              price: 249.99,
              rating: 4.7,
              reviewCount: 1243,
              sentimentScore: 0.85,
              features: {
                "Sound Quality": { value: "Excellent", score: 0.92 },
                "Battery Life": { value: "30 hours", score: 0.88 },
                "Noise Cancellation": { value: "Active", score: 0.90 },
                "Connectivity": { value: "Bluetooth 5.0", score: 0.75 },
                "Comfort": { value: "Memory foam", score: 0.82 },
                "Durability": { value: "High", score: 0.78 },
                "Water Resistance": { value: "IPX4", score: 0.65 }
              },
              pros: ["Exceptional sound clarity", "Long battery life", "Comfortable for extended use"],
              cons: ["Expensive", "Occasional Bluetooth connectivity issues"]
            },
            {
              id: 2,
              name: "Ultra Noise Cancelling Headphones",
              brand: "AudioPro",
              category: "Electronics",
              description: "Block out the world with our advanced noise cancelling technology.",
              price: 299.99,
              rating: 4.5,
              reviewCount: 876,
              sentimentScore: 0.82,
              features: {
                "Sound Quality": { value: "Very Good", score: 0.85 },
                "Battery Life": { value: "25 hours", score: 0.80 },
                "Noise Cancellation": { value: "Advanced", score: 0.95 },
                "Connectivity": { value: "Bluetooth 5.1", score: 0.88 },
                "Comfort": { value: "Protein leather", score: 0.75 },
                "Durability": { value: "Medium", score: 0.70 },
                "Water Resistance": { value: "IPX5", score: 0.78 }
              },
              pros: ["Best-in-class noise cancellation", "Premium build quality", "Great app support"],
              cons: ["Heavy", "Less comfortable for long sessions", "Higher price point"]
            },
            {
              id: 3,
              name: "Sport Wireless Earbuds",
              brand: "FitSound",
              category: "Electronics",
              description: "Designed for active lifestyles with secure fit and water resistance.",
              price: 179.99,
              rating: 4.3,
              reviewCount: 2156,
              sentimentScore: 0.79,
              features: {
                "Sound Quality": { value: "Good", score: 0.78 },
                "Battery Life": { value: "8 hours (24 with case)", score: 0.82 },
                "Noise Cancellation": { value: "Passive", score: 0.60 },
                "Connectivity": { value: "Bluetooth 5.0", score: 0.85 },
                "Comfort": { value: "Silicone tips", score: 0.88 },
                "Durability": { value: "Very High", score: 0.92 },
                "Water Resistance": { value: "IPX7", score: 0.95 }
              },
              pros: ["Excellent for workouts", "Secure fit", "Fully waterproof"],
              cons: ["Average sound quality", "Limited noise isolation"]
            }
          ];

          // Filter products based on IDs from URL with validation
          const filteredProducts = mockProducts.filter(product =>
            product && productIds.includes(product.id?.toString())
          );

          if (filteredProducts.length < 2) {
            setError('Not enough valid products found for comparison');
            setLoading(false);
            return;
          }

          setProducts(filteredProducts);

          // Extract all unique features with null checks
          const allFeatures = new Set<string>();
          filteredProducts.forEach(product => {
            if (product?.features && typeof product.features === 'object') {
              Object.keys(product.features).forEach(feature => {
                if (feature) allFeatures.add(feature);
              });
            }
          });

          setFeatures(Array.from(allFeatures));
        }
      } catch (err) {
        setError('Failed to load product data for comparison');
        console.error('Error fetching products for comparison:', err);
        showToast('error', 'Could not load comparison data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [searchParams, showToast]);

  // Search for products to add to comparison
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!searchQuery.trim()) return;

    setIsSearching(true);

    try {
      const response = await apiService.searchProducts(searchQuery);
      setSearchResults(response.products || []);
    } catch (error) {
      console.error('Error searching products:', error);
      showToast('error', 'Failed to search products');

      // Fallback mock search results for development
      setSearchResults([
        {
          id: 4,
          name: "Budget Wireless Headphones",
          brand: "ValueAudio",
          category: "Electronics",
          description: "Affordable wireless headphones with decent sound quality.",
          price: 79.99,
          rating: 4.0,
          reviewCount: 3421,
          sentimentScore: 0.72,
          features: {},
          pros: ["Affordable", "Good battery life"],
          cons: ["Average sound quality", "Basic build quality"]
        },
        {
          id: 5,
          name: "Professional Studio Headphones",
          brand: "ProSound",
          category: "Electronics",
          description: "Professional-grade studio headphones for audio production.",
          price: 349.99,
          rating: 4.8,
          reviewCount: 567,
          sentimentScore: 0.91,
          features: {},
          pros: ["Exceptional sound accuracy", "Durable build"],
          cons: ["Expensive", "Requires amplifier for best performance"]
        }
      ]);
    } finally {
      setIsSearching(false);
    }
  };

  // Add product to comparison
  const addProduct = (product: Product) => {
    if (products.length >= 4) {
      showToast({ type: 'warning', message: 'You can compare up to 4 products at a time' });
      return;
    }

    if (products.some(p => p.id === product.id)) {
      showToast({
        type: 'info', message: 'This product is already in your comparison',
        title: ''
      });
      return;
    }

    // Get current product IDs and add the new one
    const currentIds = searchParams.get('ids')?.split(',').filter(id => id) || [];
    const newIds = [...currentIds, product.id.toString()];

    // Update URL with new product IDs
    setSearchParams({ ids: newIds.join(',') });

    // Clear search
    setSearchQuery('');
    setSearchResults([]);
    setShowSearch(false);
  };

  // Remove product from comparison
  const removeProduct = (productId: number) => {
    // Get current product IDs and remove the specified one
    const currentIds = searchParams.get('ids')?.split(',').filter(id => id) || [];
    const newIds = currentIds.filter(id => parseInt(id) !== productId);

    if (newIds.length < 2) {
      // If less than 2 products remain, show a message
      showToast('info', 'At least two products are required for comparison');
      return;
    }

    // Update URL with remaining product IDs
    setSearchParams({ ids: newIds.join(',') });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <LoadingSpinner size="large" text="Loading comparison data..." />
      </div>
    );
  }

  if (error || products.length < 2) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <FiAlertCircle className="mr-2" />
            {error || "Please select at least two products to compare"}
          </p>
        </div>
      </div>
    );
  }

  // Helper function to get sentiment color
  const getSentimentColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-green-500';
    if (score >= 0.4) return 'text-yellow-500';
    if (score >= 0.2) return 'text-orange-500';
    return 'text-red-500';
  };

  // References for animations
  const heroRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Check if sections are in view
  const heroInView = useInView(heroRef, { once: false, amount: 0.3 });
  const contentInView = useInView(contentRef, { once: false, amount: 0.3 });

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section with Premium Gradient */}
        <div
          ref={heroRef}
          className="relative bg-gradient-primary rounded-premium shadow-premium overflow-hidden mb-8"
        >
          {/* Animated background circles */}
          <AnimatedCircles
            variant="secondary"
            count={8}
            maxOpacity={0.25}
            animationStyle="mixed"
            blurAmount={1.5}
            speed="slow"
          />

          <div className="relative z-10 px-6 py-8 sm:px-10 sm:py-10">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <ScrollFadeIn
                direction="left"
                threshold={0.2}
                duration={0.5}
                distance={25}
              >
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={heroInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="flex items-center mb-3">
                    <motion.div
                      className="flex items-center justify-center h-14 w-14 rounded-full bg-white/20 text-white mb-3 relative z-10 mr-4"
                      whileHover={{ scale: 1.05 }}
                    >
                      <FiBarChart2 className="h-7 w-7" />
                    </motion.div>
                    <div>
                      <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white font-display">Product Comparison</h1>
                      <p className="text-white/80 text-sm sm:text-base md:text-lg mt-1">
                        Compare features, specs, and sentiment across multiple products
                      </p>
                    </div>
                  </div>
                </motion.div>
              </ScrollFadeIn>

              {/* Add product button */}
              {products.length < 4 && (
                <ScrollFadeIn
                  direction="right"
                  threshold={0.2}
                  duration={0.5}
                  distance={25}
                  delay={0.1}
                >
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={heroInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 20 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    <GradientButton
                      variant="secondary"
                      onClick={() => setShowSearch(true)}
                      className="mt-4 md:mt-0 bg-white text-primary hover:bg-gray-50"
                      leftIcon={<FiPlus size={18} />}
                    >
                      Add Product
                    </GradientButton>
                  </motion.div>
                </ScrollFadeIn>
              )}
            </div>
          </div>
        </div>

        {/* Currently comparing section */}
        <ScrollFadeIn
          direction="up"
          threshold={0.1}
          duration={0.4}
          distance={20}
          delay={0.1}
        >
          <motion.div
            ref={contentRef}
            initial={{ opacity: 0, y: 20 }}
            animate={contentInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.4 }}
            className="bg-white/90 backdrop-blur-sm rounded-premium shadow-premium border border-gray-100 p-5 mb-6"
          >
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
              <div className="flex items-center mb-3 sm:mb-0">
                <span className="font-medium text-lg text-gray-800 font-display">Currently Comparing:</span>
                <span className="ml-2 px-3 py-1 bg-primary/10 text-primary text-sm font-medium rounded-full flex items-center">
                  <FiBarChart2 className="mr-1.5" size={14} />
                  {products.length} Products
                </span>
              </div>

              {products.length >= 2 && (
                <div className="text-sm text-gray-500 italic">
                  You can compare up to 4 products at a time
                </div>
              )}
            </div>

            <div className="flex flex-wrap gap-2">
              {products.map(product => (
                <motion.div
                  key={`chip-${product.id}`}
                  className="flex items-center bg-gray-50 hover:bg-gray-100 rounded-full pl-3 pr-1 py-1 text-sm border border-gray-200 shadow-sm"
                  whileHover={{ y: -2, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  <span className="mr-1 font-medium text-gray-700">{product.name}</span>
                  <motion.button
                    onClick={() => removeProduct(product.id)}
                    className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 hover:bg-gray-300 hover:text-red-500 transition-colors ml-1"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <FiX size={14} />
                  </motion.button>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </ScrollFadeIn>

        {/* Search modal */}
        {showSearch && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.3, type: "spring", stiffness: 500, damping: 30 }}
              className="bg-white rounded-premium shadow-premium w-full max-w-2xl overflow-hidden border border-gray-100"
            >
              <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-primary/5 to-secondary/5">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="bg-primary/10 p-2 rounded-full mr-3">
                      <FiPlus className="text-primary" size={20} />
                    </div>
                    <h2 className="text-xl font-display font-bold text-gray-900">Add Product to Compare</h2>
                  </div>
                  <motion.button
                    onClick={() => {
                      setShowSearch(false);
                      setSearchQuery('');
                      setSearchResults([]);
                    }}
                    className="w-8 h-8 rounded-full flex items-center justify-center text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors"
                    whileHover={{ scale: 1.1, backgroundColor: "#f3f4f6" }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FiX size={20} />
                  </motion.button>
                </div>
              </div>

              <div className="p-6">
                <form onSubmit={handleSearch} className="mb-6">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search for a product..."
                      className="w-full pl-12 pr-24 py-3.5 bg-gray-50 border border-gray-200 rounded-premium focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/30 shadow-sm transition-all duration-200"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <FiSearch size={18} />
                    </div>
                    <GradientButton
                      type="submit"
                      variant="primary"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 py-2"
                      size="sm"
                    >
                      Search
                    </GradientButton>
                  </div>
                </form>

                <div className="max-h-96 overflow-y-auto rounded-premium">
                  {isSearching ? (
                    <div className="flex justify-center py-10">
                      <LoadingSpinner size="medium" text="Searching products..." />
                    </div>
                  ) : searchResults.length > 0 ? (
                    <motion.ul
                      className="divide-y divide-gray-100 bg-gray-50 rounded-premium border border-gray-100"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      {searchResults.map((product, index) => (
                        <motion.li
                          key={product.id}
                          className="p-4 hover:bg-white transition-colors duration-200"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          whileHover={{ backgroundColor: "white", boxShadow: "0 2px 4px rgba(0,0,0,0.05)" }}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <h3 className="text-lg font-medium text-gray-900 font-display">{product.name}</h3>
                              <div className="flex items-center mt-1.5">
                                <p className="text-sm text-gray-500 mr-3">{product.brand}</p>
                                <div className="flex items-center text-sm bg-yellow-50 px-2 py-0.5 rounded-full">
                                  <span className="text-yellow-400 mr-1">★</span>
                                  <span className="text-gray-700 font-medium">{product.rating.toFixed(1)}</span>
                                </div>
                                <div className="ml-2 text-xs text-gray-500">
                                  {product.reviewCount} reviews
                                </div>
                              </div>
                            </div>
                            <GradientButton
                              variant="primary"
                              size="sm"
                              onClick={() => addProduct(product)}
                              className="py-1.5"
                              leftIcon={<FiPlus size={14} />}
                            >
                              Add
                            </GradientButton>
                          </div>
                        </motion.li>
                      ))}
                    </motion.ul>
                  ) : searchQuery ? (
                    <motion.div
                      className="text-center py-10 px-4 bg-gray-50 rounded-premium border border-gray-100"
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4 shadow-sm">
                        <FiSearch className="text-gray-400" size={24} />
                      </div>
                      <h3 className="text-lg font-medium text-gray-700 font-display mb-2">No products found</h3>
                      <p className="text-gray-500 mb-4">No products found matching "{searchQuery}"</p>
                      <p className="text-sm text-gray-400">Try a different search term or browse popular products</p>
                    </motion.div>
                  ) : (
                    <motion.div
                      className="text-center py-10 px-4 bg-gray-50 rounded-premium border border-gray-100"
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-white mb-4 shadow-sm">
                        <FiSearch className="text-primary/60" size={24} />
                      </div>
                      <h3 className="text-lg font-medium text-gray-700 font-display mb-2">Find Products to Compare</h3>
                      <p className="text-gray-500 mb-2">Search for products to add to your comparison</p>
                      <p className="text-sm text-gray-400">Try searching for product names, brands, or categories</p>
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        )}

        <ScrollFadeIn
          direction="up"
          threshold={0.1}
          duration={0.4}
          distance={20}
          delay={0.2}
        >
          <motion.div
            className="bg-white rounded-premium shadow-premium border border-gray-100 overflow-hidden mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {/* Product Headers */}
            <div className="grid grid-cols-1 md:grid-cols-[220px_repeat(auto-fill,minmax(180px,1fr))] lg:grid-cols-[220px_repeat(auto-fill,minmax(220px,1fr))]">
              <div className="bg-gradient-to-r from-primary/5 to-transparent p-6 font-medium text-gray-800 border-b border-gray-100">
                <div className="flex items-center">
                  <div className="bg-primary/10 p-1.5 rounded-full mr-2.5">
                    <FiBarChart2 className="text-primary" size={16} />
                  </div>
                  <h3 className="text-lg font-display font-bold">Product Details</h3>
                </div>
              </div>
              {products.map((product, index) => (
                <motion.div
                  key={product.id}
                  className="p-5 sm:p-6 border-l border-b border-gray-100 relative bg-white hover:bg-gray-50/50 transition-colors duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.1 * index }}
                  whileHover={{ backgroundColor: "#f9fafb" }}
                >
                  {/* Product Image */}
                  <motion.div
                    className="relative w-full h-40 sm:h-48 bg-gray-100 rounded-premium mb-4 overflow-hidden shadow-sm border border-gray-200"
                    whileHover={{ y: -5, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  >
                    <img
                      src={`https://source.unsplash.com/random/300x300/?${product.category?.toLowerCase() || 'product'}`}
                      alt={product.name || 'Product image'}
                      className="w-full h-full object-cover"
                      loading="lazy"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x300?text=Product';
                      }}
                    />
                    {/* Remove button with animation */}
                    <motion.button
                      onClick={() => removeProduct(product.id)}
                      className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm p-1.5 rounded-full text-gray-600 hover:text-red-500 hover:bg-white transition-colors shadow-sm"
                      aria-label="Remove product"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <FiX size={16} />
                    </motion.button>
                  </motion.div>

                  <h2 className="font-bold text-lg sm:text-xl mb-1 pr-6 text-gray-900 line-clamp-2 font-display">{product.name}</h2>
                  <p className="text-gray-600 text-sm mb-3">{product.brand || 'Unknown'} • {product.category || 'Uncategorized'}</p>

                  <div className="flex items-center justify-between mb-3">
                    <span className="font-bold text-lg sm:text-xl text-primary">${product.price?.toFixed(2) || '0.00'}</span>
                    <motion.div
                      className={`px-3 py-1.5 rounded-full text-xs sm:text-sm font-medium ${
                        (product.sentimentScore || 0) > 0.7 ? 'bg-green-100 text-green-700 border border-green-200' :
                        (product.sentimentScore || 0) > 0.4 ? 'bg-yellow-100 text-yellow-700 border border-yellow-200' :
                        'bg-red-100 text-red-700 border border-red-200'
                      }`}
                      whileHover={{ y: -2, boxShadow: "0 2px 4px rgba(0,0,0,0.05)" }}
                    >
                      <div className="flex items-center">
                        <FiBarChart2 className="mr-1.5" size={14} />
                        <span>{Math.round((product.sentimentScore || 0) * 100)}% Sentiment</span>
                      </div>
                    </motion.div>
                  </div>

                  <div className="flex items-center">
                    <div className="flex items-center bg-yellow-50 px-2.5 py-1 rounded-full border border-yellow-100">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <span key={star} className={star <= Math.round(product.rating || 0) ? "text-yellow-400" : "text-gray-300"}>★</span>
                        ))}
                      </div>
                      <span className="text-gray-700 text-xs sm:text-sm ml-2 font-medium">({(product.reviewCount || 0).toLocaleString()})</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Features Comparison with Highlighting */}
            <div className="border-b border-gray-100">
              <div className="grid grid-cols-1 md:grid-cols-[220px_repeat(auto-fill,minmax(220px,1fr))]">
                <div className="p-6 font-medium text-gray-800 bg-gradient-to-r from-primary/5 to-transparent">
                  <div className="flex items-center">
                    <div className="bg-primary/10 p-1.5 rounded-full mr-2.5">
                      <FiStar className="text-primary" size={16} />
                    </div>
                    <h3 className="text-lg font-display font-bold">Key Features</h3>
                  </div>
                </div>
                <div className="col-span-full md:col-auto"></div>
              </div>

              {features.map((feature, featureIndex) => {
                // Find the best score for this feature across all products
                const featureScores = products
                  .map(product => product.features?.[feature]?.score || 0)
                  .filter(score => score > 0);

                const bestScore = featureScores.length > 0 ? Math.max(...featureScores) : 0;
                const worstScore = featureScores.length > 0 ? Math.min(...featureScores) : 0;

                // Determine if this is a significant feature (high variance in scores)
                const scoreVariance = bestScore - worstScore;
                const isSignificantFeature = scoreVariance > 0.2 && bestScore > 0.6;

                return (
                  <motion.div
                    key={feature}
                    className={`grid grid-cols-1 md:grid-cols-[220px_repeat(auto-fill,minmax(220px,1fr))] ${
                      featureIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'
                    } ${isSignificantFeature ? 'relative' : ''}`}
                    whileHover={{
                      backgroundColor: isSignificantFeature ? '#f0f9ff' : '',
                      boxShadow: isSignificantFeature ? 'inset 0 0 0 1px rgba(59, 130, 246, 0.1)' : ''
                    }}
                    transition={{ duration: 0.2 }}
                  >
                    {isSignificantFeature && (
                      <div className="absolute left-0 top-0 bottom-0 w-1.5 bg-gradient-to-b from-blue-500 to-primary"></div>
                    )}

                    <div className={`p-5 font-medium ${isSignificantFeature ? 'text-blue-700' : 'text-gray-700'}`}>
                      <div className="flex items-center">
                        {isSignificantFeature && (
                          <motion.div
                            className="mr-2 text-blue-500 bg-blue-100 p-1 rounded-full"
                            initial={{ rotate: 0, scale: 1 }}
                            animate={{ rotate: [0, -10, 10, -10, 10, 0], scale: [1, 1.1, 1] }}
                            transition={{ duration: 1, delay: 0.2 * featureIndex, repeat: 0 }}
                          >
                            <FiStar size={14} />
                          </motion.div>
                        )}
                        <span className="font-display">{feature}</span>
                      </div>

                      {isSignificantFeature && (
                        <div className="text-xs text-blue-600 mt-1 font-normal bg-blue-50 px-2 py-0.5 rounded-full inline-block">
                          Key differentiator
                        </div>
                      )}
                    </div>

                    {products.map((product) => {
                      const featureData = product.features?.[feature];
                      const isTopScore = featureData?.score === bestScore && bestScore > 0;

                      return (
                        <motion.div
                          key={`${product.id}-${feature}`}
                          className={`p-5 border-l border-gray-100 ${
                            isTopScore && isSignificantFeature ? 'bg-blue-50/70' : ''
                          }`}
                          whileHover={{
                            backgroundColor: featureData ? (isTopScore && isSignificantFeature ? '#ebf5ff' : '#f9fafb') : '',
                            scale: isTopScore ? 1.01 : 1
                          }}
                          transition={{ duration: 0.2 }}
                        >
                          {featureData ? (
                            <div className="space-y-2">
                              <div className={`font-medium ${isTopScore && isSignificantFeature ? 'text-blue-700' : ''}`}>
                                {isTopScore && isSignificantFeature && (
                                  <motion.span
                                    className="inline-block bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full mr-2 border border-blue-200"
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.3, delay: 0.5 }}
                                  >
                                    Best
                                  </motion.span>
                                )}
                                {featureData.value}
                              </div>

                              {featureData.score !== undefined && (
                                <div className="flex items-center mt-2">
                                  <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2 overflow-hidden">
                                    <motion.div
                                      className={`h-2.5 rounded-full ${
                                        isTopScore && isSignificantFeature
                                          ? 'bg-gradient-to-r from-blue-500 to-primary'
                                          : featureData.score > 0.7
                                            ? 'bg-gradient-to-r from-green-500 to-green-400'
                                            : featureData.score > 0.4
                                              ? 'bg-gradient-to-r from-yellow-500 to-yellow-400'
                                              : 'bg-gradient-to-r from-red-500 to-red-400'
                                      }`}
                                      style={{ width: `${featureData.score * 100}%` }}
                                      initial={{ width: 0 }}
                                      animate={{ width: `${featureData.score * 100}%` }}
                                      transition={{ duration: 0.8, delay: 0.1 * featureIndex, ease: "easeOut" }}
                                    ></motion.div>
                                  </div>
                                  <span className={`text-xs font-medium ${
                                    isTopScore && isSignificantFeature
                                      ? 'text-blue-700'
                                      : getSentimentColor(featureData.score)
                                  }`}>
                                    {Math.round(featureData.score * 100)}%
                                  </span>
                                </div>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-400 italic">Not available</span>
                          )}
                        </motion.div>
                      );
                    })}
                  </motion.div>
                );
              })}
            </div>

          {/* Pros and Cons */}
          <div className="grid grid-cols-1 md:grid-cols-2 border-b border-gray-100">
            {/* Pros Section */}
            <div className="border-r border-gray-100">
              <div className="p-6 bg-gradient-to-r from-green-50 to-transparent border-b border-gray-100">
                <div className="flex items-center">
                  <div className="bg-green-100 p-1.5 rounded-full mr-2.5">
                    <FiCheck className="text-green-600" size={16} />
                  </div>
                  <h3 className="text-lg font-display font-bold text-green-700">Pros</h3>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-[repeat(auto-fill,minmax(200px,1fr))]">
                {products.map((product, productIndex) => (
                  <motion.div
                    key={`${product.id}-pros`}
                    className="p-5 border-b md:border-b-0 md:border-r border-gray-100 hover:bg-green-50/30 transition-colors duration-300"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.05 * productIndex }}
                    whileHover={{ backgroundColor: "#f0fdf4" }}
                  >
                    <h4 className="font-medium text-gray-900 mb-3 font-display">{product.name}</h4>
                    <ul className="space-y-3">
                      {product.pros && product.pros.length > 0 ? (
                        product.pros.map((pro, index) => (
                          <motion.li
                            key={index}
                            className="flex items-start"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: 0.1 * index + 0.2 }}
                          >
                            <div className="bg-green-100 p-1 rounded-full mr-2 mt-0.5 flex-shrink-0">
                              <FiCheck className="text-green-600" size={12} />
                            </div>
                            <span className="text-gray-700">{pro}</span>
                          </motion.li>
                        ))
                      ) : (
                        <motion.li
                          className="text-gray-400 italic flex items-center"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3, delay: 0.2 }}
                        >
                          <div className="bg-gray-100 p-1 rounded-full mr-2 flex-shrink-0">
                            <FiCheck className="text-gray-400" size={12} />
                          </div>
                          No pros listed
                        </motion.li>
                      )}
                    </ul>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Cons Section */}
            <div>
              <div className="p-6 bg-gradient-to-r from-red-50 to-transparent border-b border-gray-100">
                <div className="flex items-center">
                  <div className="bg-red-100 p-1.5 rounded-full mr-2.5">
                    <FiX className="text-red-600" size={16} />
                  </div>
                  <h3 className="text-lg font-display font-bold text-red-700">Cons</h3>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-[repeat(auto-fill,minmax(200px,1fr))]">
                {products.map((product, productIndex) => (
                  <motion.div
                    key={`${product.id}-cons`}
                    className="p-5 border-b md:border-b-0 md:border-r border-gray-100 last:border-r-0 hover:bg-red-50/30 transition-colors duration-300"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.05 * productIndex }}
                    whileHover={{ backgroundColor: "#fef2f2" }}
                  >
                    <h4 className="font-medium text-gray-900 mb-3 font-display">{product.name}</h4>
                    <ul className="space-y-3">
                      {product.cons && product.cons.length > 0 ? (
                        product.cons.map((con, index) => (
                          <motion.li
                            key={index}
                            className="flex items-start"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: 0.1 * index + 0.2 }}
                          >
                            <div className="bg-red-100 p-1 rounded-full mr-2 mt-0.5 flex-shrink-0">
                              <FiX className="text-red-600" size={12} />
                            </div>
                            <span className="text-gray-700">{con}</span>
                          </motion.li>
                        ))
                      ) : (
                        <motion.li
                          className="text-gray-400 italic flex items-center"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3, delay: 0.2 }}
                        >
                          <div className="bg-gray-100 p-1 rounded-full mr-2 flex-shrink-0">
                            <FiX className="text-gray-400" size={12} />
                          </div>
                          No cons listed
                        </motion.li>
                      )}
                    </ul>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Enhanced Recommendation Section */}
          <div className="relative overflow-hidden rounded-b-premium">
            {/* Animated background circles */}
            <AnimatedCircles
              variant="mixed"
              count={6}
              maxOpacity={0.15}
              animationStyle="mixed"
              blurAmount={2}
              speed="slow"
              className="absolute inset-0"
            />

            <div className="relative z-10 p-8 bg-gradient-to-br from-primary/10 via-secondary/5 to-transparent">
              <ScrollFadeIn
                direction="up"
                threshold={0.1}
                duration={0.4}
                distance={20}
                delay={0.3}
              >
                <div className="mb-8">
                  <div className="flex items-center">
                    <div className="bg-primary/15 p-2.5 rounded-full mr-3 shadow-sm">
                      <FiBarChart2 className="text-primary" size={22} />
                    </div>
                    <h3 className="text-2xl font-display font-bold text-gray-900">Our Recommendation</h3>
                  </div>
                  <p className="text-gray-600 mt-2 ml-12">
                    Based on sentiment analysis, user ratings, and value for money
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-[repeat(auto-fill,minmax(250px,1fr))] gap-6">
                  {products.map((product, index) => {
                    // Calculate a recommendation score based on sentiment, rating, and price
                    const sentimentWeight = 0.5;
                    const ratingWeight = 0.3;
                    const priceWeight = 0.2;

                    const sentimentScore = product.sentimentScore * sentimentWeight;
                    const ratingScore = (product.rating / 5) * ratingWeight;
                    const priceScore = (1 - Math.min(product.price / 500, 1)) * priceWeight;

                    const recommendationScore = sentimentScore + ratingScore + priceScore;

                    const isRecommended = recommendationScore === Math.max(...products.map(p => {
                      const pSentimentScore = p.sentimentScore * sentimentWeight;
                      const pRatingScore = (p.rating / 5) * ratingWeight;
                      const pPriceScore = (1 - Math.min(p.price / 500, 1)) * priceWeight;
                      return pSentimentScore + pRatingScore + pPriceScore;
                    }));

                    return (
                      <motion.div
                        key={`${product.id}-recommendation`}
                        className={`p-6 rounded-premium shadow-premium ${
                          isRecommended
                            ? 'bg-gradient-to-br from-primary/10 via-primary/5 to-white border border-primary/20'
                            : 'bg-white border border-gray-100'
                        }`}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.1 * index + 0.3 }}
                        whileHover={{
                          y: -5,
                          boxShadow: isRecommended
                            ? '0 20px 25px -5px rgba(59, 130, 246, 0.15), 0 10px 10px -5px rgba(59, 130, 246, 0.1)'
                            : '0 15px 20px -3px rgba(0, 0, 0, 0.1), 0 6px 8px -2px rgba(0, 0, 0, 0.05)'
                        }}
                      >
                        {isRecommended && (
                          <div className="absolute top-0 right-0 w-24 h-24 overflow-hidden">
                            <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 rotate-45 bg-primary text-white text-xs font-bold py-1 px-6 shadow-md">
                              TOP PICK
                            </div>
                          </div>
                        )}

                        <div className="flex items-center justify-between mb-4">
                          <h4 className={`font-bold text-lg font-display ${isRecommended ? 'text-primary' : 'text-gray-900'}`}>
                            {product.name}
                          </h4>
                          {isRecommended && (
                            <motion.div
                              className="flex items-center"
                              initial={{ scale: 0.8, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ delay: 0.5, duration: 0.3 }}
                            >
                              <div className="bg-primary/10 p-1 rounded-full">
                                <FiCheck className="text-primary" size={16} />
                              </div>
                            </motion.div>
                          )}
                        </div>

                        {/* Score breakdown */}
                        <div className="space-y-4 mb-5">
                          <div>
                            <div className="flex justify-between text-sm font-medium mb-1.5">
                              <div className="flex items-center">
                                <div className="bg-green-100 p-1 rounded-full mr-2">
                                  <FiBarChart2 className="text-green-600" size={12} />
                                </div>
                                <span className="text-gray-700">Sentiment</span>
                              </div>
                              <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                product.sentimentScore > 0.7
                                  ? 'bg-green-100 text-green-700'
                                  : product.sentimentScore > 0.4
                                    ? 'bg-yellow-100 text-yellow-700'
                                    : 'bg-red-100 text-red-700'
                              }`}>
                                {Math.round(product.sentimentScore * 100)}%
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                              <motion.div
                                className={`h-2 rounded-full ${
                                  product.sentimentScore > 0.7
                                    ? 'bg-gradient-to-r from-green-500 to-green-400'
                                    : product.sentimentScore > 0.4
                                      ? 'bg-gradient-to-r from-yellow-500 to-yellow-400'
                                      : 'bg-gradient-to-r from-red-500 to-red-400'
                                }`}
                                style={{ width: `${product.sentimentScore * 100}%` }}
                                initial={{ width: 0 }}
                                animate={{ width: `${product.sentimentScore * 100}%` }}
                                transition={{ duration: 0.8, delay: 0.2 + (0.1 * index) }}
                              ></motion.div>
                            </div>
                          </div>

                          <div>
                            <div className="flex justify-between text-sm font-medium mb-1.5">
                              <div className="flex items-center">
                                <div className="bg-yellow-100 p-1 rounded-full mr-2">
                                  <FiStar className="text-yellow-600" size={12} />
                                </div>
                                <span className="text-gray-700">Rating</span>
                              </div>
                              <div className="flex items-center text-yellow-600 font-medium">
                                <div className="flex mr-1">
                                  {[1, 2, 3, 4, 5].map((star) => (
                                    <span key={star} className={star <= Math.round(product.rating) ? "text-yellow-400 text-xs" : "text-gray-300 text-xs"}>★</span>
                                  ))}
                                </div>
                                <span className="text-xs">{product.rating.toFixed(1)}</span>
                              </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                              <motion.div
                                className="h-2 rounded-full bg-gradient-to-r from-yellow-500 to-yellow-400"
                                style={{ width: `${(product.rating / 5) * 100}%` }}
                                initial={{ width: 0 }}
                                animate={{ width: `${(product.rating / 5) * 100}%` }}
                                transition={{ duration: 0.8, delay: 0.3 + (0.1 * index) }}
                              ></motion.div>
                            </div>
                          </div>

                          <div>
                            <div className="flex justify-between text-sm font-medium mb-1.5">
                              <div className="flex items-center">
                                <div className="bg-blue-100 p-1 rounded-full mr-2">
                                  <FiArrowRight className="text-blue-600" size={12} />
                                </div>
                                <span className="text-gray-700">Value</span>
                              </div>
                              <span className="text-blue-600 font-medium text-sm">${product.price.toFixed(2)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                              <motion.div
                                className="h-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-400"
                                style={{ width: `${(1 - Math.min(product.price / 500, 1)) * 100}%` }}
                                initial={{ width: 0 }}
                                animate={{ width: `${(1 - Math.min(product.price / 500, 1)) * 100}%` }}
                                transition={{ duration: 0.8, delay: 0.4 + (0.1 * index) }}
                              ></motion.div>
                            </div>
                          </div>
                        </div>

                        {/* Overall score */}
                        <div className="bg-gray-50 p-4 rounded-premium border border-gray-100 mb-4">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium text-gray-700">Overall Score</span>
                            <span className={`text-sm font-bold ${isRecommended ? 'text-primary' : 'text-gray-700'}`}>
                              {Math.round(recommendationScore * 100)}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                            <motion.div
                              className={`h-3 rounded-full ${
                                isRecommended
                                  ? 'bg-gradient-to-r from-primary to-secondary'
                                  : 'bg-gradient-to-r from-gray-500 to-gray-400'
                              }`}
                              style={{ width: `${recommendationScore * 100}%` }}
                              initial={{ width: 0 }}
                              animate={{ width: `${recommendationScore * 100}%` }}
                              transition={{ duration: 1, delay: 0.5 + (0.1 * index) }}
                            ></motion.div>
                          </div>
                        </div>

                        <div className={`text-sm rounded-premium p-3 ${
                          isRecommended
                            ? 'bg-primary/5 text-primary border border-primary/10'
                            : 'bg-gray-50 text-gray-600 border border-gray-100'
                        }`}>
                          {isRecommended
                            ? (
                              <div className="flex items-start">
                                <FiCheck className="text-primary mt-0.5 mr-1.5 flex-shrink-0" size={14} />
                                <span>Best overall choice based on sentiment analysis, user ratings, and value for money.</span>
                              </div>
                            )
                            : (
                              <div className="flex items-start">
                                <FiArrowRight className="text-gray-400 mt-0.5 mr-1.5 flex-shrink-0" size={14} />
                                <span>Good option with some trade-offs in {
                                  product.sentimentScore < 0.7
                                    ? 'user sentiment'
                                    : product.rating < 4.3
                                      ? 'ratings'
                                      : 'price-to-performance ratio'
                                }.</span>
                              </div>
                            )
                          }
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </ScrollFadeIn>
            </div>
          </div>
        </motion.div>
      </ScrollFadeIn>
      </div>
    </div>
  );
};

export default ComparisonPage;





