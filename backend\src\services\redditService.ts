import axios from 'axios';
import { getCache, setCache } from '../config/redis';
import logger from '../utils/logger';
import { RedditPost } from '../models/types';

// Cache TTL in seconds
const REDDIT_CACHE_TTL = 3600; // 1 hour

/**
 * Reddit API Service
 */
class RedditService {
  private clientId: string;
  private clientSecret: string;
  private userAgent: string;
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  constructor() {
    this.clientId = process.env.REDDIT_CLIENT_ID || 'H44nTyJUgV4waOKnC9YA9w';
    this.clientSecret = process.env.REDDIT_CLIENT_SECRET || 'VROpHb3GSWpRde66dru9X6cRYDwjgQ';
    this.userAgent = process.env.REDDIT_USER_AGENT || 'ProductWhisper/1.0';
  }

  /**
   * Get access token for Reddit API
   */
  private async getAccessToken(): Promise<string> {
    try {
      // Check if token is still valid
      if (this.accessToken && Date.now() < this.tokenExpiry) {
        return this.accessToken;
      }

      // Request new token
      const response = await axios.post(
        'https://www.reddit.com/api/v1/access_token',
        'grant_type=client_credentials',
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': this.userAgent
          },
          auth: {
            username: this.clientId,
            password: this.clientSecret
          }
        }
      );

      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);

      logger.info('Reddit API token obtained successfully');
      return this.accessToken as string;
    } catch (error) {
      logger.error('Reddit authentication error:', error);
      throw new Error('Failed to authenticate with Reddit API');
    }
  }

  /**
   * Search for product mentions on Reddit
   */
  async searchProducts(query: string, limit: number = 25): Promise<RedditPost[]> {
    try {
      // Check cache first
      const cacheKey = `reddit:search:${query}:${limit}`;
      const cachedData = await getCache(cacheKey);

      if (cachedData) {
        logger.debug(`Using cached Reddit data for query: ${query}`);
        return JSON.parse(cachedData);
      }

      // Get access token
      const token = await this.getAccessToken();

      // Search subreddits related to product reviews
      const subreddits = [
        'ProductReviews',
        'BuyItForLife',
        'gadgets',
        'tech',
        'reviews',
        'GoodValue'
      ];

      // Build search query
      const searchQuery = encodeURIComponent(`${query} self:yes`);

      // Make requests to each subreddit
      const requests = subreddits.map(subreddit =>
        axios.get(
          `https://oauth.reddit.com/r/${subreddit}/search?q=${searchQuery}&sort=relevance&limit=${Math.ceil(limit / subreddits.length)}&t=year`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'User-Agent': this.userAgent
            }
          }
        ).catch(error => {
          logger.warn(`Error searching subreddit r/${subreddit}:`, error.message);
          return { data: { data: { children: [] } } };
        })
      );

      // Execute all requests in parallel
      const responses = await Promise.all(requests);

      // Process and combine results
      const results: RedditPost[] = [];

      for (const response of responses) {
        const posts = response.data.data.children;

        for (const post of posts) {
          const data = post.data;

          // Extract product name from title
          const productName = this.extractProductName(data.title, query);

          if (productName) {
            results.push({
              source: 'reddit',
              sourceId: data.id,
              productName: productName,
              content: data.selftext || data.title,
              url: `https://reddit.com${data.permalink}`,
              createdAt: new Date(data.created_utc * 1000),
              score: data.score,
              numComments: data.num_comments,
              author: data.author,
              subreddit: data.subreddit,
            });
          }
        }
      }

      // Cache results
      await setCache(cacheKey, JSON.stringify(results), REDDIT_CACHE_TTL);

      logger.info(`Found ${results.length} Reddit posts for query: ${query}`);
      return results;
    } catch (error) {
      logger.error(`Error searching Reddit for query "${query}":`, error);
      throw error;
    }
  }

  /**
   * Extract product name from post title
   */
  private extractProductName(title: string, query: string): string | null {
    if (!title) return null;

    const titleLower = title.toLowerCase();
    const queryLower = query.toLowerCase();

    // Check if title contains the query
    if (titleLower.includes(queryLower)) {
      // Check for review patterns
      const reviewIndex = titleLower.indexOf('review of');

      if (reviewIndex !== -1) {
        // Extract after "review of" or similar
        const afterReview = title.substring(reviewIndex + 9).trim();
        if (afterReview.toLowerCase().includes(queryLower)) {
          // Find the start of the product name
          const productStart = afterReview.toLowerCase().indexOf(queryLower);
          // Extract a reasonable length for the product name
          return afterReview.substring(productStart, Math.min(afterReview.length, productStart + queryLower.length + 20));
        }
      }

      // If no review pattern found, just return the query with some context
      return `${query} ${titleLower.includes('pro') ? 'Pro' : titleLower.includes('ultra') ? 'Ultra' : ''}`.trim();
    }

    return null;
  }

  /**
   * Get trending products from Reddit
   */
  async getTrendingProducts(limit: number = 10): Promise<any[]> {
    try {
      // Check cache first
      const cacheKey = `reddit:trending:${limit}`;
      const cachedData = await getCache(cacheKey);

      if (cachedData) {
        logger.debug('Using cached Reddit trending data');
        return JSON.parse(cachedData);
      }

      // Get access token
      const token = await this.getAccessToken();

      // Product-related subreddits
      const subreddits = [
        'ProductReviews',
        'BuyItForLife',
        'gadgets',
        'tech',
        'reviews',
        'GoodValue'
      ];

      // Get top posts from each subreddit
      const requests = subreddits.map(subreddit =>
        axios.get(
          `https://oauth.reddit.com/r/${subreddit}/top?t=week&limit=${Math.ceil(limit / subreddits.length)}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'User-Agent': this.userAgent
            }
          }
        ).catch(error => {
          logger.warn(`Error getting top posts from r/${subreddit}:`, error.message);
          return { data: { data: { children: [] } } };
        })
      );

      // Execute all requests in parallel
      const responses = await Promise.all(requests);

      // Process and combine results
      const results: any[] = [];

      for (const response of responses) {
        const posts = response.data.data.children;

        for (const post of posts) {
          const data = post.data;

          // Extract product information
          const productInfo = this.extractProductInfo(data.title);

          if (productInfo) {
            results.push({
              name: productInfo.name,
              category: productInfo.category,
              score: data.score,
              comments: data.num_comments,
              url: `https://reddit.com${data.permalink}`,
              created: new Date(data.created_utc * 1000),
              subreddit: data.subreddit,
            });
          }
        }
      }

      // Sort by score and limit
      const trending = results
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);

      // Cache results
      await setCache(cacheKey, JSON.stringify(trending), REDDIT_CACHE_TTL);

      logger.info(`Found ${trending.length} trending products on Reddit`);
      return trending;
    } catch (error) {
      logger.error('Error getting trending products from Reddit:', error);
      throw error;
    }
  }

  /**
   * Extract product information from post title
   */
  private extractProductInfo(title: string): { name: string; category: string } | null {
    if (!title) return null;

    // Common product categories
    const categories = [
      'headphones', 'earbuds', 'speaker', 'laptop', 'phone', 'smartphone',
      'camera', 'tablet', 'monitor', 'keyboard', 'mouse', 'router', 'watch',
      'tv', 'television', 'vacuum', 'blender', 'microwave', 'refrigerator',
      'washer', 'dryer', 'dishwasher', 'air conditioner', 'heater', 'fan'
    ];

    // Check if title contains any category
    for (const category of categories) {
      if (title.toLowerCase().includes(category)) {
        // Extract product name (simple heuristic)
        const words = title.split(' ');
        const categoryIndex = words.findIndex(word =>
          word.toLowerCase().includes(category)
        );

        if (categoryIndex !== -1) {
          // Take a few words before and after the category
          const start = Math.max(0, categoryIndex - 2);
          const end = Math.min(words.length, categoryIndex + 3);
          const name = words.slice(start, end).join(' ');

          return {
            name,
            category
          };
        }
      }
    }

    return null;
  }
}

// Export singleton instance
export const redditService = new RedditService();
