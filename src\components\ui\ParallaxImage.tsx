import React from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

interface ParallaxImageProps {
  src: string;
  alt: string;
  speed?: number;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right';
  containerClassName?: string;
  imgClassName?: string;
}

/**
 * A component that creates a parallax effect on an image as the user scrolls
 */
const ParallaxImage: React.FC<ParallaxImageProps> = ({
  src,
  alt,
  speed = 0.5,
  className = '',
  direction = 'up',
  containerClassName = '',
  imgClassName = '',
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start end', 'end start'],
  });

  // Calculate transform values based on direction
  const getTransformValues = () => {
    const multiplier = direction === 'down' || direction === 'right' ? -1 : 1;
    const value = 20 * speed * multiplier;

    switch (direction) {
      case 'up':
      case 'down':
        return useTransform(scrollYProgress, [0, 1], [0, value]);
      case 'left':
      case 'right':
        return useTransform(scrollYProgress, [0, 1], [0, value]);
      default:
        return useTransform(scrollYProgress, [0, 1], [0, value]);
    }
  };

  const transformValue = getTransformValues();

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${containerClassName}`}
    >
      <motion.div
        className={`w-full h-full ${className}`}
        style={{
          y: direction === 'up' || direction === 'down' ? transformValue : 0,
          x: direction === 'left' || direction === 'right' ? transformValue : 0,
        }}
      >
        <img
          src={src}
          alt={alt}
          className={`w-full h-full object-cover ${imgClassName}`}
        />
      </motion.div>
    </div>
  );
};

export default ParallaxImage;
