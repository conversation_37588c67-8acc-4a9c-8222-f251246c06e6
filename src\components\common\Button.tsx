import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

// Define our own IconType interface
type IconType = React.ComponentType<{ className?: string; size?: number }>;

// Define ButtonProps interface directly in this file to avoid import issues
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  href?: string;
  to?: string;
  external?: boolean;
  className?: string;
  icon?: React.ComponentType<{ className?: string; size?: number }>;
  iconPosition?: 'left' | 'right';
  children?: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  icon: Icon,
  leftIcon,
  rightIcon,
  iconPosition = 'left',
  isLoading = false,
  fullWidth = false,
  to,
  href,
  external = false,
  children,
  className = '',
  disabled,
  ...props
}) => {
  // Base styles
  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-60 disabled:cursor-not-allowed';

  // Size styles
  const sizeStyles = {
    xs: 'text-xs px-2 py-1 rounded',
    sm: 'text-sm px-3 py-1.5 rounded-md',
    md: 'text-sm px-4 py-2 rounded-md',
    lg: 'text-base px-5 py-2.5 rounded-md',
    xl: 'text-lg px-6 py-3 rounded-md',
  };

  // Variant styles
  const variantStyles = {
    primary: 'bg-primary text-white hover:bg-primary-dark focus:ring-primary',
    secondary: 'bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary',
    outline: 'border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
  };

  // Width styles
  const widthStyles = fullWidth ? 'w-full' : '';

  // Loading state
  const loadingClasses = isLoading ? 'relative !text-transparent' : '';

  // Disabled styles
  const disabledStyles = (disabled || isLoading) ? 'opacity-60 cursor-not-allowed' : '';

  // Combine all styles
  const buttonStyles = `${baseStyles} ${sizeStyles[size]} ${variantStyles[variant]} ${widthStyles} ${loadingClasses} ${disabledStyles} ${className}`;

  // Loading spinner
  const LoadingSpinner = () => (
    <div className={`absolute inset-0 flex items-center justify-center ${isLoading ? 'opacity-100' : 'opacity-0'}`}>
      <svg className="animate-spin h-5 w-5 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  );

  // Button content
  const ButtonContent = () => (
    <>
      {isLoading && !loadingClasses && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}

      {leftIcon && <span className={`mr-2 ${isLoading && loadingClasses ? 'opacity-0' : ''}`}>{leftIcon}</span>}

      {Icon && iconPosition === 'left' && !isLoading && (
        <Icon className="mr-2 -ml-1" size={size === 'lg' || size === 'xl' ? 20 : size === 'md' ? 16 : 14} />
      )}

      <span className={isLoading && loadingClasses ? 'opacity-0' : ''}>{children}</span>

      {Icon && iconPosition === 'right' && (
        <Icon className="ml-2 -mr-1" size={size === 'lg' || size === 'xl' ? 20 : size === 'md' ? 16 : 14} />
      )}

      {rightIcon && <span className={`ml-2 ${isLoading && loadingClasses ? 'opacity-0' : ''}`}>{rightIcon}</span>}

      {isLoading && loadingClasses && <LoadingSpinner />}
    </>
  );

  // If href is provided, render as an anchor tag
  if (href) {
    return (
      <motion.a
        href={href}
        target={external ? '_blank' : undefined}
        rel={external ? 'noopener noreferrer' : undefined}
        className={buttonStyles}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <ButtonContent />
      </motion.a>
    );
  }

  // If to is provided, render as a Link from react-router-dom
  if (to) {
    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Link to={to} className={buttonStyles}>
          <ButtonContent />
        </Link>
      </motion.div>
    );
  }

  // Otherwise render as button
  return (
    <motion.button
      className={buttonStyles}
      disabled={disabled || isLoading}
      whileHover={!(disabled || isLoading) ? { scale: 1.02 } : {}}
      whileTap={!(disabled || isLoading) ? { scale: 0.98 } : {}}
      {...props}
    >
      <ButtonContent />
    </motion.button>
  );
};

export default Button;