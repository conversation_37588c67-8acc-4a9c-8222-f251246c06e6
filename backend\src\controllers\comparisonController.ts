import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { ApiError } from '../middleware/errorHandler';
import * as comparisonService from '../services/comparisonService';
import logger from '../utils/logger';
import { getCache, setCache } from '../config/redis';

/**
 * Compare products
 */
export const compareProducts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { productIds } = req.body;
    
    if (!productIds || !Array.isArray(productIds) || productIds.length < 2) {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'At least two product IDs are required for comparison');
    }
    
    // Validate product IDs
    for (const id of productIds) {
      if (isNaN(parseInt(id))) {
        throw new ApiError(StatusCodes.BAD_REQUEST, `Invalid product ID: ${id}`);
      }
    }
    
    // Try to get from cache first
    const cacheKey = `comparison:products:${productIds.sort().join('-')}`;
    const cachedData = await getCache(cacheKey);
    
    if (cachedData) {
      return res.status(StatusCodes.OK).json(JSON.parse(cachedData));
    }
    
    const comparison = await comparisonService.compareProducts(productIds);
    
    const result = {
      success: true,
      productIds,
      data: comparison,
    };
    
    // Cache the result
    await setCache(cacheKey, JSON.stringify(result), 3600); // Cache for 1 hour
    
    res.status(StatusCodes.OK).json(result);
  } catch (error) {
    logger.error('Error in compareProducts controller:', error);
    next(error);
  }
};

/**
 * Compare product sentiment
 */
export const compareSentiment = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { productIds } = req.body;
    
    if (!productIds || !Array.isArray(productIds) || productIds.length < 2) {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'At least two product IDs are required for sentiment comparison');
    }
    
    // Validate product IDs
    for (const id of productIds) {
      if (isNaN(parseInt(id))) {
        throw new ApiError(StatusCodes.BAD_REQUEST, `Invalid product ID: ${id}`);
      }
    }
    
    // Try to get from cache first
    const cacheKey = `comparison:sentiment:${productIds.sort().join('-')}`;
    const cachedData = await getCache(cacheKey);
    
    if (cachedData) {
      return res.status(StatusCodes.OK).json(JSON.parse(cachedData));
    }
    
    const comparison = await comparisonService.compareSentiment(productIds);
    
    const result = {
      success: true,
      productIds,
      data: comparison,
    };
    
    // Cache the result
    await setCache(cacheKey, JSON.stringify(result), 3600); // Cache for 1 hour
    
    res.status(StatusCodes.OK).json(result);
  } catch (error) {
    logger.error('Error in compareSentiment controller:', error);
    next(error);
  }
};
