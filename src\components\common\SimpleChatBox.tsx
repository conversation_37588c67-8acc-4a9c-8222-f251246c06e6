import React, { useState } from 'react';
import { FiMessageSquare, FiX, FiSend } from 'react-icons/fi';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
}

const SimpleChatBox: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hi there! 👋 I'm your ProductWhisper assistant. How can I help you today?",
      isUser: false
    }
  ]);

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Math.random().toString(),
      content: input,
      isUser: true
    };
    setMessages([...messages, userMessage]);
    setInput('');

    // Simulate response
    setTimeout(() => {
      const botMessage: Message = {
        id: Math.random().toString(),
        content: "Thank you for your message! This is a simple response to show that the chat box is working.",
        isUser: false
      };
      setMessages(prev => [...prev, botMessage]);
    }, 1000);
  };

  return (
    <>
      {/* Chat Button */}
      <button
        className="fixed bottom-6 right-6 z-40 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700"
        onClick={toggleChat}
      >
        {isOpen ? <FiX size={24} /> : <FiMessageSquare size={24} />}
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div
          className="fixed bottom-24 right-6 z-40 w-80 sm:w-96 bg-white rounded-xl shadow-2xl overflow-hidden flex flex-col"
          style={{ height: '500px', maxHeight: 'calc(100vh - 120px)' }}
        >
          {/* Header */}
          <div className="p-4 bg-blue-600 text-white flex justify-between items-center">
            <div>
              <h3 className="font-semibold">ProductWhisper Assistant</h3>
              <p className="text-xs opacity-80">Ask me anything about our products</p>
            </div>
            <button
              onClick={toggleChat}
              className="text-white p-1 rounded-full"
            >
              <FiX size={18} />
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`mb-4 ${message.isUser ? 'text-right' : 'text-left'}`}
              >
                <div
                  className={`inline-block py-2 px-4 rounded-lg ${
                    message.isUser
                      ? 'bg-blue-600 text-white'
                      : 'bg-white border border-gray-200 text-gray-800'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Input */}
          <form onSubmit={handleSubmit} className="p-3 border-t border-gray-200 bg-white">
            <div className="flex items-center">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 py-2 px-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="submit"
                className="bg-blue-600 text-white p-2 rounded-r-lg hover:bg-blue-700"
                disabled={!input.trim()}
              >
                <FiSend size={18} />
              </button>
            </div>
          </form>
        </div>
      )}
    </>
  );
};

export default SimpleChatBox;
