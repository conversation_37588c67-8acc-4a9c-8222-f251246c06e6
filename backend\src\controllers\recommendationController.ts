import { Request, Response } from 'express';
import logger from '../utils/logger';
import { getMockTrendingProducts, getMockSimilarProducts } from '../utils/mockData';

/**
 * Get trending products
 * @route GET /api/recommendations/trending
 */
export const getTrendingProducts = async (req: Request, res: Response): Promise<void> => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const category = req.query.category as string;

    // In a real implementation, this would fetch trending products from the database
    // For now, we'll use mock data
    const trendingProducts = getMockTrendingProducts(limit, category);

    res.status(200).json({
      success: true,
      count: trendingProducts.length,
      data: trendingProducts
    });
  } catch (error) {
    logger.error('Error getting trending products:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Get similar products
 * @route GET /api/recommendations/similar/:productId
 */
export const getSimilarProducts = async (req: Request, res: Response): Promise<void> => {
  try {
    const productId = parseInt(req.params.productId);
    const limit = parseInt(req.query.limit as string) || 5;

    if (isNaN(productId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid product ID'
      });
      return;
    }

    // In a real implementation, this would fetch similar products from the database
    // For now, we'll use mock data
    const similarProducts = getMockSimilarProducts(productId, limit);

    res.status(200).json({
      success: true,
      count: similarProducts.length,
      data: similarProducts
    });
  } catch (error) {
    logger.error('Error getting similar products:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

/**
 * Get personalized product recommendations
 * @route GET /api/recommendations/personalized
 */
export const getPersonalizedRecommendations = async (req: Request, res: Response): Promise<void> => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;

    // In a real implementation, this would fetch personalized recommendations based on user history
    // For now, we'll use mock data
    const personalizedRecommendations = getMockTrendingProducts(limit);

    res.status(200).json({
      success: true,
      count: personalizedRecommendations.length,
      data: personalizedRecommendations
    });
  } catch (error) {
    logger.error('Error getting personalized recommendations:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};
