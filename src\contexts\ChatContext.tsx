import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import { chatService } from '../services/chatService';

// Message types
export type MessageRole = 'user' | 'assistant' | 'system';

export interface ChatMessage {
  id: string;
  content: string;
  role: MessageRole;
  timestamp: Date;
}

// Chat context interface
interface ChatContextType {
  messages: ChatMessage[];
  isOpen: boolean;
  isTyping: boolean;
  openChat: () => void;
  closeChat: () => void;
  toggleChat: () => void;
  sendMessage: (content: string) => void;
  clearMessages: () => void;
  clearCache: () => void;
  getAnalytics: () => any;
  submitFeedback: (messageId: string, helpful: boolean) => Promise<boolean>;
}

// Create context with default values
const ChatContext = createContext<ChatContextType>({
  messages: [],
  isOpen: false,
  isTyping: false,
  openChat: () => {},
  closeChat: () => {},
  toggleChat: () => {},
  sendMessage: () => {},
  clearMessages: () => {},
  clearCache: () => {},
  getAnalytics: () => ({}),
  submitFeedback: async () => false,
});

// Provider props
interface ChatProviderProps {
  children: ReactNode;
}

// Generate a unique ID for messages
const generateId = (): string => {
  return Math.random().toString(36).substring(2, 11);
};

// Chat provider component
export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: generateId(),
      content: "Hi there! 👋 I'm your ProductWhisper assistant. How can I help you today?",
      role: 'assistant',
      timestamp: new Date(),
    },
  ]);
  const [isOpen, setIsOpen] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  // Open chat
  const openChat = useCallback(() => {
    setIsOpen(true);
  }, []);

  // Close chat
  const closeChat = useCallback(() => {
    setIsOpen(false);
  }, []);

  // Toggle chat
  const toggleChat = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  // Send a message
  const sendMessage = useCallback(async (content: string) => {
    // Safety check for empty content
    if (!content || typeof content !== 'string' || !content.trim()) return;

    // Add user message
    const userMessage: ChatMessage = {
      id: generateId(),
      content,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);

    // Set typing indicator
    setIsTyping(true);

    // Calculate a realistic typing delay based on response length
    // Average person types at ~40 words per minute, or ~200 characters per minute
    const getTypingDelay = (text: string, minDelay = 1000, maxDelay = 3000) => {
      // Estimate typing time: ~200 chars per minute = ~3.33 chars per second
      const typingTime = (text.length / 3.33) * 1000;
      // Clamp between min and max delay
      return Math.min(Math.max(typingTime, minDelay), maxDelay);
    };

    try {
      // Get response from chat service
      const responseContent = await chatService.generateResponse(content);

      // Calculate typing delay
      const typingDelay = getTypingDelay(responseContent);

      // Simulate typing delay
      setTimeout(() => {
        const assistantMessage: ChatMessage = {
          id: generateId(),
          content: responseContent || "I'm sorry, I couldn't process your request. Please try again.",
          role: 'assistant',
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, assistantMessage]);
        setIsTyping(false);
      }, typingDelay);
    } catch (error) {
      // Handle any errors that might occur during response generation
      console.error('Error generating response:', error);

      // Short delay for error message
      setTimeout(() => {
        const errorMessage: ChatMessage = {
          id: generateId(),
          content: "I'm sorry, something went wrong. Please try again later.",
          role: 'assistant',
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, errorMessage]);
        setIsTyping(false);
      }, 1000);
    }
  }, []);

  // Clear all messages
  const clearMessages = useCallback(() => {
    try {
      const welcomeMessage = {
        id: generateId(),
        content: "Hi there! 👋 I'm your ProductWhisper assistant. How can I help you today?",
        role: 'assistant' as MessageRole,
        timestamp: new Date(),
      };

      setMessages([welcomeMessage]);
    } catch (error) {
      console.error('Error clearing messages:', error);
      // Fallback to empty array with a simple message if something goes wrong
      setMessages([{
        id: '0',
        content: "Welcome to ProductWhisper. How can I help you?",
        role: 'assistant',
        timestamp: new Date(),
      }]);
    }
  }, []);

  // Clear chat cache
  const clearCache = useCallback(() => {
    try {
      chatService.clearCache();
    } catch (error) {
      console.error('Error clearing chat cache:', error);
    }
  }, []);

  // Get analytics data
  const getAnalytics = useCallback(() => {
    try {
      return chatService.getAnalytics();
    } catch (error) {
      console.error('Error getting chat analytics:', error);
      return {};
    }
  }, []);

  // Submit feedback for a chat response
  const submitFeedback = useCallback(async (messageId: string, helpful: boolean) => {
    try {
      return await chatService.submitFeedback(messageId, helpful);
    } catch (error) {
      console.error('Error submitting chat feedback:', error);
      return false;
    }
  }, []);

  return (
    <ChatContext.Provider
      value={{
        messages,
        isOpen,
        isTyping,
        openChat,
        closeChat,
        toggleChat,
        sendMessage,
        clearMessages,
        clearCache,
        getAnalytics,
        submitFeedback,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

// Custom hook to use the chat context
export const useChat = () => {
  const context = useContext(ChatContext);

  // Check if context is undefined (not within a provider)
  if (context === undefined) {
    console.error('useChat hook was called outside of a ChatProvider');
    throw new Error('useChat must be used within a ChatProvider');
  }

  // Create a safe version of the context with null checks
  const safeContext: ChatContextType = {
    messages: context.messages || [],
    isOpen: Boolean(context.isOpen),
    isTyping: Boolean(context.isTyping),
    openChat: context.openChat || (() => {}),
    closeChat: context.closeChat || (() => {}),
    toggleChat: context.toggleChat || (() => {}),
    sendMessage: context.sendMessage || (() => {}),
    clearMessages: context.clearMessages || (() => {}),
    clearCache: context.clearCache || (() => {}),
    getAnalytics: context.getAnalytics || (() => ({})),
    submitFeedback: context.submitFeedback || (async () => false)
  };

  return safeContext;
};

export default ChatContext;

