{"parser": "@typescript-eslint/parser", "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "prettier"], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "env": {"node": true, "es6": true, "jest": true}, "rules": {"prettier/prettier": "error", "no-console": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}}