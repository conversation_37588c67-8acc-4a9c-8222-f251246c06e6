import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

interface GradientButtonProps {
  children: React.ReactNode;
  to?: string;
  href?: string;
  onClick?: () => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
  external?: boolean;
  animate?: boolean;
}

/**
 * A button component with gradient background and hover effects
 */
const GradientButton: React.FC<GradientButtonProps> = ({
  children,
  to,
  href,
  onClick,
  className = '',
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  leftIcon,
  rightIcon,
  type = 'button',
  external = false,
  animate = true,
}) => {
  // Gradient classes based on variant
  const gradientClasses = {
    primary: 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700',
    secondary: 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700',
    accent: 'bg-gradient-to-r from-teal-500 to-emerald-500 hover:from-teal-600 hover:to-emerald-600',
    success: 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600',
    warning: 'bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600',
    error: 'bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600',
  };

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    xl: 'px-8 py-4 text-xl',
  };

  // Base classes
  const baseClasses = 'inline-flex items-center justify-center font-medium text-white rounded-lg shadow-md transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  // Width classes
  const widthClasses = fullWidth ? 'w-full' : '';
  
  // Disabled classes
  const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed' : '';
  
  // Focus ring classes based on variant
  const focusRingClasses = {
    primary: 'focus:ring-blue-500',
    secondary: 'focus:ring-purple-500',
    accent: 'focus:ring-teal-500',
    success: 'focus:ring-green-500',
    warning: 'focus:ring-yellow-500',
    error: 'focus:ring-red-500',
  };
  
  // Combine all classes
  const buttonClasses = `${baseClasses} ${gradientClasses[variant]} ${sizeClasses[size]} ${widthClasses} ${disabledClasses} ${focusRingClasses[variant]} ${className}`;

  // Animation variants
  const buttonVariants = {
    hover: animate ? { scale: 1.03, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' } : {},
    tap: animate ? { scale: 0.98 } : {},
  };

  // Button content
  const content = (
    <>
      {leftIcon && <span className="mr-2">{leftIcon}</span>}
      {children}
      {rightIcon && <span className="ml-2">{rightIcon}</span>}
    </>
  );

  // Render as link if 'to' prop is provided (internal link)
  if (to) {
    return (
      <motion.div
        whileHover={!disabled ? buttonVariants.hover : {}}
        whileTap={!disabled ? buttonVariants.tap : {}}
      >
        <Link to={to} className={buttonClasses} onClick={onClick}>
          {content}
        </Link>
      </motion.div>
    );
  }

  // Render as anchor if 'href' prop is provided (external link)
  if (href) {
    return (
      <motion.a
        href={href}
        target={external ? '_blank' : undefined}
        rel={external ? 'noopener noreferrer' : undefined}
        className={buttonClasses}
        onClick={onClick}
        whileHover={!disabled ? buttonVariants.hover : {}}
        whileTap={!disabled ? buttonVariants.tap : {}}
      >
        {content}
      </motion.a>
    );
  }

  // Otherwise render as button
  return (
    <motion.button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
      whileHover={!disabled ? buttonVariants.hover : {}}
      whileTap={!disabled ? buttonVariants.tap : {}}
    >
      {content}
    </motion.button>
  );
};

export default GradientButton;
