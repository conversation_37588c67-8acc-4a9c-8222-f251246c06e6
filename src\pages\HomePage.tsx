import React, { useState, useRef, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion, useInView, useScroll, useTransform } from 'framer-motion';
import { apiService } from '../services/api';
import LoadingSpinner from '../components/common/LoadingSpinner';
import useApi from '../hooks/useApi';
import ApiErrorFallback from '../components/common/ApiErrorFallback';
import {
  AnimatedCircles,
  ScrollReveal,
  ParallaxSection,
  ScrollFadeIn,
  ParallaxImage,
  GradientButton
} from '../components/ui';
import { mockProducts, mockReviews, mockCategories } from '../utils/mockData';
import {
  Search as MagnifyingGlassIcon,
  BarChart as ChartBarIcon,
  ArrowLeftRight as ArrowsRightLeftIcon,
  Lightbulb as LightBulbIcon,
  MessageSquare as ChatBubbleLeftRightIcon,
  ShieldCheck as ShieldCheckIcon,
  Star as StarIcon,
  ArrowRight
} from 'lucide-react';
import { But<PERSON> } from '../components/common';

interface TrendingProduct {
  id: number;
  name: string;
  description: string;
  brand: string;
  category: string;
  score: number;
  mention_count: number;
}

const HomePage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  // References for scroll animations
  const heroRef = useRef<HTMLDivElement>(null);
  const featuresRef = useRef<HTMLDivElement>(null);
  const trendingRef = useRef<HTMLDivElement>(null);

  // Check if sections are in view
  const heroInView = useInView(heroRef, { once: false, amount: 0.3 });
  const featuresInView = useInView(featuresRef, { once: false, amount: 0.3 });
  const trendingInView = useInView(trendingRef, { once: false, amount: 0.3 });

  // Use the useApi hook to fetch trending products with caching
  const { data, loading, error, refetch } = useApi(
    () => apiService.getTrendingProducts(6),
    {
      cacheKey: 'trending-products',
      cacheDuration: 5 * 60 * 1000, // 5 minutes
      onError: (err) => {
        console.error('Error fetching trending products:', err);
        // We'll handle the error in the UI, so no need to throw
      }
    }
  );

  // Map the Product[] to TrendingProduct[] format with proper null checks
  const trendingProducts: TrendingProduct[] = useMemo(() => {
    // If we have valid data from the API
    if (data && Array.isArray(data) && data.length > 0) {
      return data.map(product => ({
        id: product?.id || 0,
        name: product?.name || 'Unknown Product',
        description: product?.description || 'No description available',
        brand: product?.brand || 'Unknown Brand',
        category: product?.category || 'Uncategorized',
        score: product?.sentimentScore || 0,
        mention_count: product?.reviewCount || 0
      }));
    }

    // Fallback to mock data
    return mockProducts.slice(0, 6).map(product => ({
      id: product.id,
      name: product.name,
      description: product.description || 'No description available',
      brand: product.brand || 'Unknown Brand',
      category: product.category || 'Uncategorized',
      score: product.sentimentScore || 0,
      mention_count: product.reviewCount || 0
    }));
  }, [data]);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <div>
      {/* Hero section with simplified clean design */}
      <div
        ref={heroRef}
        className="relative bg-gradient-primary rounded-premium shadow-premium overflow-hidden mb-6"
      >


        <div className="hero-content relative z-10">
          <motion.h1
            className="font-display text-4xl font-bold text-white sm:text-5xl sm:tracking-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, type: "spring", stiffness: 50 }}
          >
            Discover What People{" "}
            <motion.span
              className="text-yellow-300 inline-block"
              animate={{
                scale: [1, 1.05, 1],
                textShadow: [
                  "0 0 0px rgba(253, 224, 71, 0.5)",
                  "0 0 10px rgba(253, 224, 71, 0.8)",
                  "0 0 0px rgba(253, 224, 71, 0.5)"
                ]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              Really Think
            </motion.span>
          </motion.h1>

          <motion.p
            className="mt-3 text-lg text-white/80 leading-relaxed max-w-xl"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            ProductWhisper analyzes thousands of reviews and comments to give you the real story behind products, helping you make informed decisions.
          </motion.p>

          <motion.div
            className="hero-search-container"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <form onSubmit={handleSearch} className="relative w-full">
              <div className="hero-search-icon">
                <MagnifyingGlassIcon className="h-5 w-5" aria-hidden="true" />
              </div>
              <input
                type="text"
                className="hero-search-input"
                placeholder="Search for a product..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button
                type="submit"
                className="hero-search-button"
              >
                Search
              </button>
            </form>
          </motion.div>
        </div>
      </div>

      {/* Features section with enhanced scroll reveal animations */}
      <motion.div
        ref={featuresRef}
        className="py-10 bg-gray-50 relative overflow-hidden"
      >
        {/* Enhanced parallax background circles */}
        <ParallaxSection speed={0.15} direction="down" className="absolute inset-0 z-0">
          <AnimatedCircles
            variant="accent"
            count={15}
            maxOpacity={0.4}
            animationStyle="pulse"
            blurAmount={1.2}
            speed="slow"
          />
        </ParallaxSection>

        {/* Additional decorative elements */}
        <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-white to-transparent z-0"></div>
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-white to-transparent z-0"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <ScrollFadeIn
            direction="up"
            className="lg:text-center"
            threshold={0.2}
            duration={0.8}
            distance={40}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={featuresInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <h2 className="text-base text-blue-600 font-semibold tracking-wide uppercase font-sans">Premium Features</h2>
              <p className="mt-2 text-4xl font-bold tracking-tight text-gray-900 font-display">
                Make Smarter Purchasing Decisions
              </p>
              <p className="mt-4 max-w-2xl text-lg text-gray-600 lg:mx-auto font-sans leading-relaxed">
                ProductWhisper helps you cut through marketing hype and discover what real users think about the products you're interested in.
              </p>
            </motion.div>
          </ScrollFadeIn>

          <div className="mt-10">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <ScrollFadeIn
                direction="up"
                delay={0.1}
                className="relative"
                duration={0.6}
                distance={30}
              >
                <motion.div
                  className="h-full p-5 bg-white rounded-premium shadow-premium hover:shadow-premium-hover transition-all duration-300 border border-transparent hover:border-blue-100"
                  whileHover={{
                    y: -8,
                    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                    backgroundColor: "#fafcff" // very light blue tint on hover
                  }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="relative">
                    <motion.div
                      className="flex items-center justify-center h-14 w-14 rounded-full bg-blue-100 text-blue-600 mb-4 relative z-10"
                      whileHover={{
                        scale: 1.05,
                        backgroundColor: "#dbeafe", // lighter blue
                        boxShadow: "0 0 15px rgba(59, 130, 246, 0.5)"
                      }}
                    >
                      <MagnifyingGlassIcon className="h-8 w-8" aria-hidden="true" />
                    </motion.div>
                    <motion.div
                      className="absolute -top-2 -left-2 w-18 h-18 rounded-full bg-blue-50 z-0"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.3, 0.5, 0.3]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                    />
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 font-display mb-3">Sentiment Analysis</h3>
                  <p className="text-base text-gray-600 leading-relaxed">
                    Our advanced AI analyzes thousands of reviews and comments to determine how people really feel about products, identifying both positive and negative aspects.
                  </p>

                  <motion.div
                    className="mt-6 pt-4 border-t border-gray-100 flex items-center text-blue-600 text-sm font-medium"
                    initial={{ opacity: 0 }}
                    whileHover={{ x: 5 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    Learn more
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </motion.div>
                </motion.div>
              </ScrollFadeIn>

              <ScrollFadeIn
                direction="up"
                delay={0.2}
                className="relative"
                duration={0.6}
                distance={30}
              >
                <motion.div
                  className="h-full p-5 bg-white rounded-premium shadow-premium hover:shadow-premium-hover transition-all duration-300 border border-transparent hover:border-indigo-100"
                  whileHover={{
                    y: -8,
                    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                    backgroundColor: "#fafbff" // very light indigo tint on hover
                  }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="relative">
                    <motion.div
                      className="flex items-center justify-center h-14 w-14 rounded-full bg-indigo-100 text-indigo-600 mb-4 relative z-10"
                      whileHover={{
                        scale: 1.05,
                        backgroundColor: "#e0e7ff", // lighter indigo
                        boxShadow: "0 0 15px rgba(99, 102, 241, 0.5)"
                      }}
                    >
                      <ChartBarIcon className="h-8 w-8" aria-hidden="true" />
                    </motion.div>
                    <motion.div
                      className="absolute -top-2 -left-2 w-18 h-18 rounded-full bg-indigo-50 z-0"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.3, 0.5, 0.3]
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                    />
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 font-display mb-3">Trend Analysis</h3>
                  <p className="text-base text-gray-600 leading-relaxed">
                    Track how sentiment changes over time and spot emerging issues or improvements. Stay ahead of the curve with our real-time trend monitoring.
                  </p>

                  <motion.div
                    className="mt-6 pt-4 border-t border-gray-100 flex items-center text-indigo-600 text-sm font-medium"
                    initial={{ opacity: 0 }}
                    whileHover={{ x: 5 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    Learn more
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </motion.div>
                </motion.div>
              </ScrollFadeIn>

              <ScrollFadeIn
                direction="up"
                delay={0.3}
                className="relative"
                duration={0.6}
                distance={30}
              >
                <motion.div
                  className="h-full p-5 bg-white rounded-premium shadow-premium hover:shadow-premium-hover transition-all duration-300 border border-transparent hover:border-purple-100"
                  whileHover={{
                    y: -8,
                    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                    backgroundColor: "#fcfaff" // very light purple tint on hover
                  }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="relative">
                    <motion.div
                      className="flex items-center justify-center h-14 w-14 rounded-full bg-purple-100 text-purple-600 mb-4 relative z-10"
                      whileHover={{
                        scale: 1.05,
                        backgroundColor: "#f3e8ff", // lighter purple
                        boxShadow: "0 0 15px rgba(168, 85, 247, 0.5)"
                      }}
                    >
                      <ArrowsRightLeftIcon className="h-8 w-8" aria-hidden="true" />
                    </motion.div>
                    <motion.div
                      className="absolute -top-2 -left-2 w-18 h-18 rounded-full bg-purple-50 z-0"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.3, 0.5, 0.3]
                      }}
                      transition={{
                        duration: 3.5,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                    />
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 font-display mb-3">Product Comparison</h3>
                  <p className="text-base text-gray-600 leading-relaxed">
                    Compare products side-by-side based on real user sentiment and specific features. Make informed decisions with our comprehensive comparison tools.
                  </p>

                  <motion.div
                    className="mt-6 pt-4 border-t border-gray-100 flex items-center text-purple-600 text-sm font-medium"
                    initial={{ opacity: 0 }}
                    whileHover={{ x: 5 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    Learn more
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </motion.div>
                </motion.div>
              </ScrollFadeIn>
            </div>

            <motion.div
              className="mt-10 grid grid-cols-1 gap-6 md:grid-cols-3"
              initial={{ opacity: 0 }}
              animate={featuresInView ? { opacity: 1 } : { opacity: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <motion.div
                className="relative p-5 bg-white rounded-premium shadow-premium hover:shadow-premium-hover transition-shadow duration-300"
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center justify-center h-14 w-14 rounded-full bg-green-100 text-green-600 mb-4">
                  <LightBulbIcon className="h-8 w-8" aria-hidden="true" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 font-display mb-3">Smart Recommendations</h3>
                <p className="text-base text-gray-600 leading-relaxed">
                  Get personalized product recommendations based on your preferences and the collective wisdom of thousands of real users.
                </p>
              </motion.div>

              <motion.div
                className="relative p-5 bg-white rounded-premium shadow-premium hover:shadow-premium-hover transition-shadow duration-300"
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center justify-center h-14 w-14 rounded-full bg-yellow-100 text-yellow-600 mb-4">
                  <ChatBubbleLeftRightIcon className="h-8 w-8" aria-hidden="true" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 font-display mb-3">Review Summarization</h3>
                <p className="text-base text-gray-600 leading-relaxed">
                  Save time with our AI-powered review summarization that distills thousands of reviews into key insights and takeaways.
                </p>
              </motion.div>

              <motion.div
                className="relative p-5 bg-white rounded-premium shadow-premium hover:shadow-premium-hover transition-shadow duration-300"
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center justify-center h-14 w-14 rounded-full bg-red-100 text-red-600 mb-4">
                  <ShieldCheckIcon className="h-8 w-8" aria-hidden="true" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 font-display mb-3">Fake Review Detection</h3>
                <p className="text-base text-gray-600 leading-relaxed">
                  Our advanced algorithms identify and filter out fake or biased reviews, ensuring you get authentic insights from real users.
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Trending products section */}
      <motion.div
        ref={trendingRef}
        initial={{ opacity: 0 }}
        animate={trendingInView ? { opacity: 1 } : { opacity: 0 }}
        transition={{ duration: 0.8 }}
        className="py-10 relative overflow-hidden"
      >
        <AnimatedCircles variant="primary" count={10} className="opacity-60" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="lg:text-center mb-8"
            initial={{ y: 30, opacity: 0 }}
            animate={trendingInView ? { y: 0, opacity: 1 } : { y: 30, opacity: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-base text-blue-600 font-semibold tracking-wide uppercase font-sans">Trending Now</h2>
            <p className="mt-2 text-4xl font-bold tracking-tight text-gray-900 font-display">
              Popular Products People Are Talking About
            </p>
            <p className="mt-4 max-w-2xl text-lg text-gray-600 lg:mx-auto font-sans leading-relaxed">
              Discover what's trending based on real user sentiment and mentions across the web.
            </p>
          </motion.div>

          {loading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="large" text="Loading trending products..." />
            </div>
          ) : error ? (
            <div className="max-w-lg mx-auto py-8">
              <ApiErrorFallback
                error={error instanceof Error ? error : new Error(String(error))}
                message="We couldn't load trending products at this time."
                retry={refetch}
              />
            </div>
          ) : (
            <motion.div
              className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3"
              initial={{ y: 50, opacity: 0 }}
              animate={trendingInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
              transition={{ duration: 0.6, staggerChildren: 0.1 }}
            >
              {trendingProducts.length > 0 ? (
                trendingProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ y: 50, opacity: 0 }}
                    animate={trendingInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <Link
                      to={`/product/${product.id}`}
                      className="group block h-full bg-white rounded-premium shadow-premium overflow-hidden hover:shadow-premium-hover transition-all duration-300"
                    >
                      <div className="p-6">
                        <div className="flex justify-between items-start">
                          <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors font-display">
                            {product.name}
                          </h3>
                          <div className="flex items-center space-x-1 bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                            <StarIcon className="h-4 w-4" />
                            <span className="text-sm font-semibold">{(product.score * 5).toFixed(1)}</span>
                          </div>
                        </div>
                        <p className="mt-3 text-base text-gray-600 line-clamp-2 font-sans leading-relaxed">
                          {product.description}
                        </p>
                        <div className="mt-6 flex justify-between items-center pt-4 border-t border-gray-100">
                          <span className="text-sm font-semibold text-gray-700">{product.brand}</span>
                          <span className="text-sm font-medium text-blue-600">{product.mention_count.toLocaleString()} mentions</span>
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                ))
              ) : (
                <div className="col-span-full text-center py-8 text-gray-500">
                  No trending products found at this time.
                </div>
              )}
            </motion.div>
          )}

          <motion.div
            className="mt-12 text-center"
            initial={{ opacity: 0 }}
            animate={trendingInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Button
              to="/search"
              size="lg"
              className="px-8 py-3 text-base font-semibold"
              rightIcon={<MagnifyingGlassIcon className="h-5 w-5 ml-2" />}
            >
              Explore More Products
            </Button>
          </motion.div>
        </div>
      </motion.div>

      {/* Enhanced Call to Action Section */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="bg-gradient-secondary py-16 mt-12 rounded-premium mx-4 sm:mx-8 lg:mx-12 relative overflow-hidden shadow-2xl"
      >
        {/* Enhanced animated background */}
        <AnimatedCircles
          variant="secondary"
          count={30}
          maxOpacity={0.6}
          animationStyle="mixed"
          blurAmount={1.5}
          speed="medium"
        />

        {/* Additional decorative elements */}
        <motion.div
          className="absolute top-0 right-0 w-64 h-64 bg-yellow-300/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.2, 0.1]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />

        <motion.div
          className="absolute bottom-0 left-0 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.15, 0.1]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />

        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.h2
              className="text-3xl sm:text-4xl font-bold text-white font-display mb-6"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              Ready to Make{" "}
              <motion.span
                className="relative inline-block"
                animate={{
                  color: ["#ffffff", "#fef08a", "#ffffff"]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                Smarter Purchasing
                <motion.span
                  className="absolute -bottom-1 left-0 w-full h-1 bg-yellow-300/70 rounded-full"
                  animate={{
                    width: ["0%", "100%", "0%"],
                    left: ["0%", "0%", "100%"]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
              </motion.span>{" "}
              Decisions?
            </motion.h2>
          </motion.div>

          <motion.p
            className="text-xl text-white/90 font-light max-w-3xl mx-auto mb-10 leading-relaxed"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Join thousands of smart shoppers who use ProductWhisper to cut through marketing hype and discover products that truly meet their needs.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <GradientButton
              to="/search"
              size="xl"
              variant="primary"
              className="bg-white text-indigo-600 hover:bg-gray-100 px-8 py-3 text-base font-semibold"
              rightIcon={<ArrowRight className="ml-1 h-5 w-5" />}
            >
              Start Exploring
            </GradientButton>

            <GradientButton
              to="/about"
              variant="secondary"
              size="xl"
              className="bg-transparent border border-white text-white hover:bg-white/10 px-8 py-3 text-base font-semibold"
            >
              Learn More
            </GradientButton>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default HomePage;
