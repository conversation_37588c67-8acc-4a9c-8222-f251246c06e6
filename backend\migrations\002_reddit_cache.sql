-- Reddit API cache tables

-- Reddit cache requests table
CREATE TABLE IF NOT EXISTS reddit_cache_requests (
  id SERIAL PRIMARY KEY,
  cache_key VARCHAR(255) NOT NULL,
  request_type VARCHAR(50) NOT NULL,
  parameters JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  access_count INTEGER DEFAULT 1,
  UNIQUE(cache_key)
);

-- Reddit cache responses table
CREATE TABLE IF NOT EXISTS reddit_cache_responses (
  id SERIAL PRIMARY KEY,
  request_id INTEGER NOT NULL REFERENCES reddit_cache_requests(id) ON DELETE CASCADE,
  response_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reddit posts table (for storing individual posts)
CREATE TABLE IF NOT EXISTS reddit_posts (
  id SERIAL PRIMARY KEY,
  post_id VARCHAR(50) NOT NULL,
  subreddit VARCHAR(100) NOT NULL,
  title TEXT NOT NULL,
  selftext TEXT,
  author VARCHAR(100),
  score INTEGER,
  num_comments INTEGER,
  created_utc BIGINT,
  permalink VARCHAR(255),
  url VARCHAR(255),
  is_self BOOLEAN,
  retrieved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sentiment_score DECIMAL(4, 3),
  UNIQUE(post_id)
);

-- Reddit product mentions table (for tracking product mentions in posts)
CREATE TABLE IF NOT EXISTS reddit_product_mentions (
  id SERIAL PRIMARY KEY,
  post_id INTEGER NOT NULL REFERENCES reddit_posts(id) ON DELETE CASCADE,
  product_name VARCHAR(255) NOT NULL,
  category VARCHAR(100),
  sentiment_score DECIMAL(4, 3),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, product_name)
);

-- Reddit trending products table (for aggregated trending products)
CREATE TABLE IF NOT EXISTS reddit_trending_products (
  id SERIAL PRIMARY KEY,
  product_name VARCHAR(255) NOT NULL,
  category VARCHAR(100),
  mention_count INTEGER NOT NULL DEFAULT 0,
  total_score INTEGER NOT NULL DEFAULT 0,
  average_sentiment DECIMAL(4, 3),
  timeframe VARCHAR(20) NOT NULL, -- 'day', 'week', 'month'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(product_name, timeframe)
);

-- Create indexes
CREATE INDEX idx_reddit_cache_requests_expires_at ON reddit_cache_requests(expires_at);
CREATE INDEX idx_reddit_cache_requests_last_accessed_at ON reddit_cache_requests(last_accessed_at);
CREATE INDEX idx_reddit_posts_created_utc ON reddit_posts(created_utc);
CREATE INDEX idx_reddit_posts_subreddit ON reddit_posts(subreddit);
CREATE INDEX idx_reddit_product_mentions_product_name ON reddit_product_mentions(product_name);
CREATE INDEX idx_reddit_trending_products_mention_count ON reddit_trending_products(mention_count);
CREATE INDEX idx_reddit_trending_products_timeframe ON reddit_trending_products(timeframe);

-- Function to clean up expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_reddit_cache()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM reddit_cache_requests
  WHERE expires_at < NOW();
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update last accessed timestamp
CREATE OR REPLACE FUNCTION update_reddit_cache_access(p_cache_key VARCHAR)
RETURNS VOID AS $$
BEGIN
  UPDATE reddit_cache_requests
  SET last_accessed_at = NOW(),
      access_count = access_count + 1
  WHERE cache_key = p_cache_key;
END;
$$ LANGUAGE plpgsql;
