import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  LineChart,
  Line
} from 'recharts';
import { type AspectSentiment } from '../types/sentiment';

interface SentimentVisualizerProps {
  data: Array<{
    name: string;
    score?: number;
    sentiment?: number;
    value?: number;
  }>;
  type: 'bar' | 'pie' | 'radar' | 'line';
  aspectData?: AspectSentiment[];
  className?: string;
}

/**
 * Component for visualizing sentiment data using various chart types
 */
const SentimentVisualizer: React.FC<SentimentVisualizerProps> = ({
  data,
  type,
  aspectData,
  className = ''
}) => {
  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  // Get sentiment color based on score
   const getSentimentColor = (score: number): string => {
    if (score >= 0.6) return '#22c55e'; // green-500
    if (score >= 0.2) return '#4ade80'; // green-400
    if (score >= -0.2) return '#9ca3af'; // gray-400
    if (score >= -0.6) return '#f87171'; // red-400
    return '#ef4444'; // red-500
  };

  // Format sentiment score for display
  const formatSentimentScore = (score: number): string => {
    return score.toFixed(2);
  };

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded shadow-md">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Prepare data for sentiment distribution pie chart
  const prepareSentimentDistributionData = () => {
    const sentimentCounts = {
      'Very Positive': 0,
      'Positive': 0,
      'Neutral': 0,
      'Negative': 0,
      'Very Negative': 0
    };

    // Check if data is an array and not empty
    if (Array.isArray(data) && data.length > 0) {
      data.forEach((item: any) => {
        if (!item) return; // Skip null or undefined items

        const score = item.score || item.sentiment || 0;

        if (score >= 0.6) sentimentCounts['Very Positive']++;
        else if (score >= 0.2) sentimentCounts['Positive']++;
        else if (score >= -0.2) sentimentCounts['Neutral']++;
        else if (score >= -0.6) sentimentCounts['Negative']++;
        else sentimentCounts['Very Negative']++;
      });
    } else {
      // If no data, set neutral to 1 to show something
      sentimentCounts['Neutral'] = 1;
    }

    return Object.entries(sentimentCounts).map(([name, value]) => ({ name, value }));
  };

  // Render bar chart for sentiment scores
  const renderBarChart = () => {
    // Check if data is valid for rendering
    if (!Array.isArray(data) || data.length === 0) {
      return (
        <div className="text-center text-gray-500 py-4">
          No data available for bar chart
        </div>
      );
    }

    // Determine which data key to use (score or value)
    const dataKey = data[0].hasOwnProperty('score') ? 'score' : 'value';

    return (
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis domain={[-1, 1]} />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Bar
            dataKey={dataKey}
            name="Sentiment Score"
            fill="#8884d8"
            isAnimationActive={true}
          />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  // Render pie chart for sentiment distribution
  const renderPieChart = () => {
    const pieData = prepareSentimentDistributionData();

    return (
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={pieData}
            cx="50%"
            cy="50%"
            labelLine={true}
            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            isAnimationActive={true}
          >
            {pieData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  // Render radar chart for aspect sentiment
  const renderRadarChart = () => {
    if (!aspectData || aspectData.length === 0) {
      return <div className="text-center text-gray-500 py-4">No aspect data available</div>;
    }

    return (
      <ResponsiveContainer width="100%" height={300}>
        <RadarChart cx="50%" cy="50%" outerRadius="80%" data={aspectData}>
          <PolarGrid />
          <PolarAngleAxis dataKey="aspect" />
          <PolarRadiusAxis domain={[-1, 1]} />
          <Radar
            name="Sentiment"
            dataKey="sentiment"
            stroke="#8884d8"
            fill="#8884d8"
            fillOpacity={0.6}
            isAnimationActive={true}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
        </RadarChart>
      </ResponsiveContainer>
    );
  };

  // Render line chart for sentiment trends
  const renderLineChart = () => {
    // Check if data is valid for rendering
    if (!Array.isArray(data) || data.length === 0) {
      return (
        <div className="text-center text-gray-500 py-4">
          No data available for line chart
        </div>
      );
    }

    // Determine which data key to use for X-axis
    const xAxisKey = data[0].hasOwnProperty('date') ? 'date' : 'name';

    // Determine which data key to use for Y-axis
    const yAxisKey = data[0].hasOwnProperty('value') ? 'value' :
                    (data[0].hasOwnProperty('score') ? 'score' : 'sentiment');

    return (
      <ResponsiveContainer width="100%" height={300}>
        <LineChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey={xAxisKey} />
          <YAxis domain={[-1, 1]} />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Line
            type="monotone"
            dataKey={yAxisKey}
            name="Sentiment Score"
            stroke="#8884d8"
            activeDot={{ r: 8 }}
            isAnimationActive={true}
          />
        </LineChart>
      </ResponsiveContainer>
    );
  };

  // Render the appropriate chart based on type
  const renderChart = () => {
    switch (type) {
      case 'bar':
        return renderBarChart();
      case 'pie':
        return renderPieChart();
      case 'radar':
        return renderRadarChart();
      case 'line':
        return renderLineChart();
      default:
        return <div>Invalid chart type</div>;
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      {renderChart()}
    </div>
  );
};

export default SentimentVisualizer;


