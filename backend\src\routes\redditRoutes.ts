import { Router } from 'express';
import * as redditController from '../controllers/redditController';

const router = Router();

/**
 * @swagger
 * /reddit/discussions:
 *   get:
 *     summary: Get trending Reddit discussions
 *     tags: [Reddit]
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Category filter
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [day, week, month]
 *           default: week
 *         description: Time period for trend data
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           minimum: 1
 *           maximum: 50
 *         description: Number of results to return
 *     responses:
 *       200:
 *         description: Trending Reddit discussions
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Server error
 */
router.get('/discussions', redditController.getTrendingDiscussions);

/**
 * @swagger
 * /reddit/products:
 *   get:
 *     summary: Get trending Reddit products with sentiment analysis
 *     tags: [Reddit]
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Category filter
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [day, week, month]
 *           default: week
 *         description: Time period for trend data
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *           minimum: 1
 *           maximum: 50
 *         description: Number of results to return
 *     responses:
 *       200:
 *         description: Trending Reddit products with sentiment analysis
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Server error
 */
router.get('/products', redditController.getTrendingProducts);

/**
 * @swagger
 * /reddit/cache/cleanup:
 *   post:
 *     summary: Clean up expired cache entries
 *     tags: [Reddit]
 *     responses:
 *       200:
 *         description: Cache cleanup result
 *       500:
 *         description: Server error
 */
router.post('/cache/cleanup', redditController.cleanupCache);

export default router;
