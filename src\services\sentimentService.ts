import { apiService } from './api';
import type { SentimentR<PERSON>ult, BatchSentimentResult, SentimentComparisonResult, AspectSentiment } from '../types/sentiment';

/**
 * Sentiment Analysis Service
 * Provides methods to analyze sentiment of text using the Python NLP service
 * Falls back to mock data when the service is unavailable
 */
class SentimentService {
  private nlpServiceUrl: string;

  constructor() {
    // Use the environment variable or default to localhost
    this.nlpServiceUrl = import.meta.env.VITE_SENTIMENT_API_URL || 'http://localhost:5000';
  }

  /**
   * Analyze sentiment of a single text
   * @param text Text to analyze
   * @returns Sentiment analysis result
   */
  async analyzeSentiment(text: string): Promise<SentimentResult> {
    try {
      const response = await fetch(`${this.nlpServiceUrl}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error analyzing sentiment:', error);

      // Return mock data as fallback
      return this.getMockSentimentResult(text);
    }
  }

  /**
   * Analyze sentiment of multiple texts
   * @param texts Array of texts to analyze
   * @returns Batch sentiment analysis result
   */
  async batchAnalyzeSentiment(texts: string[]): Promise<BatchSentimentResult> {
    try {
      const response = await fetch(`${this.nlpServiceUrl}/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ texts }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error batch analyzing sentiment:', error);

      // Return mock data as fallback
      return {
        results: texts.map(text => this.getMockSentimentResult(text)),
        count: texts.length
      };
    }
  }

  /**
   * Analyze sentiment of product reviews
   * @param productId Product ID
   * @returns Sentiment analysis result for product reviews
   */
  async analyzeProductReviews(productId: number): Promise<SentimentResult[]> {
    try {
      // Get product details with reviews
      const product = await apiService.getProductDetails(productId);

      if (!product.reviews || product.reviews.length === 0) {
        return [];
      }

      // Extract review comments
      const reviewTexts = product.reviews.map(review => review.comment);

      // Analyze sentiment of reviews
      const batchResult = await this.batchAnalyzeSentiment(reviewTexts);

      return batchResult.results;
    } catch (error) {
      console.error('Error analyzing product reviews:', error);
      return [];
    }
  }

  /**
   * Compare sentiment between two products
   * @param productId1 First product ID
   * @param productId2 Second product ID
   * @returns Comparison result
   */
  async compareProductSentiment(productId1: number, productId2: number): Promise<SentimentComparisonResult> {
    try {
      // Get product details with reviews
      const product1 = await apiService.getProductDetails(productId1);
      const product2 = await apiService.getProductDetails(productId2);

      if (!product1 || !product2) {
        throw new Error('Products not found');
      }

      // Analyze reviews for both products
      const product1Results = await this.analyzeProductReviews(productId1);
      const product2Results = await this.analyzeProductReviews(productId2);

      // Calculate average sentiment scores
      const getAverageSentiment = (results: SentimentResult[]) => {
        if (results.length === 0) return 0;
        const sum = results.reduce((acc, result) => acc + result.score, 0);
        return sum / results.length;
      };

      // Calculate aspect-based sentiment
      const getAspectSentiment = (results: SentimentResult[]): AspectSentiment[] => {
        const aspects: Record<string, { count: number, total: number }> = {};

        results.forEach(result => {
          if (result.aspects) {
            result.aspects.forEach(aspect => {
              if (!aspects[aspect.aspect]) {
                aspects[aspect.aspect] = { count: 0, total: 0 };
              }
              aspects[aspect.aspect].count++;
              aspects[aspect.aspect].total += aspect.sentiment;
            });
          }
        });

        return Object.entries(aspects).map(([aspect, data]) => ({
          aspect,
          sentiment: data.total / data.count,
          count: data.count
        }));
      };

      // Prepare comparison data
      const comparison = {
        products: [
          {
            id: product1.id,
            name: product1.name,
            brand: product1.brand,
            imageUrl: product1.imageUrl,
            reviewCount: product1.reviewCount,
            overallSentiment: product1.sentimentScore,
            analyzedReviews: product1Results.length,
            averageSentiment: getAverageSentiment(product1Results),
            aspects: getAspectSentiment(product1Results)
          },
          {
            id: product2.id,
            name: product2.name,
            brand: product2.brand,
            imageUrl: product2.imageUrl,
            reviewCount: product2.reviewCount,
            overallSentiment: product2.sentimentScore,
            analyzedReviews: product2Results.length,
            averageSentiment: getAverageSentiment(product2Results),
            aspects: getAspectSentiment(product2Results)
          }
        ],
        // Find common aspects for direct comparison
        commonAspects: this.findCommonAspects(
          getAspectSentiment(product1Results),
          getAspectSentiment(product2Results)
        )
      };

      return comparison;
    } catch (error) {
      console.error('Error comparing product sentiment:', error);
      throw error;
    }
  }

  /**
   * Find common aspects between two products for comparison
   */
  private findCommonAspects(aspects1: AspectSentiment[], aspects2: AspectSentiment[]): SentimentComparisonResult['commonAspects'] {
    const aspects1Map = new Map(aspects1.map(a => [a.aspect, a]));
    const commonAspects = [];

    for (const aspect2 of aspects2) {
      if (aspects1Map.has(aspect2.aspect)) {
        const aspect1 = aspects1Map.get(aspect2.aspect);
        commonAspects.push({
          aspect: aspect2.aspect,
          product1: {
            sentiment: aspect1?.sentiment ?? 0,
            count: aspect1?.count ?? 0
          },
          product2: {
            sentiment: aspect2.sentiment,
            count: aspect2.count
          },
          difference: aspect1?.sentiment ?? 0 - aspect2.sentiment
        });
      }
    }

    // Sort by absolute difference (largest differences first)
    return commonAspects.sort((a, b) => Math.abs(b.difference) - Math.abs(a.difference));
  }

  /**
   * Generate mock sentiment result for fallback
   * @param text Text to analyze
   * @returns Mock sentiment result
   */
  private getMockSentimentResult(text: string): SentimentResult {
    // Simple sentiment analysis based on keywords
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'best', 'perfect'];
    const negativeWords = ['bad', 'poor', 'terrible', 'worst', 'hate', 'disappointing', 'awful'];

    const lowerText = text.toLowerCase();

    let positiveCount = 0;
    let negativeCount = 0;

    positiveWords.forEach(word => {
      if (lowerText.includes(word)) positiveCount++;
    });

    negativeWords.forEach(word => {
      if (lowerText.includes(word)) negativeCount++;
    });

    const total = positiveCount + negativeCount;
    const pos = total > 0 ? positiveCount / total : 0;
    const neg = total > 0 ? negativeCount / total : 0;
    const neu = total > 0 ? 0 : 1;
    const compound = pos - neg;

    return {
      score: compound,
      details: {
        compound,
        pos,
        neg,
        neu
      },
      text_sample: text.length > 100 ? text.substring(0, 100) + '...' : text
    };
  }
}

// Export singleton instance
export const sentimentService = new SentimentService();






