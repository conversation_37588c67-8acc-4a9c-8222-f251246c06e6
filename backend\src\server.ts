import dotenv from 'dotenv';
import http from 'http';
import { app } from './app';
import logger from './utils/logger';
import { connectDatabase } from './config/database';
import { connectRedis } from './config/redis';
import { redditCacheRefreshJob } from './jobs/redditCacheRefreshJob';

// Load environment variables
dotenv.config();

// Set port
const PORT = process.env.PORT || 8000;

// Initialize services and start server
const startServer = async () => {
  try {
    logger.info('Starting ProductWhisper API server...');

    // Connect to database
    await connectDatabase();
    logger.info('Database connection established');

    // Connect to Redis
    try {
      await connectRedis();
      logger.info('Redis connection established');

      // Start background jobs
      redditCacheRefreshJob.start();
      logger.info('Background jobs started');
    } catch (error) {
      logger.warn('Redis connection failed, continuing without caching');
    }

    // Create HTTP server
    const server = http.createServer(app);

    // Start server
    server.listen(PORT, () => {
      logger.info(`Server running on port ${PORT} in ${process.env.NODE_ENV} mode`);
    });

    // Handle graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM signal received: closing HTTP server');

      // Stop background jobs
      redditCacheRefreshJob.stop();
      logger.info('Background jobs stopped');

      server.close(() => {
        logger.info('HTTP server closed');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      logger.info('SIGINT signal received: closing HTTP server');

      // Stop background jobs
      redditCacheRefreshJob.stop();
      logger.info('Background jobs stopped');

      server.close(() => {
        logger.info('HTTP server closed');
        process.exit(0);
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();
