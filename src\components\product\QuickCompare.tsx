import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { FiX, FiArrowRight, FiPlus, FiCheck } from 'react-icons/fi';
import Button from '../../common/components/Button';

export interface QuickCompareProduct {
  id: number;
  name: string;
  image?: string;
  category?: string;
}

interface QuickCompareProps {
  maxProducts?: number;
}

const QuickCompare: React.FC<QuickCompareProps> = ({ maxProducts = 4 }) => {
  const [products, setProducts] = useState<QuickCompareProduct[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();

  // Load products from localStorage on mount
  useEffect(() => {
    try {
      const savedProducts = localStorage.getItem('quickCompareProducts');
      if (savedProducts) {
        const parsedProducts = JSON.parse(savedProducts);
        if (Array.isArray(parsedProducts) && parsedProducts.length > 0) {
          setProducts(parsedProducts);
          setIsVisible(true);
        }
      }
    } catch (error) {
      console.error('Error loading quick compare products:', error);
    }
  }, []);

  // Save products to localStorage when they change
  useEffect(() => {
    try {
      if (products.length > 0) {
        localStorage.setItem('quickCompareProducts', JSON.stringify(products));
      } else {
        localStorage.removeItem('quickCompareProducts');
      }
    } catch (error) {
      console.error('Error saving quick compare products:', error);
    }
  }, [products]);

  // Add a product to the comparison
  const addProduct = (product: QuickCompareProduct) => {
    // Check if product already exists
    if (products.some(p => p.id === product.id)) {
      return;
    }

    // Add product, limiting to maxProducts
    setProducts(prev => {
      const newProducts = [...prev, product].slice(0, maxProducts);
      setIsVisible(true);
      return newProducts;
    });
  };

  // Remove a product from the comparison
  const removeProduct = (productId: number) => {
    setProducts(prev => {
      const newProducts = prev.filter(p => p.id !== productId);
      if (newProducts.length === 0) {
        setIsVisible(false);
      }
      return newProducts;
    });
  };

  // Clear all products
  const clearProducts = () => {
    setProducts([]);
    setIsVisible(false);
  };

  // Navigate to comparison page
  const goToComparison = () => {
    if (products.length >= 2) {
      const productIds = products.map(p => p.id).join(',');
      navigate(`/comparison?ids=${productIds}`);
    }
  };

  // Check if a product is in the comparison
  const isProductInComparison = (productId: number) => {
    return products.some(p => p.id === productId);
  };

  return (
    <>
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 bg-white rounded-xl shadow-xl border border-gray-200 overflow-hidden"
            style={{ width: 'min(90vw, 600px)' }}
          >
            <div className="p-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
              <h3 className="font-medium text-gray-800">Quick Compare ({products.length}/{maxProducts})</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={clearProducts}
                  className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                >
                  Clear All
                </button>
                <button
                  onClick={() => setIsVisible(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                  aria-label="Minimize"
                >
                  <FiX size={18} />
                </button>
              </div>
            </div>
            
            <div className="p-4">
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                {products.map((product) => (
                  <motion.div
                    key={product.id}
                    className="relative bg-gray-50 rounded-lg p-2 border border-gray-100"
                    whileHover={{ y: -2 }}
                  >
                    <button
                      onClick={() => removeProduct(product.id)}
                      className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-sm border border-gray-200 text-gray-400 hover:text-red-500 transition-colors"
                      aria-label="Remove product"
                    >
                      <FiX size={14} />
                    </button>
                    
                    <div className="w-full h-16 bg-white rounded-md mb-2 flex items-center justify-center overflow-hidden">
                      {product.image ? (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = `https://via.placeholder.com/100x100?text=${encodeURIComponent(product.name.charAt(0))}`;
                          }}
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-100 flex items-center justify-center text-gray-400">
                          {product.name.charAt(0)}
                        </div>
                      )}
                    </div>
                    
                    <div className="text-xs font-medium text-gray-800 line-clamp-2 h-10">
                      {product.name}
                    </div>
                    
                    {product.category && (
                      <div className="text-xs text-gray-500 mt-1">
                        {product.category}
                      </div>
                    )}
                  </motion.div>
                ))}
                
                {Array.from({ length: Math.max(0, maxProducts - products.length) }).map((_, index) => (
                  <div
                    key={`empty-${index}`}
                    className="bg-gray-50 rounded-lg p-2 border border-dashed border-gray-200 flex items-center justify-center h-[106px]"
                  >
                    <div className="text-gray-400 text-xs text-center">
                      <FiPlus size={20} className="mx-auto mb-1" />
                      Add Product
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 flex justify-end">
                <Button
                  onClick={goToComparison}
                  disabled={products.length < 2}
                  className={`px-4 py-2 text-sm ${products.length < 2 ? 'opacity-50 cursor-not-allowed' : ''}`}
                  rightIcon={<FiArrowRight size={16} />}
                >
                  Compare Now
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// Create a context to share the QuickCompare functionality
const QuickCompareContext = React.createContext<{
  addProduct: (product: QuickCompareProduct) => void;
  removeProduct: (productId: number) => void;
  isProductInComparison: (productId: number) => boolean;
  toggleVisibility: () => void;
}>({
  addProduct: () => {},
  removeProduct: () => {},
  isProductInComparison: () => false,
  toggleVisibility: () => {},
});

// Export the context
export const useQuickCompare = () => React.useContext(QuickCompareContext);

// Export the provider
export const QuickCompareProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [instance] = useState(() => new QuickCompare({ maxProducts: 4 }));
  const [products, setProducts] = useState<QuickCompareProduct[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  // Context value
  const value = React.useMemo(() => ({
    addProduct: (product: QuickCompareProduct) => {
      // Implementation
    },
    removeProduct: (productId: number) => {
      // Implementation
    },
    isProductInComparison: (productId: number) => {
      // Implementation
      return false;
    },
    toggleVisibility: () => {
      // Implementation
    },
  }), []);

  return (
    <QuickCompareContext.Provider value={value}>
      {children}
      <QuickCompare maxProducts={4} />
    </QuickCompareContext.Provider>
  );
};

export default QuickCompare;
