import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>heck, FiPlus } from 'react-icons/fi';
import type { QuickCompareProduct } from './QuickCompare';

interface CompareButtonProps {
  product: QuickCompareProduct;
  isInComparison: boolean;
  onAddToCompare: (product: QuickCompareProduct) => void;
  onRemoveFromCompare: (productId: number) => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const CompareButton: React.FC<CompareButtonProps> = ({
  product,
  isInComparison,
  onAddToCompare,
  onRemoveFromCompare,
  size = 'md',
  className = '',
}) => {
  const handleClick = () => {
    if (isInComparison) {
      onRemoveFromCompare(product.id);
    } else {
      onAddToCompare(product);
    }
  };

  // Size classes
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2',
  };

  return (
    <motion.button
      onClick={handleClick}
      className={`
        flex items-center justify-center rounded-full font-medium
        transition-colors
        ${isInComparison 
          ? 'bg-primary text-white hover:bg-primary-dark' 
          : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        }
        ${sizeClasses[size]}
        ${className}
      `}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
    >
      {isInComparison ? (
        <>
          <FiCheck className="mr-1" size={size === 'sm' ? 12 : size === 'md' ? 14 : 16} />
          <span>Added</span>
        </>
      ) : (
        <>
          <FiPlus className="mr-1" size={size === 'sm' ? 12 : size === 'md' ? 14 : 16} />
          <span>Compare</span>
        </>
      )}
    </motion.button>
  );
};

export default CompareButton;
