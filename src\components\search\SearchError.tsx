import React from 'react';
import { motion } from 'framer-motion';
import { FiAlertCircle, FiRefreshCw } from 'react-icons/fi';

interface SearchErrorProps {
  message: string;
  onRetry?: () => void;
}

const SearchError: React.FC<SearchErrorProps> = ({ message, onRetry }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-red-50 border border-red-100 rounded-xl p-6 text-center my-8"
    >
      <div className="flex flex-col items-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <FiAlertCircle className="text-red-500" size={32} />
        </div>
        
        <h3 className="text-lg font-medium text-red-800 mb-2">Search Error</h3>
        
        <p className="text-red-600 mb-6 max-w-md mx-auto">
          {message || "We couldn't complete your search. Please try again."}
        </p>
        
        {onRetry && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onRetry}
            className="bg-white border border-red-200 text-red-600 px-4 py-2 rounded-lg flex items-center hover:bg-red-50 transition-colors"
          >
            <FiRefreshCw className="mr-2" size={16} />
            Try Again
          </motion.button>
        )}
      </div>
    </motion.div>
  );
};

export default SearchError;
