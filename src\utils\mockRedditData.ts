/**
 * Mock data utilities for Reddit trends
 */

/**
 * Generate mock Reddit trending discussions
 * @param category Optional category filter
 * @param timeframe Timeframe ('day', 'week', 'month')
 * @param limit Maximum number of results
 * @returns Mock trending discussions data
 */
export const getMockRedditTrends = (
  category?: string,
  timeframe: string = 'week',
  limit: number = 20
): any => {
  // Generate mock product names based on category
  const productNames = generateMockProductNames(category, limit);
  
  // Generate trending discussions
  const trendingDiscussions = productNames.map((productName, index) => {
    const mentionCount = Math.floor(Math.random() * 50) + 5;
    const totalScore = Math.floor(Math.random() * 5000) + 500;
    
    return {
      product_name: productName,
      mention_count: mentionCount,
      total_score: totalScore,
      average_score: totalScore / mentionCount,
      posts: generateMockPosts(productName, Math.min(5, mentionCount))
    };
  });
  
  // Sort by mention count
  trendingDiscussions.sort((a, b) => b.mention_count - a.mention_count);
  
  return {
    category: category || 'all',
    timeframe,
    count: trendingDiscussions.length,
    trending_products: trendingDiscussions.slice(0, limit)
  };
};

/**
 * Generate mock Reddit trending products with sentiment
 * @param category Optional category filter
 * @param timeframe Timeframe ('day', 'week', 'month')
 * @param limit Maximum number of results
 * @returns Mock trending products with sentiment data
 */
export const getMockRedditProductTrends = (
  category?: string,
  timeframe: string = 'week',
  limit: number = 10
): any => {
  // Generate mock product names based on category
  const productNames = generateMockProductNames(category, limit);
  
  // Generate trending products with sentiment
  const trendingProducts = productNames.map((productName, index) => {
    const mentionCount = Math.floor(Math.random() * 50) + 5;
    const totalScore = Math.floor(Math.random() * 5000) + 500;
    const sentimentScore = (Math.random() * 0.6) + 0.3; // Between 0.3 and 0.9
    
    return {
      product_name: productName,
      mention_count: mentionCount,
      total_score: totalScore,
      average_score: totalScore / mentionCount,
      posts: generateMockPosts(productName, Math.min(5, mentionCount)),
      sentiment: {
        score: sentimentScore,
        details: {
          compound: sentimentScore,
          neg: 1 - sentimentScore,
          neu: 0.2,
          pos: sentimentScore
        },
        aspects: generateMockAspects(productName)
      }
    };
  });
  
  // Sort by sentiment score
  trendingProducts.sort((a, b) => b.sentiment.score - a.sentiment.score);
  
  return {
    category: category || 'all',
    timeframe,
    count: trendingProducts.length,
    trending_products: trendingProducts.slice(0, limit)
  };
};

/**
 * Generate mock product names based on category
 * @param category Optional category
 * @param count Number of product names to generate
 * @returns Array of product names
 */
const generateMockProductNames = (category?: string, count: number = 20): string[] => {
  const productPrefixes = [
    'Ultra', 'Pro', 'Max', 'Elite', 'Premium', 'Advanced', 'Smart', 'Super', 
    'Extreme', 'Deluxe', 'Ultimate', 'Precision', 'Performance', 'Next-Gen'
  ];
  
  const productTypes: Record<string, string[]> = {
    electronics: [
      'Laptop', 'Smartphone', 'Tablet', 'Smartwatch', 'Earbuds', 'TV', 
      'Camera', 'Speaker', 'Monitor', 'Router', 'Keyboard', 'Mouse'
    ],
    audio: [
      'Headphones', 'Earbuds', 'Speaker', 'Soundbar', 'Microphone', 
      'Amplifier', 'Turntable', 'Receiver', 'DAC', 'Subwoofer'
    ],
    smartphones: [
      'Phone', 'Smartphone', 'Phablet', 'Mobile', 'Foldable', 
      'Flip Phone', 'Camera Phone', 'Gaming Phone'
    ],
    gaming: [
      'Console', 'Controller', 'Gaming PC', 'Gaming Laptop', 'Gaming Mouse', 
      'Gaming Keyboard', 'Gaming Headset', 'Gaming Chair', 'VR Headset'
    ],
    default: [
      'Device', 'Gadget', 'Product', 'Tech', 'Accessory', 'Tool', 
      'Appliance', 'Equipment', 'System', 'Solution'
    ]
  };
  
  // Determine which product types to use
  let types = productTypes.default;
  if (category && productTypes[category.toLowerCase()]) {
    types = productTypes[category.toLowerCase()];
  } else if (!category) {
    // Mix of all categories
    types = [
      ...productTypes.electronics,
      ...productTypes.audio,
      ...productTypes.smartphones,
      ...productTypes.gaming
    ];
  }
  
  // Generate brand names
  const brands = [
    'TechMaster', 'SoundWave', 'QuantumX', 'NexGen', 'FusionTech', 
    'ApexGear', 'PrimeTech', 'InnovateX', 'EliteWare', 'PulseTech',
    'SonicPro', 'VisionTech', 'CoreLogic', 'OmniTech', 'ZenithX'
  ];
  
  // Generate product names
  const productNames: string[] = [];
  for (let i = 0; i < count; i++) {
    const brand = brands[Math.floor(Math.random() * brands.length)];
    const prefix = productPrefixes[Math.floor(Math.random() * productPrefixes.length)];
    const type = types[Math.floor(Math.random() * types.length)];
    const model = Math.floor(Math.random() * 1000) + 1;
    
    productNames.push(`${brand} ${prefix} ${type} ${model}`);
  }
  
  return productNames;
};

/**
 * Generate mock Reddit posts
 * @param productName Product name
 * @param count Number of posts to generate
 * @returns Array of mock posts
 */
const generateMockPosts = (productName: string, count: number): any[] => {
  const posts = [];
  
  for (let i = 0; i < count; i++) {
    const id = Math.random().toString(36).substring(2, 10);
    const score = Math.floor(Math.random() * 1000) + 50;
    const daysAgo = Math.floor(Math.random() * 30);
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    
    // Generate title based on patterns
    let title = '';
    const pattern = Math.floor(Math.random() * 5);
    
    switch (pattern) {
      case 0:
        title = `Review: ${productName} - Is it worth the hype?`;
        break;
      case 1:
        title = `Just got the ${productName} and here are my thoughts`;
        break;
      case 2:
        title = `${productName} vs Competition - Detailed comparison`;
        break;
      case 3:
        title = `Should you buy the ${productName}? My experience after ${Math.floor(Math.random() * 12) + 1} months`;
        break;
      case 4:
        title = `The ${productName} is ${Math.random() > 0.7 ? 'disappointing' : 'amazing'} - Here's why`;
        break;
    }
    
    posts.push({
      id,
      title,
      url: `https://reddit.com/r/tech/comments/${id}`,
      score,
      created_at: date.toISOString()
    });
  }
  
  return posts;
};

/**
 * Generate mock aspects for sentiment analysis
 * @param productName Product name
 * @returns Array of aspect sentiment data
 */
const generateMockAspects = (productName: string): any[] => {
  const commonAspects = [
    'quality', 'price', 'performance', 'design', 'battery', 
    'durability', 'features', 'usability', 'support', 'value'
  ];
  
  // Select 3-6 random aspects
  const aspectCount = Math.floor(Math.random() * 4) + 3;
  const selectedAspects = [];
  
  for (let i = 0; i < aspectCount; i++) {
    const aspect = commonAspects[Math.floor(Math.random() * commonAspects.length)];
    if (!selectedAspects.find(a => a.name === aspect)) {
      const score = (Math.random() * 0.6) + 0.3; // Between 0.3 and 0.9
      selectedAspects.push({
        name: aspect,
        score,
        mentions: Math.floor(Math.random() * 20) + 1
      });
    }
  }
  
  return selectedAspects;
};
