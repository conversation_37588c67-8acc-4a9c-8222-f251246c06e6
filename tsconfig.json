{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "noPropertyAccessFromIndexSignature": false, "noImplicitThis": true, "alwaysStrict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2022"], "types": ["node", "jest"], "typeRoots": ["./node_modules/@types", "./src/types"], "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/types/*": ["./types/*"], "@/utils/*": ["./utils/*"], "@/services/*": ["./services/*"], "@/middleware/*": ["./middleware/*"], "@/routes/*": ["./routes/*"], "@/models/*": ["./models/*"], "@/controllers/*": ["./controllers/*"]}}, "include": ["src/**/*", "src/**/*.json"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "python-nlp-service"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}