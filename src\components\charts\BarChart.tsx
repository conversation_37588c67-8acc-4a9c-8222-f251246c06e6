import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface BarData {
  label: string;
  value: number;
  color?: string;
  secondaryValue?: number;
  secondaryColor?: string;
}

interface BarChartProps {
  data: BarData[];
  height?: number;
  width?: string;
  showValues?: boolean;
  showLabels?: boolean;
  showTooltip?: boolean;
  horizontal?: boolean;
  animate?: boolean;
  className?: string;
  formatValue?: (value: number) => string;
  maxValue?: number;
  barPadding?: number;
  barRadius?: number;
  defaultColor?: string;
  defaultSecondaryColor?: string;
  labelPosition?: 'top' | 'bottom' | 'left' | 'right';
}

const BarChart: React.FC<BarChartProps> = ({
  data,
  height = 300,
  width = '100%',
  showValues = true,
  showLabels = true,
  showTooltip = true,
  horizontal = false,
  animate = true,
  className = '',
  formatValue = (value) => value.toString(),
  maxValue,
  barPadding = 0.2,
  barRadius = 4,
  defaultColor = '#3b82f6',
  defaultSecondaryColor = '#93c5fd',
  labelPosition = 'bottom',
}) => {
  const [activeBar, setActiveBar] = useState<number | null>(null);

  // Calculate dimensions
  const margin = { top: 20, right: 20, bottom: 40, left: 40 };
  const chartWidth = 1000 - margin.left - margin.right;
  const chartHeight = height - margin.top - margin.bottom;

  // Calculate max value if not provided
  const calculatedMaxValue = maxValue || Math.max(
    ...data.map(d => Math.max(d.value, d.secondaryValue || 0))
  ) * 1.1; // Add 10% padding

  // Calculate bar dimensions
  const barWidth = horizontal
    ? (chartHeight / data.length) * (1 - barPadding)
    : (chartWidth / data.length) * (1 - barPadding);

  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      <svg
        width="100%"
        height={height}
        viewBox={`0 0 ${chartWidth + margin.left + margin.right} ${chartHeight + margin.top + margin.bottom}`}
        preserveAspectRatio="none"
      >
        <g transform={`translate(${margin.left}, ${margin.top})`}>
          {/* X axis (for vertical bars) or Y axis (for horizontal bars) */}
          {horizontal ? (
            <line
              x1={0}
              y1={0}
              x2={0}
              y2={chartHeight}
              stroke="#e5e7eb"
              strokeWidth={1}
            />
          ) : (
            <line
              x1={0}
              y1={chartHeight}
              x2={chartWidth}
              y2={chartHeight}
              stroke="#e5e7eb"
              strokeWidth={1}
            />
          )}

          {/* Grid lines */}
          {[0.2, 0.4, 0.6, 0.8, 1].map((t) => {
            const value = calculatedMaxValue * t;
            return horizontal ? (
              <g key={`grid-${t}`}>
                <line
                  x1={0}
                  y1={0}
                  x2={(value / calculatedMaxValue) * chartWidth}
                  y2={0}
                  stroke="#e5e7eb"
                  strokeWidth={1}
                  strokeDasharray="4 4"
                  transform={`translate(0, ${chartHeight})`}
                />
                <text
                  x={(value / calculatedMaxValue) * chartWidth}
                  y={chartHeight + 20}
                  fontSize="10"
                  textAnchor="middle"
                  fill="#6b7280"
                >
                  {formatValue(value)}
                </text>
              </g>
            ) : (
              <g key={`grid-${t}`}>
                <line
                  x1={0}
                  y1={chartHeight - (value / calculatedMaxValue) * chartHeight}
                  x2={chartWidth}
                  y2={chartHeight - (value / calculatedMaxValue) * chartHeight}
                  stroke="#e5e7eb"
                  strokeWidth={1}
                  strokeDasharray="4 4"
                />
                <text
                  x={-5}
                  y={chartHeight - (value / calculatedMaxValue) * chartHeight}
                  fontSize="10"
                  textAnchor="end"
                  dominantBaseline="middle"
                  fill="#6b7280"
                >
                  {formatValue(value)}
                </text>
              </g>
            );
          })}

          {/* Bars */}
          {data.map((d, i) => {
            const barHeight = (d.value / calculatedMaxValue) * (horizontal ? chartWidth : chartHeight);
            const secondaryBarHeight = d.secondaryValue
              ? (d.secondaryValue / calculatedMaxValue) * (horizontal ? chartWidth : chartHeight)
              : 0;
            
            const x = horizontal
              ? 0
              : (chartWidth / data.length) * (i + barPadding / 2);
            
            const y = horizontal
              ? (chartHeight / data.length) * (i + barPadding / 2)
              : chartHeight - barHeight;
            
            const secondaryY = horizontal
              ? (chartHeight / data.length) * (i + barPadding / 2)
              : chartHeight - secondaryBarHeight;

            return (
              <g key={`bar-${i}`}>
                {/* Secondary bar (if provided) */}
                {d.secondaryValue && (
                  <motion.rect
                    x={horizontal ? 0 : x}
                    y={horizontal ? y : secondaryY}
                    width={horizontal ? secondaryBarHeight : barWidth}
                    height={horizontal ? barWidth : secondaryBarHeight}
                    fill={d.secondaryColor || defaultSecondaryColor}
                    rx={barRadius}
                    ry={barRadius}
                    initial={animate ? { scale: 0 } : { scale: 1 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.1 * i }}
                    onMouseEnter={() => setActiveBar(i)}
                    onMouseLeave={() => setActiveBar(null)}
                  />
                )}

                {/* Main bar */}
                <motion.rect
                  x={horizontal ? 0 : x}
                  y={horizontal ? y : y}
                  width={horizontal ? barHeight : barWidth}
                  height={horizontal ? barWidth : barHeight}
                  fill={d.color || defaultColor}
                  rx={barRadius}
                  ry={barRadius}
                  initial={animate ? { scale: 0 } : { scale: 1 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 * i }}
                  onMouseEnter={() => setActiveBar(i)}
                  onMouseLeave={() => setActiveBar(null)}
                />

                {/* Bar value */}
                {showValues && (
                  <text
                    x={horizontal ? barHeight + 5 : x + barWidth / 2}
                    y={horizontal ? y + barWidth / 2 : y - 5}
                    fontSize="10"
                    textAnchor={horizontal ? "start" : "middle"}
                    dominantBaseline={horizontal ? "middle" : "auto"}
                    fill="#6b7280"
                    opacity={activeBar === i || activeBar === null ? 1 : 0.5}
                  >
                    {formatValue(d.value)}
                  </text>
                )}

                {/* Bar label */}
                {showLabels && (
                  <text
                    x={horizontal ? -5 : x + barWidth / 2}
                    y={horizontal ? y + barWidth / 2 : chartHeight + 15}
                    fontSize="10"
                    textAnchor={horizontal ? "end" : "middle"}
                    dominantBaseline={horizontal ? "middle" : "auto"}
                    fill="#6b7280"
                    opacity={activeBar === i || activeBar === null ? 1 : 0.5}
                  >
                    {d.label}
                  </text>
                )}

                {/* Tooltip */}
                {showTooltip && activeBar === i && (
                  <g>
                    <rect
                      x={horizontal ? barHeight + 10 : x - 40 + barWidth / 2}
                      y={horizontal ? y - 20 : y - 40}
                      width={80}
                      height={d.secondaryValue ? 50 : 30}
                      fill="white"
                      stroke="#e5e7eb"
                      strokeWidth={1}
                      rx={4}
                      ry={4}
                    />
                    <text
                      x={horizontal ? barHeight + 50 : x + barWidth / 2}
                      y={horizontal ? y - 5 : y - 25}
                      fontSize="10"
                      textAnchor="middle"
                      fill="#374151"
                      fontWeight="bold"
                    >
                      {d.label}
                    </text>
                    <text
                      x={horizontal ? barHeight + 50 : x + barWidth / 2}
                      y={horizontal ? y + 10 : y - 10}
                      fontSize="10"
                      textAnchor="middle"
                      fill="#6b7280"
                    >
                      {formatValue(d.value)}
                    </text>
                    {d.secondaryValue && (
                      <text
                        x={horizontal ? barHeight + 50 : x + barWidth / 2}
                        y={horizontal ? y + 25 : y + 5}
                        fontSize="10"
                        textAnchor="middle"
                        fill="#6b7280"
                      >
                        {formatValue(d.secondaryValue)}
                      </text>
                    )}
                  </g>
                )}
              </g>
            );
          })}
        </g>
      </svg>
    </div>
  );
};

export default BarChart;
