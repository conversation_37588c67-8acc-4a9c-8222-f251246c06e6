import React, { useMemo } from 'react';
import {type WordCloudItem } from '../types/sentiment';

interface SentimentWordCloudProps {
  words: WordCloudItem[];
  maxWords?: number;
  className?: string;
}

/**
 * Component for displaying a simple word cloud from sentiment analysis
 * Note: This is a simplified version without a full word cloud library
 */
const SentimentWordCloud: React.FC<SentimentWordCloudProps> = ({
  words,
  maxWords = 50,
  className = ''
}) => {
  // Get sentiment color based on score
  const getSentimentColor = (score: number): string => {
    if (score >= 0.6) return 'text-green-600';
    if (score >= 0.2) return 'text-green-400';
    if (score >= -0.2) return 'text-gray-500';
    if (score >= -0.6) return 'text-red-400';
    return 'text-red-600';
  };

  // Get font size based on value
  const getFontSize = (value: number, maxValue: number): string => {
    const minSize = 0.8;
    const maxSize = 2.5;
    const normalizedValue = value / maxValue;
    const size = minSize + normalizedValue * (maxSize - minSize);
    return `${size}rem`;
  };

  // Process and sort words
  const processedWords = useMemo(() => {
    try {
      if (!words || !Array.isArray(words) || words.length === 0) return [];

      // Sort by value (frequency) descending
      const sortedWords = [...words].sort((a, b) => b.value - a.value);

      // Take top N words
      const topWords = sortedWords.slice(0, maxWords);

      // Find max value for scaling
      const maxValue = Math.max(...topWords.map(word => word.value || 1));

      // Add display properties
      return topWords.map(word => ({
        ...word,
        color: getSentimentColor(word.sentiment || 0),
        fontSize: getFontSize(word.value || 1, maxValue || 1)
      }));
    } catch (error) {
      console.error('Error processing word cloud data:', error);
      return [];
    }
  }, [words, maxWords]);

  if (!processedWords || processedWords.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 text-center ${className}`}>
        <p className="text-gray-500">No word data available</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      <div className="flex flex-wrap justify-center gap-2 p-4">
        {processedWords.map((word, index) => (
          <span
            key={index}
            className={`${word.color} transition-all hover:scale-110`}
            style={{ fontSize: word.fontSize }}
          >
            {word.text}
          </span>
        ))}
      </div>
    </div>
  );
};

export default SentimentWordCloud;
