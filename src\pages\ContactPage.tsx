import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiMail,
  FiPhone,
  FiMapPin,
  FiSend,
  FiMessageCircle,
  FiHelpCircle,
  FiCheckCircle,
  FiAlertCircle,
  FiClock,
  FiArrowRight,
  FiGlobe,
  FiHeadphones,
  FiUser,
  FiChevronDown
} from 'react-icons/fi';
import AnimatedCircles from '../components/ui/AnimatedCircles';

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Reset form and show success message
      setFormData({ name: '', email: '', subject: '', message: '' });
      setSubmitSuccess(true);

      // Hide success message after 5 seconds
      setTimeout(() => setSubmitSuccess(false), 5000);
    } catch (_) {
      setSubmitError('There was an error sending your message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
      {/* Background animated elements */}
      <div className="absolute inset-0 opacity-10 pointer-events-none">
        <AnimatedCircles variant="mixed" count={8} speed="slow" blurAmount={2} />
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.7 }}
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"
      >
        {/* Hero Section - Modern Premium Design */}
        <div className="relative mb-20 bg-gradient-primary rounded-2xl overflow-hidden shadow-premium">
          {/* Background gradient elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-dark/80 to-primary/90"></div>
            <div className="absolute top-10 left-10 w-64 h-64 rounded-full bg-secondary/30 blur-3xl"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 rounded-full bg-accent/20 blur-3xl"></div>
            <div className="absolute top-1/3 right-1/4 w-40 h-40 rounded-full bg-primary-light/30 blur-2xl"></div>
            <div className="absolute bottom-1/3 left-1/4 w-32 h-32 rounded-full bg-white/10 blur-2xl"></div>
          </div>

          <motion.div
            className="relative z-10 px-6 py-16 sm:px-12 sm:py-20 flex flex-col items-center justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            <motion.div
              className="inline-flex items-center justify-center p-2 bg-white/10 backdrop-blur-sm rounded-full mb-6"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: "spring", stiffness: 300, damping: 20, delay: 0.3 }}
            >
              <div className="bg-white/20 p-3 rounded-full">
                <FiMessageCircle className="w-6 h-6 text-white" />
              </div>
            </motion.div>

            <h1 className="text-4xl md:text-6xl font-display font-bold text-white mb-6 text-center">Get in Touch</h1>
            <p className="text-white/90 text-xl max-w-2xl mx-auto text-center leading-relaxed">
              Have questions about ProductWhisper? We're here to help and would love to hear from you.
            </p>

            <motion.div
              className="mt-8 w-24 h-1 bg-secondary rounded-full"
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 96, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            />

            <motion.div
              className="mt-10 flex flex-wrap justify-center gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <motion.a
                href="#contact-form"
                className="px-6 py-3 bg-white text-primary font-medium rounded-lg shadow-md hover:shadow-lg transition-all inline-flex items-center"
                whileHover={{ scale: 1.03, y: -2 }}
                whileTap={{ scale: 0.98 }}
              >
                Send a Message
                <FiArrowRight className="ml-2" />
              </motion.a>
              <motion.a
                href="tel:+1234567890"
                className="px-6 py-3 bg-white/10 backdrop-blur-sm text-white font-medium rounded-lg border border-white/20 hover:bg-white/20 transition-all inline-flex items-center"
                whileHover={{ scale: 1.03, y: -2 }}
                whileTap={{ scale: 0.98 }}
              >
                <FiPhone className="mr-2" />
                Call Us
              </motion.a>
            </motion.div>
          </motion.div>
        </div>

        {/* Contact Cards - Modern Premium Design */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 -mt-28">
          {[
            {
              icon: FiMail,
              title: "Email Us",
              description: "Our friendly team is here to help with any questions.",
              action: "<EMAIL>",
              actionHref: "mailto:<EMAIL>",
              color: "primary"
            },
            {
              icon: FiHeadphones,
              title: "Call Us",
              description: "Mon-Fri from 8am to 5pm PST. We'd love to hear from you.",
              action: "+1 (234) 567-890",
              actionHref: "tel:+1234567890",
              color: "secondary"
            },
            {
              icon: FiGlobe,
              title: "Visit Us",
              description: "Come say hello at our headquarters in Tech City.",
              action: "123 Innovation Way, Tech City, CA 94043",
              actionHref: "https://maps.google.com",
              color: "accent"
            }
          ].map((card, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl shadow-premium p-8 flex flex-col items-center text-center relative overflow-hidden group"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 20,
                delay: 0.3 + (index * 0.1)
              }}
              whileHover={{
                y: -5,
                boxShadow: "0 20px 40px -5px rgba(0, 0, 0, 0.1), 0 10px 15px -5px rgba(0, 0, 0, 0.05)"
              }}
            >
              {/* Background decoration */}
              <div className={`absolute top-0 left-0 w-full h-1 bg-${card.color}`}></div>
              <div className={`absolute -right-12 -top-12 w-24 h-24 rounded-full bg-${card.color}/5 group-hover:bg-${card.color}/10 transition-colors duration-300`}></div>

              {/* Icon */}
              <motion.div
                className={`bg-${card.color}/10 p-5 rounded-full mb-6 text-${card.color} relative z-10`}
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <card.icon size={32} />
              </motion.div>

              {/* Content */}
              <h3 className="text-2xl font-display font-semibold mb-3 text-gray-900">{card.title}</h3>
              <p className="text-gray-600 mb-6">{card.description}</p>
              <a
                href={card.actionHref}
                className={`text-${card.color} font-medium hover:text-${card.color}-dark transition-colors inline-flex items-center group-hover:underline`}
                target={card.title === "Visit Us" ? "_blank" : undefined}
                rel={card.title === "Visit Us" ? "noopener noreferrer" : undefined}
              >
                {card.action}
                <motion.span
                  className="inline-block ml-2"
                  initial={{ x: 0 }}
                  whileHover={{ x: 5 }}
                >
                  <FiArrowRight size={16} />
                </motion.span>
              </a>
            </motion.div>
          ))}
        </div>

        {/* Contact Form Section - Modern Premium Design */}
        <div id="contact-form" className="grid grid-cols-1 lg:grid-cols-5 gap-10 mb-20">
          {/* Left Column - Form */}
          <div className="lg:col-span-3">
            <motion.div
              className="bg-white rounded-xl shadow-premium p-8 border border-gray-100 relative overflow-hidden"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.3 }}
            >
              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary to-primary-light"></div>
              <div className="absolute -right-16 -bottom-16 w-32 h-32 rounded-full bg-primary/5 opacity-70"></div>
              <div className="absolute -left-16 -top-16 w-32 h-32 rounded-full bg-secondary/5 opacity-70"></div>

              <div className="flex items-center mb-8">
                <motion.div
                  className="bg-primary/10 p-4 rounded-full text-primary mr-5 flex-shrink-0"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <FiMessageCircle size={28} />
                </motion.div>
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 font-display">Send Us a Message</h2>
                  <div className="w-20 h-1 bg-primary/20 mt-2"></div>
                </div>
              </div>

              <AnimatePresence>
                {submitSuccess && (
                  <motion.div
                    className="bg-success/10 border border-success/20 text-success px-6 py-5 rounded-lg mb-8 flex items-start"
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  >
                    <div className="bg-white p-2 rounded-full shadow-sm mr-4 flex-shrink-0">
                      <FiCheckCircle className="text-success" size={20} />
                    </div>
                    <div>
                      <h4 className="font-medium text-success mb-1">Message Sent Successfully!</h4>
                      <p className="text-success/80">Thank you for your message! We'll get back to you soon.</p>
                    </div>
                  </motion.div>
                )}

                {submitError && (
                  <motion.div
                    className="bg-error/10 border border-error/20 text-error px-6 py-5 rounded-lg mb-8 flex items-start"
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  >
                    <div className="bg-white p-2 rounded-full shadow-sm mr-4 flex-shrink-0">
                      <FiAlertCircle className="text-error" size={20} />
                    </div>
                    <div>
                      <h4 className="font-medium text-error mb-1">Error</h4>
                      <p className="text-error/80">{submitError}</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              <form onSubmit={handleSubmit} className="space-y-6 relative z-10">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Your Name
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/30 transition-all shadow-sm"
                        placeholder="John Doe"
                        required
                      />
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                        <FiUser size={16} />
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Your Email
                    </label>
                    <div className="relative">
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/30 transition-all shadow-sm"
                        placeholder="<EMAIL>"
                        required
                      />
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                        <FiMail size={16} />
                      </div>
                    </div>
                  </motion.div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                    Subject
                  </label>
                  <div className="relative">
                    <select
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/30 transition-all shadow-sm appearance-none"
                      required
                    >
                      <option value="">Select a subject</option>
                      <option value="general">General Inquiry</option>
                      <option value="support">Technical Support</option>
                      <option value="feedback">Product Feedback</option>
                      <option value="partnership">Partnership Opportunity</option>
                      <option value="other">Other</option>
                    </select>
                    <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                      <FiHelpCircle size={16} />
                    </div>
                    <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
                      <FiChevronDown size={16} />
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                >
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Your Message
                  </label>
                  <div className="relative">
                    <textarea
                      id="message"
                      name="message"
                      rows={6}
                      value={formData.message}
                      onChange={handleChange}
                      className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/30 transition-all shadow-sm"
                      placeholder="How can we help you?"
                      required
                    ></textarea>
                    <div className="absolute left-3 top-6 text-gray-400">
                      <FiMessageCircle size={16} />
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                  className="pt-4"
                >
                  <motion.button
                    type="submit"
                    className="w-full px-8 py-4 bg-gradient-to-r from-primary to-primary-light text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all flex items-center justify-center"
                    disabled={isSubmitting}
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <FiSend className="mr-2" size={18} />
                        Send Message
                      </span>
                    )}
                  </motion.button>
                </motion.div>
              </form>
            </motion.div>
          </div>

          {/* Right Column - FAQ and Info */}
          <div className="lg:col-span-2">
            <motion.div
              className="bg-white rounded-xl shadow-premium p-8 border border-gray-100 relative overflow-hidden mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.4 }}
            >
              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-secondary to-secondary-light"></div>
              <div className="absolute -right-16 -bottom-16 w-32 h-32 rounded-full bg-secondary/5 opacity-70"></div>

              <div className="flex items-center mb-8">
                <motion.div
                  className="bg-secondary/10 p-4 rounded-full text-secondary mr-5 flex-shrink-0"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <FiHelpCircle size={28} />
                </motion.div>
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 font-display">Frequently Asked</h2>
                  <div className="w-20 h-1 bg-secondary/20 mt-2"></div>
                </div>
              </div>

              <div className="space-y-6 relative z-10">
                {[
                  {
                    question: "How does ProductWhisper work?",
                    answer: "ProductWhisper uses advanced sentiment analysis to analyze product reviews and provide insights into what people really think."
                  },
                  {
                    question: "Is there a free trial available?",
                    answer: "Yes, we offer a 14-day free trial with full access to all features. No credit card required."
                  },
                  {
                    question: "How accurate is the sentiment analysis?",
                    answer: "Our sentiment analysis has been trained on millions of reviews and achieves over 90% accuracy in most product categories."
                  }
                ].map((faq, index) => (
                  <motion.div
                    key={index}
                    className="bg-secondary/5 rounded-lg p-5 hover:bg-secondary/10 transition-colors duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 + (index * 0.1) }}
                    whileHover={{ x: 5 }}
                  >
                    <h3 className="font-medium text-gray-900 mb-2 flex items-center">
                      <span className="w-6 h-6 rounded-full bg-secondary/20 text-secondary flex items-center justify-center text-xs font-bold mr-3">
                        {index + 1}
                      </span>
                      {faq.question}
                    </h3>
                    <p className="text-gray-700 pl-9">{faq.answer}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              className="bg-gradient-to-br from-primary/10 to-secondary/5 rounded-xl shadow-premium border border-gray-100 p-8 relative overflow-hidden"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ type: "spring", stiffness: 100, damping: 20, delay: 0.5 }}
            >
              {/* Decorative elements */}
              <div className="absolute -left-16 -top-16 w-32 h-32 rounded-full bg-primary/10 opacity-50"></div>
              <div className="absolute -right-16 -bottom-16 w-32 h-32 rounded-full bg-secondary/10 opacity-50"></div>

              <div className="flex items-center mb-8">
                <motion.div
                  className="bg-white p-4 rounded-full text-primary shadow-md mr-5 flex-shrink-0"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <FiClock size={28} />
                </motion.div>
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 font-display">Business Hours</h2>
                  <div className="w-20 h-1 bg-primary/20 mt-2"></div>
                </div>
              </div>

              <div className="space-y-4 relative z-10">
                {[
                  { day: "Monday - Friday", hours: "8:00 AM - 5:00 PM", status: "open" },
                  { day: "Saturday", hours: "10:00 AM - 2:00 PM", status: "limited" },
                  { day: "Sunday", hours: "Closed", status: "closed" }
                ].map((schedule, index) => (
                  <motion.div
                    key={index}
                    className="flex justify-between items-center p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/50 shadow-sm"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 + (index * 0.1) }}
                    whileHover={{ y: -2 }}
                  >
                    <span className="text-gray-800 font-medium flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${
                        schedule.status === 'open' ? 'bg-success' :
                        schedule.status === 'limited' ? 'bg-warning' : 'bg-error'
                      }`}></span>
                      {schedule.day}
                    </span>
                    <span className="font-semibold text-gray-900">{schedule.hours}</span>
                  </motion.div>
                ))}

                <motion.div
                  className="pt-4 mt-4 border-t border-gray-200/50 text-center"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.9 }}
                >
                  <div className="bg-white/70 backdrop-blur-sm p-4 rounded-lg shadow-sm">
                    <p className="text-gray-700 text-sm">
                      All times are in Pacific Standard Time (PST).
                      <br />Our support team typically responds within 24 hours.
                    </p>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ContactPage;
