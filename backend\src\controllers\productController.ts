import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { ApiError } from '../middleware/errorHandler';
import * as productService from '../services/productService';
import logger from '../utils/logger';

/**
 * Get all products with pagination
 */
export const getProducts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    
    const products = await productService.getProducts(page, limit);
    
    res.status(StatusCodes.OK).json({
      success: true,
      count: products.length,
      page,
      limit,
      data: products,
    });
  } catch (error) {
    logger.error('Error in getProducts controller:', error);
    next(error);
  }
};

/**
 * Get a product by ID
 */
export const getProductById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const productId = parseInt(req.params.id);
    
    if (isNaN(productId)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Invalid product ID');
    }
    
    const product = await productService.getProductById(productId);
    
    if (!product) {
      throw new ApiError(StatusCodes.NOT_FOUND, `Product with ID ${productId} not found`);
    }
    
    res.status(StatusCodes.OK).json({
      success: true,
      data: product,
    });
  } catch (error) {
    logger.error(`Error in getProductById controller for ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Get reviews for a product
 */
export const getProductReviews = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const productId = parseInt(req.params.id);
    
    if (isNaN(productId)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Invalid product ID');
    }
    
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    
    const reviews = await productService.getProductReviews(productId, page, limit);
    
    res.status(StatusCodes.OK).json({
      success: true,
      count: reviews.length,
      page,
      limit,
      data: reviews,
    });
  } catch (error) {
    logger.error(`Error in getProductReviews controller for ID ${req.params.id}:`, error);
    next(error);
  }
};
