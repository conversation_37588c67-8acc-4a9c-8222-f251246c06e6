/**
 * Interface for sentiment analysis result
 */
export interface SentimentResult {
  score: number;
  details: {
    compound: number;
    neg: number;
    neu: number;
    pos: number;
  };
  text_sample?: string;
  entities?: {
    name: string;
    type: string;
    sentiment: number;
  }[];
  aspects?: {
    aspect: string;
    sentiment: number;
    sentence: string;
    adjectives: string[];
  }[];
  error?: string;
};

/**
 * Interface for batch sentiment analysis result
 */
export interface BatchSentimentResult {
  results: SentimentResult[];
  count: number;
};

/**
 * Interface for aspect sentiment data
 */
export interface AspectSentiment {
  aspect: string;
  sentiment: number;
  count: number;
}

/**
 * Interface for word cloud data
 */
export interface WordCloudItem {
  text: string;
  value: number;
  sentiment: number;
};

/**
 * Interface for sentiment visualization data
 */
export interface SentimentVisualizationData {
  product: {
    id: number;
    name: string;
    [key: string]: unknown;
  };
  sentimentDistribution: {
    name: string;
    score: number;
    count: number;
  }[];
  aspectData: AspectSentiment[];
};

/**
 * Interface for sentiment comparison result
 */
export interface SentimentComparisonResult {
  products: {
    id: number;
    name: string;
    brand: string;
    imageUrl?: string;
    reviewCount: number;
    overallSentiment: number;
    analyzedReviews: number;
    averageSentiment: number;
    aspects: AspectSentiment[];
  }[];
  commonAspects: {
    aspect: string;
    product1: {
      sentiment: number;
      count: number;
    };
    product2: {
      sentiment: number;
      count: number;
    };
    difference: number;
  }[];
};

