import React, { useState, useEffect, use<PERSON>emo, useCallback } from "react";
import { Input } from "../components/ui/input";
import { Button } from "../components/common";
import { Card, CardContent } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { Slider } from "../components/ui/slider";
import { motion, AnimatePresence } from 'framer-motion';
import { FiRefreshCw } from 'react-icons/fi';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import SearchSkeleton from '../components/search/SearchSkeleton';
import SearchError from '../components/search/SearchError';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
} from "../components/ui/sheet";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../components/ui/accordion";
import { Checkbox } from "../components/ui/checkbox";
import { ScrollArea } from "../components/ui/scroll-area";
import {
  Search,
  Star,
  X,
  Filter,
  SlidersHorizontal,
  Grid,
  List,
  ChevronDown,
  ShoppingCart,
  Heart,
  TrendingUp,
  History,
  Loader2,
  Info,
  ArrowUpDown,
  ArrowDown,
  ArrowUp,
  DollarSign,
  Calendar,
  Tag
} from "lucide-react";
import ProductCard from "../components/product/ProductCard";
import AnimatedProductGrid from "../components/product/AnimatedProductGrid";
import {
  PriceRangeSlider,
  TagFilter,
  AnimatedFilterBadge,
  SortingDropdown
} from "../components/ui";

// Sample product data
const products = [
  {
    id: 1,
    name: "Premium Wireless Headphones",
    category: "Audio",
    brand: "SoundMaster",
    price: 249.99,
    rating: 4.8,
    reviews: 1243,
    image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500",
    description: "Experience premium sound quality with active noise cancellation.",
    inStock: true,
    tags: ["wireless", "noise-cancelling", "premium"],
    discount: 10
  },
  {
    id: 2,
    name: "Ultra HD Smart TV 55\"",
    category: "Television",
    brand: "VisionPlus",
    price: 799.99,
    rating: 4.7,
    reviews: 856,
    image: "https://images.unsplash.com/photo-1593784991095-a205069470b6?w=500",
    description: "Crystal clear 4K resolution with smart features and voice control.",
    inStock: true,
    tags: ["4K", "smart", "HDR"],
    discount: 15
  },
  {
    id: 3,
    name: "Professional DSLR Camera",
    category: "Photography",
    brand: "PhotoPro",
    price: 1299.99,
    rating: 4.9,
    reviews: 512,
    image: "https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=500",
    description: "Capture stunning photos with this professional-grade camera.",
    inStock: true,
    tags: ["professional", "high-resolution", "DSLR"],
    discount: 0
  },
  {
    id: 4,
    name: "Ergonomic Office Chair",
    category: "Furniture",
    brand: "ComfortPlus",
    price: 299.99,
    rating: 4.6,
    reviews: 743,
    image: "https://images.unsplash.com/photo-1505843490701-5be5d1b31f8f?w=500",
    description: "Work comfortably with adjustable height and lumbar support.",
    inStock: false,
    tags: ["ergonomic", "office", "adjustable"],
    discount: 5
  },
  {
    id: 5,
    name: "Smart Fitness Tracker",
    category: "Wearables",
    brand: "FitTech",
    price: 129.99,
    rating: 4.5,
    reviews: 1876,
    image: "https://images.unsplash.com/photo-1575311373937-040b8e1fd5b6?w=500",
    description: "Track your fitness goals with heart rate monitoring and GPS.",
    inStock: true,
    tags: ["fitness", "smart", "waterproof"],
    discount: 0
  },
  {
    id: 6,
    name: "Portable Bluetooth Speaker",
    category: "Audio",
    brand: "SoundMaster",
    price: 89.99,
    rating: 4.4,
    reviews: 2134,
    image: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=500",
    description: "Powerful sound in a compact, waterproof design for on-the-go use.",
    inStock: true,
    tags: ["bluetooth", "portable", "waterproof"],
    discount: 20
  },
  {
    id: 7,
    name: "Gaming Laptop 15.6\"",
    category: "Computers",
    brand: "TechPower",
    price: 1499.99,
    rating: 4.7,
    reviews: 763,
    image: "https://images.unsplash.com/photo-1603302576837-37561b2e2302?w=500",
    description: "High-performance gaming laptop with RGB keyboard and advanced cooling.",
    inStock: true,
    tags: ["gaming", "high-performance", "RGB"],
    discount: 8
  },
  {
    id: 8,
    name: "Smartphone with 5G",
    category: "Mobile",
    brand: "TechGiant",
    price: 899.99,
    rating: 4.8,
    reviews: 1521,
    image: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500",
    description: "Next-gen smartphone with 5G connectivity and pro-grade camera system.",
    inStock: true,
    tags: ["5G", "smartphone", "camera"],
    discount: 12
  },
];

// Extract unique categories and brands
const categories = ["All", ...Array.from(new Set(products.map(p => p.category)))];
const brands = ["All", ...Array.from(new Set(products.map(p => p.brand)))];

// Extract all unique tags from products
const allTags = Array.from(new Set(products.flatMap(p => p.tags || [])));

// Enhanced sort options with icons
const sortOptions = [
  { value: "relevance", label: "Relevance", icon: <ArrowUpDown size={16} /> },
  { value: "price-low", label: "Price: Low to High", icon: <ArrowUp size={16} /> },
  { value: "price-high", label: "Price: High to Low", icon: <ArrowDown size={16} /> },
  { value: "rating", label: "Highest Rated", icon: <Star size={16} /> },
  { value: "newest", label: "Newest First", icon: <Calendar size={16} /> },
  { value: "discount", label: "Biggest Discount", icon: <DollarSign size={16} /> },
];

const trendingSearches = ["wireless headphones", "gaming laptop", "smart tv", "fitness tracker"];
const recentSearchesDefault = ["bluetooth speaker", "office chair", "smartphone"];

// Price ranges for quick filtering
const priceRanges = [
  { label: "Under $100", min: 0, max: 100 },
  { label: "$100 - $300", min: 100, max: 300 },
  { label: "$300 - $500", min: 300, max: 500 },
  { label: "$500 - $1000", min: 500, max: 1000 },
  { label: "Over $1000", min: 1000, max: Number.MAX_SAFE_INTEGER },
];

// Star rating component
const StarRating: React.FC<{ rating: number }> = ({ rating }) => {
  return (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          size={16}
          className={`${rating >= star ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
        />
      ))}
    </div>
  );
};

// Price formatter
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(price);
};

export default function SearchPage() {
  // Calculate max price for slider
  const maxPrice = Math.max(...products.map(p => p.price));

  // State management
  const [search, setSearch] = useState("");
  const [category, setCategory] = useState("All");
  const [brand, setBrand] = useState("All");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("relevance");
  const [priceRange, setPriceRange] = useState<[number, number]>([0, maxPrice]);
  const [minRating, setMinRating] = useState(0);
  const [inStockOnly, setInStockOnly] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>(recentSearchesDefault);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // New state for enhanced filters
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [hasDiscount, setHasDiscount] = useState(false);
  const [minSentiment, setMinSentiment] = useState(0);
  const [animationVariant, setAnimationVariant] = useState<'fade' | 'slide' | 'scale' | 'stagger'>('stagger');
  const [showFiltersPanel, setShowFiltersPanel] = useState(true);
  const [quickPriceRange, setQuickPriceRange] = useState<string | null>(null);

  // Filtered products based on all criteria
  const filteredProducts = useMemo(() => {
    return products.filter((product) => {
      // Text search
      const matchesSearch = search === "" ||
        product.name.toLowerCase().includes(search.toLowerCase()) ||
        product.description.toLowerCase().includes(search.toLowerCase()) ||
        product.tags?.some(tag => tag.toLowerCase().includes(search.toLowerCase()));

      // Category filter
      const matchesCategory = category === "All" || product.category === category;

      // Brand filter
      const matchesBrand = brand === "All" || product.brand === brand;

      // Price range filter
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];

      // Rating filter
      const matchesRating = product.rating >= minRating;

      // Stock filter
      const matchesStock = !inStockOnly || product.inStock;

      // New filters

      // Tags filter
      const matchesTags = selectedTags.length === 0 ||
        selectedTags.every(tag => product.tags?.includes(tag));

      // Discount filter
      const matchesDiscount = !hasDiscount || (product.discount && product.discount > 0);

      // Sentiment filter (using mock sentiment score for now)
      const sentimentScore = product.sentimentScore || 0.7; // Default to 0.7 if not provided
      const matchesSentiment = sentimentScore >= minSentiment;

      // Quick price range filter
      let matchesQuickPrice = true;
      if (quickPriceRange) {
        const selectedRange = priceRanges.find(range => range.label === quickPriceRange);
        if (selectedRange) {
          matchesQuickPrice = product.price >= selectedRange.min && product.price <= selectedRange.max;
        }
      }

      return matchesSearch && matchesCategory && matchesBrand && matchesPrice &&
             matchesRating && matchesStock && matchesTags && matchesDiscount &&
             matchesSentiment && matchesQuickPrice;
    }).sort((a, b) => {
      // Enhanced sorting
      switch (sortBy) {
        case "price-low":
          return a.price - b.price;
        case "price-high":
          return b.price - a.price;
        case "rating":
          return b.rating - a.rating;
        case "newest":
          return b.id - a.id; // Using ID as a proxy for newness
        case "discount":
          // Sort by discount percentage (higher first)
          const aDiscount = a.discount || 0;
          const bDiscount = b.discount || 0;
          return bDiscount - aDiscount;
        default:
          // Relevance - prioritize exact matches in name, then tags, then description
          const aNameMatch = a.name.toLowerCase().includes(search.toLowerCase());
          const bNameMatch = b.name.toLowerCase().includes(search.toLowerCase());

          if (aNameMatch && !bNameMatch) return -1;
          if (!aNameMatch && bNameMatch) return 1;

          // If name matches are equal, check tag matches
          if (search) {
            const aTagMatch = a.tags?.some(tag => tag.toLowerCase().includes(search.toLowerCase())) || false;
            const bTagMatch = b.tags?.some(tag => tag.toLowerCase().includes(search.toLowerCase())) || false;

            if (aTagMatch && !bTagMatch) return -1;
            if (!aTagMatch && bTagMatch) return 1;
          }

          // If still tied, use rating as secondary sort
          return b.rating - a.rating;
      }
    });
  }, [
    search,
    category,
    brand,
    priceRange,
    minRating,
    inStockOnly,
    sortBy,
    selectedTags,
    hasDiscount,
    minSentiment,
    quickPriceRange
  ]);

  // Update active filters for display
  useEffect(() => {
    const filters: string[] = [];

    if (category !== "All") filters.push(`Category: ${category}`);
    if (brand !== "All") filters.push(`Brand: ${brand}`);

    // Price filters
    if (quickPriceRange) {
      filters.push(`Price Range: ${quickPriceRange}`);
    } else if (priceRange[0] > 0 || priceRange[1] < maxPrice) {
      filters.push(`Price: ${formatPrice(priceRange[0])} - ${formatPrice(priceRange[1])}`);
    }

    // Rating filter
    if (minRating > 0) filters.push(`Rating: ${minRating}+ stars`);

    // Stock filter
    if (inStockOnly) filters.push("In Stock Only");

    // New filters
    if (hasDiscount) filters.push("On Sale");

    if (minSentiment > 0) {
      const sentimentLabel = minSentiment >= 0.8 ? "Excellent" :
                            minSentiment >= 0.6 ? "Good" :
                            minSentiment >= 0.4 ? "Average" : "Poor";
      filters.push(`Sentiment: ${sentimentLabel}+`);
    }

    // Add selected tags
    selectedTags.forEach(tag => {
      filters.push(`Tag: ${tag}`);
    });

    setActiveFilters(filters);
  }, [
    category,
    brand,
    priceRange,
    minRating,
    inStockOnly,
    maxPrice,
    selectedTags,
    hasDiscount,
    minSentiment,
    quickPriceRange
  ]);

  // Handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    if (search.trim() && !recentSearches.includes(search.trim())) {
      setRecentSearches(prev => [search.trim(), ...prev].slice(0, 5));
    }

    // Simulate search loading
    setIsSearching(true);
    setError(null); // Clear any previous errors

    // Simulate API call with random success/failure
    setTimeout(() => {
      setIsSearching(false);

      // Randomly simulate an error (for demonstration purposes)
      if (Math.random() > 0.9) {
        setError("Failed to fetch search results. Please try again.");
      }
    }, 500);
  };

  // Handle retry when search fails
  const handleRetrySearch = useCallback(() => {
    setError(null);
    setIsSearching(true);

    // Simulate retry
    setTimeout(() => {
      setIsSearching(false);
    }, 500);
  }, []);

  // Reset all filters
  const resetFilters = () => {
    setCategory("All");
    setBrand("All");
    setPriceRange([0, maxPrice]);
    setMinRating(0);
    setInStockOnly(false);
    setSortBy("relevance");

    // Reset new filters
    setSelectedTags([]);
    setHasDiscount(false);
    setMinSentiment(0);
    setQuickPriceRange(null);
  };

  // Remove a specific filter
  const removeFilter = (filter: string) => {
    if (filter.startsWith("Category:")) setCategory("All");
    else if (filter.startsWith("Brand:")) setBrand("All");
    else if (filter.startsWith("Price:")) {
      setPriceRange([0, maxPrice]);
      setQuickPriceRange(null);
    }
    else if (filter.startsWith("Price Range:")) setQuickPriceRange(null);
    else if (filter.startsWith("Rating:")) setMinRating(0);
    else if (filter === "In Stock Only") setInStockOnly(false);
    else if (filter === "On Sale") setHasDiscount(false);
    else if (filter.startsWith("Sentiment:")) setMinSentiment(0);
    else if (filter.startsWith("Tag:")) {
      const tag = filter.replace("Tag: ", "");
      setSelectedTags(prev => prev.filter(t => t !== tag));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Hero Section */}
        <div className="relative mb-8 bg-gradient-primary rounded-2xl overflow-hidden shadow-lg">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 w-64 h-64 rounded-full bg-secondary/30 blur-3xl"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 rounded-full bg-accent/20 blur-3xl"></div>
          </div>
          <div className="relative z-10 px-6 py-8 sm:px-12 sm:py-10 text-white text-center">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-display font-bold mb-4 text-white">
              Discover Perfect Products
            </h1>
            <p className="text-white/80 text-lg max-w-2xl mx-auto mb-8 font-light">
              Find exactly what you're looking for with our advanced product search and sentiment analysis
            </p>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="relative max-w-3xl mx-auto">
              <div className="flex shadow-xl rounded-xl overflow-hidden">
                <div className="relative flex-grow bg-white/10 backdrop-blur-sm">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/70" size={20} />
                  <Input
                    type="text"
                    placeholder="Search for products, brands, or categories..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-12 pr-4 py-4 text-white placeholder:text-white/60 bg-transparent border-0 focus-visible:ring-2 focus-visible:ring-white/30 text-lg"
                  />
                </div>
                <Button
                  type="submit"
                  className="rounded-l-none px-8 py-4 bg-secondary hover:bg-secondary-light text-white font-medium text-base"
                  disabled={isSearching}
                >
                  {isSearching ? <Loader2 className="h-5 w-5 animate-spin mr-2" /> : "Search"}
                </Button>
              </div>
            </form>
          </div>
        </div>

        {/* Search suggestions - Now outside the hero section */}
        {search && (
          <div className="relative z-20 max-w-3xl mx-auto -mt-4 mb-8">
            <div className="bg-white rounded-xl shadow-xl border border-gray-100 p-4 backdrop-blur-sm">
              <div className="mb-4">
                <div className="flex items-center text-sm text-primary mb-3">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  <span className="font-medium">Trending Searches</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {trendingSearches.map((term) => (
                    <Badge
                      key={term}
                      variant="secondary"
                      className="cursor-pointer hover:bg-secondary/10 hover:text-secondary transition-colors py-1.5 px-3"
                      onClick={() => setSearch(term)}
                    >
                      {term}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <div className="flex items-center text-sm text-primary mb-3">
                  <History className="h-4 w-4 mr-2" />
                  <span className="font-medium">Recent Searches</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {recentSearches.map((term) => (
                    <Badge
                      key={term}
                      variant="outline"
                      className="cursor-pointer hover:bg-gray-50 transition-colors py-1.5 px-3"
                      onClick={() => setSearch(term)}
                    >
                      {term}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters with Animated Badges */}
        <AnimatePresence>
          {activeFilters.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
              className="bg-white/90 backdrop-blur-sm rounded-xl shadow-sm border border-gray-100 p-4 mb-8"
            >
              <div className="flex flex-wrap items-center gap-3">
                <span className="text-sm font-medium text-gray-700">Active Filters:</span>
                <motion.div
                  className="flex flex-wrap gap-2"
                  layout
                >
                  {activeFilters.map((filter) => {
                    // Determine icon based on filter type
                    let icon = null;
                    if (filter.startsWith("Category:")) icon = <Tag size={14} />;
                    else if (filter.startsWith("Brand:")) icon = <ShoppingCart size={14} />;
                    else if (filter.startsWith("Price:") || filter.startsWith("Price Range:")) icon = <DollarSign size={14} />;
                    else if (filter.startsWith("Rating:")) icon = <Star size={14} />;
                    else if (filter === "In Stock Only") icon = <Check size={14} />;
                    else if (filter === "On Sale") icon = <DollarSign size={14} />;
                    else if (filter.startsWith("Sentiment:")) icon = <BarChart2 size={14} />;
                    else if (filter.startsWith("Tag:")) icon = <Tag size={14} />;

                    return (
                      <AnimatedFilterBadge
                        key={filter}
                        filter={filter}
                        onRemove={removeFilter}
                        icon={icon}
                      />
                    );
                  })}
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                  className="ml-auto"
                >
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetFilters}
                    className="text-xs border-gray-200 hover:bg-gray-50 hover:text-primary transition-colors"
                  >
                    Clear All Filters
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters - Desktop */}
          <div className="hidden lg:block w-64 flex-shrink-0">
            <motion.div
              className="bg-white rounded-xl shadow-md border border-gray-100 p-4 sticky top-4"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex justify-between items-center mb-3">
                <h2 className="font-display text-sm text-gray-900 font-semibold flex items-center">
                  <Filter size={16} className="mr-2 text-primary" />
                  Filters
                </h2>
                <Button
                  variant="ghost"
                  size="xs"
                  onClick={resetFilters}
                  className="h-6 text-xs hover:text-primary hover:bg-primary/5 transition-colors px-1.5"
                >
                  Reset All
                </Button>
              </div>

              {/* Quick price range filters */}
              <div className="mb-4 pb-4 border-b border-gray-100">
                <h3 className="text-xs font-medium text-gray-700 mb-2">Quick Price Filters</h3>
                <div className="flex flex-wrap gap-2">
                  {priceRanges.map((range) => (
                    <motion.div
                      key={range.label}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Badge
                        variant={quickPriceRange === range.label ? "secondary" : "outline"}
                        className={`px-2 py-1 cursor-pointer transition-colors ${
                          quickPriceRange === range.label
                            ? "bg-primary/10 text-primary hover:bg-primary/20"
                            : "hover:bg-gray-100"
                        }`}
                        onClick={() => {
                          if (quickPriceRange === range.label) {
                            setQuickPriceRange(null);
                          } else {
                            setQuickPriceRange(range.label);
                            setPriceRange([range.min, range.max]);
                          }
                        }}
                      >
                        {range.label}
                      </Badge>
                    </motion.div>
                  ))}
                </div>
              </div>

              <Accordion type="multiple" defaultValue={["category", "price", "rating", "tags", "other"]} className="space-y-1">
                <AccordionItem value="category" className="border-b border-gray-100 pb-1">
                  <AccordionTrigger className="hover:text-primary hover:no-underline py-2 text-gray-800 font-medium text-xs">
                    Category
                  </AccordionTrigger>
                  <AccordionContent className="pt-1 pb-2">
                    <div className="grid grid-cols-2 gap-x-2 gap-y-1.5">
                      {categories.map((cat) => (
                        <div key={cat} className="flex items-center group">
                          <Checkbox
                            id={`category-${cat}`}
                            checked={category === cat}
                            onCheckedChange={() => setCategory(cat)}
                            className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"
                          />
                          <label
                            htmlFor={`category-${cat}`}
                            className="ml-1.5 text-xs text-gray-700 cursor-pointer group-hover:text-primary transition-colors truncate"
                          >
                            {cat}
                          </label>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="brand" className="border-b border-gray-100 pb-1">
                  <AccordionTrigger className="hover:text-primary hover:no-underline py-2 text-gray-800 font-medium text-xs">
                    Brand
                  </AccordionTrigger>
                  <AccordionContent className="pt-1 pb-2">
                    <ScrollArea className="h-28 pr-2">
                      <div className="space-y-1.5 pr-2">
                        {brands.map((b) => (
                          <div key={b} className="flex items-center group">
                            <Checkbox
                              id={`brand-${b}`}
                              checked={brand === b}
                              onCheckedChange={() => setBrand(b)}
                              className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"
                            />
                            <label
                              htmlFor={`brand-${b}`}
                              className="ml-1.5 text-xs text-gray-700 cursor-pointer group-hover:text-primary transition-colors truncate"
                            >
                              {b}
                            </label>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="price" className="border-b border-gray-100 pb-1">
                  <AccordionTrigger className="hover:text-primary hover:no-underline py-2 text-gray-800 font-medium text-xs">
                    Price Range
                  </AccordionTrigger>
                  <AccordionContent className="pt-1 pb-2">
                    <PriceRangeSlider
                      min={0}
                      max={maxPrice}
                      value={priceRange}
                      onChange={(value) => {
                        setPriceRange(value);
                        setQuickPriceRange(null); // Clear quick price range when using slider
                      }}
                      step={10}
                      formatValue={formatPrice}
                      className="mt-2"
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="rating" className="border-b border-gray-100 pb-1">
                  <AccordionTrigger className="hover:text-primary hover:no-underline py-2 text-gray-800 font-medium text-xs">
                    Rating
                  </AccordionTrigger>
                  <AccordionContent className="pt-1 pb-2">
                    <div className="space-y-1.5">
                      {[4, 3, 2, 1].map((rating) => (
                        <div key={rating} className="flex items-center group">
                          <Checkbox
                            id={`rating-${rating}`}
                            checked={minRating === rating}
                            onCheckedChange={() => setMinRating(minRating === rating ? 0 : rating)}
                            className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"
                          />
                          <label
                            htmlFor={`rating-${rating}`}
                            className="ml-1.5 flex items-center cursor-pointer group-hover:text-primary transition-colors"
                          >
                            <StarRating rating={rating} />
                            <span className="text-xs ml-1 text-gray-700">& Up</span>
                          </label>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                {/* New Tags Filter */}
                <AccordionItem value="tags" className="border-b border-gray-100 pb-1">
                  <AccordionTrigger className="hover:text-primary hover:no-underline py-2 text-gray-800 font-medium text-xs">
                    Tags
                  </AccordionTrigger>
                  <AccordionContent className="pt-1 pb-2">
                    <TagFilter
                      tags={allTags}
                      selectedTags={selectedTags}
                      onChange={setSelectedTags}
                      maxDisplay={10}
                    />
                  </AccordionContent>
                </AccordionItem>

                {/* Other Filters */}
                <AccordionItem value="other" className="border-b-0">
                  <AccordionTrigger className="hover:text-primary hover:no-underline py-2 text-gray-800 font-medium text-xs">
                    Other Filters
                  </AccordionTrigger>
                  <AccordionContent className="pt-1 pb-2">
                    <div className="space-y-2">
                      {/* In Stock Filter */}
                      <div className="flex items-center group">
                        <Checkbox
                          id="in-stock"
                          checked={inStockOnly}
                          onCheckedChange={() => setInStockOnly(!inStockOnly)}
                          className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"
                        />
                        <label
                          htmlFor="in-stock"
                          className="ml-1.5 text-xs text-gray-700 cursor-pointer group-hover:text-primary transition-colors"
                        >
                          In Stock Only
                        </label>
                      </div>

                      {/* On Sale Filter */}
                      <div className="flex items-center group">
                        <Checkbox
                          id="on-sale"
                          checked={hasDiscount}
                          onCheckedChange={() => setHasDiscount(!hasDiscount)}
                          className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"
                        />
                        <label
                          htmlFor="on-sale"
                          className="ml-1.5 text-xs text-gray-700 cursor-pointer group-hover:text-primary transition-colors"
                        >
                          On Sale
                        </label>
                      </div>

                      {/* Sentiment Filter */}
                      <div className="mt-3">
                        <div className="flex justify-between items-center mb-1">
                          <label className="text-xs text-gray-700">Minimum Sentiment</label>
                          <span className="text-xs font-medium text-primary">
                            {minSentiment >= 0.8 ? "Excellent" :
                             minSentiment >= 0.6 ? "Good" :
                             minSentiment >= 0.4 ? "Average" :
                             minSentiment >= 0.2 ? "Poor" : "Any"}
                          </span>
                        </div>
                        <Slider
                          value={[minSentiment]}
                          min={0}
                          max={1}
                          step={0.2}
                          onValueChange={(value) => setMinSentiment(value[0])}
                          className="mt-2"
                        />
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <div className="mt-4 pt-3 border-t border-gray-100">
                <Button
                  className="w-full bg-primary hover:bg-primary-dark text-white text-xs py-1.5"
                  onClick={() => setShowMobileFilters(false)}
                >
                  Apply Filters
                </Button>
              </div>
            </motion.div>
          </div>

          {/* Mobile Filters */}
          <div className="lg:hidden flex items-center gap-3 mb-6">
            <Sheet open={showMobileFilters} onOpenChange={setShowMobileFilters}>
              <SheetTrigger asChild>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 border-gray-200 hover:bg-gray-50 hover:text-primary transition-colors"
                  onClick={() => setShowMobileFilters(true)}
                >
                  <Filter size={16} />
                  Filters
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[300px] sm:w-[350px]">
                <SheetHeader className="mb-6">
                  <SheetTitle className="text-xl font-display text-gray-900">Filters</SheetTitle>
                  <SheetDescription className="text-gray-600">
                    Refine your product search
                  </SheetDescription>
                </SheetHeader>
                <div className="py-4">
                  {/* Quick price range filters */}
                  <div className="mb-4 pb-4 border-b border-gray-100">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Quick Price Filters</h3>
                    <div className="flex flex-wrap gap-2">
                      {priceRanges.map((range) => (
                        <motion.div
                          key={range.label}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Badge
                            variant={quickPriceRange === range.label ? "secondary" : "outline"}
                            className={`px-2 py-1 cursor-pointer transition-colors ${
                              quickPriceRange === range.label
                                ? "bg-primary/10 text-primary hover:bg-primary/20"
                                : "hover:bg-gray-100"
                            }`}
                            onClick={() => {
                              if (quickPriceRange === range.label) {
                                setQuickPriceRange(null);
                              } else {
                                setQuickPriceRange(range.label);
                                setPriceRange([range.min, range.max]);
                              }
                            }}
                          >
                            {range.label}
                          </Badge>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  <Accordion type="multiple" defaultValue={["category", "brand", "price", "rating", "tags", "other"]} className="space-y-2">
                    <AccordionItem value="category" className="border-b border-gray-100 pb-1">
                      <AccordionTrigger className="hover:text-primary hover:no-underline py-3 text-gray-800 font-medium">
                        Category
                      </AccordionTrigger>
                      <AccordionContent className="pt-2 pb-3">
                        <div className="space-y-2.5">
                          {categories.map((cat) => (
                            <div key={cat} className="flex items-center group">
                              <Checkbox
                                id={`mobile-category-${cat}`}
                                checked={category === cat}
                                onCheckedChange={() => setCategory(cat)}
                                className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                              />
                              <label
                                htmlFor={`mobile-category-${cat}`}
                                className="ml-2.5 text-sm text-gray-700 cursor-pointer group-hover:text-primary transition-colors"
                              >
                                {cat}
                              </label>
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="brand" className="border-b border-gray-100 pb-1">
                      <AccordionTrigger className="hover:text-primary hover:no-underline py-3 text-gray-800 font-medium">
                        Brand
                      </AccordionTrigger>
                      <AccordionContent className="pt-2 pb-3">
                        <ScrollArea className="h-44">
                          <div className="space-y-2.5 pr-4">
                            {brands.map((b) => (
                              <div key={b} className="flex items-center group">
                                <Checkbox
                                  id={`mobile-brand-${b}`}
                                  checked={brand === b}
                                  onCheckedChange={() => setBrand(b)}
                                  className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                                />
                                <label
                                  htmlFor={`mobile-brand-${b}`}
                                  className="ml-2.5 text-sm text-gray-700 cursor-pointer group-hover:text-primary transition-colors"
                                >
                                  {b}
                                </label>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="price" className="border-b border-gray-100 pb-1">
                      <AccordionTrigger className="hover:text-primary hover:no-underline py-3 text-gray-800 font-medium">
                        Price Range
                      </AccordionTrigger>
                      <AccordionContent className="pt-2 pb-3">
                        <PriceRangeSlider
                          min={0}
                          max={maxPrice}
                          value={priceRange}
                          onChange={(value) => {
                            setPriceRange(value);
                            setQuickPriceRange(null); // Clear quick price range when using slider
                          }}
                          step={10}
                          formatValue={formatPrice}
                          className="mt-2"
                        />
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="rating" className="border-b border-gray-100 pb-1">
                      <AccordionTrigger className="hover:text-primary hover:no-underline py-3 text-gray-800 font-medium">
                        Rating
                      </AccordionTrigger>
                      <AccordionContent className="pt-2 pb-3">
                        <div className="space-y-3">
                          {[4, 3, 2, 1].map((rating) => (
                            <div key={rating} className="flex items-center group">
                              <Checkbox
                                id={`mobile-rating-${rating}`}
                                checked={minRating === rating}
                                onCheckedChange={() => setMinRating(minRating === rating ? 0 : rating)}
                                className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                              />
                              <label
                                htmlFor={`mobile-rating-${rating}`}
                                className="ml-2.5 flex items-center cursor-pointer group-hover:text-primary transition-colors"
                              >
                                <StarRating rating={rating} />
                                <span className="text-sm ml-2 text-gray-700">& Up</span>
                              </label>
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Tags Filter */}
                    <AccordionItem value="tags" className="border-b border-gray-100 pb-1">
                      <AccordionTrigger className="hover:text-primary hover:no-underline py-3 text-gray-800 font-medium">
                        Tags
                      </AccordionTrigger>
                      <AccordionContent className="pt-2 pb-3">
                        <TagFilter
                          tags={allTags}
                          selectedTags={selectedTags}
                          onChange={setSelectedTags}
                          maxDisplay={8}
                        />
                      </AccordionContent>
                    </AccordionItem>

                    {/* Other Filters */}
                    <AccordionItem value="other" className="border-b-0">
                      <AccordionTrigger className="hover:text-primary hover:no-underline py-3 text-gray-800 font-medium">
                        Other Filters
                      </AccordionTrigger>
                      <AccordionContent className="pt-2 pb-3">
                        <div className="space-y-3">
                          {/* In Stock Filter */}
                          <div className="flex items-center group">
                            <Checkbox
                              id="mobile-in-stock"
                              checked={inStockOnly}
                              onCheckedChange={() => setInStockOnly(!inStockOnly)}
                              className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                            />
                            <label
                              htmlFor="mobile-in-stock"
                              className="ml-2.5 text-sm text-gray-700 cursor-pointer group-hover:text-primary transition-colors"
                            >
                              In Stock Only
                            </label>
                          </div>

                          {/* On Sale Filter */}
                          <div className="flex items-center group">
                            <Checkbox
                              id="mobile-on-sale"
                              checked={hasDiscount}
                              onCheckedChange={() => setHasDiscount(!hasDiscount)}
                              className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                            />
                            <label
                              htmlFor="mobile-on-sale"
                              className="ml-2.5 text-sm text-gray-700 cursor-pointer group-hover:text-primary transition-colors"
                            >
                              On Sale
                            </label>
                          </div>

                          {/* Sentiment Filter */}
                          <div className="mt-4">
                            <div className="flex justify-between items-center mb-2">
                              <label className="text-sm text-gray-700">Minimum Sentiment</label>
                              <span className="text-sm font-medium text-primary">
                                {minSentiment >= 0.8 ? "Excellent" :
                                 minSentiment >= 0.6 ? "Good" :
                                 minSentiment >= 0.4 ? "Average" :
                                 minSentiment >= 0.2 ? "Poor" : "Any"}
                              </span>
                            </div>
                            <Slider
                              value={[minSentiment]}
                              min={0}
                              max={1}
                              step={0.2}
                              onValueChange={(value) => setMinSentiment(value[0])}
                              className="mt-2"
                            />
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
                <SheetFooter className="flex-col gap-3 sm:flex-col mt-6">
                  <Button
                    onClick={() => setShowMobileFilters(false)}
                    className="w-full bg-primary hover:bg-primary-dark text-white"
                  >
                    Apply Filters
                  </Button>
                  <Button
                    variant="outline"
                    onClick={resetFilters}
                    className="w-full border-gray-200"
                  >
                    Reset All
                  </Button>
                </SheetFooter>
              </SheetContent>
            </Sheet>

            <SortingDropdown
              options={sortOptions}
              value={sortBy}
              onChange={setSortBy}
              buttonClassName="w-full sm:w-[180px]"
            />

            <div className="flex items-center gap-1 ml-auto">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode("grid")}
                className={`rounded-full ${viewMode === "grid" ? "bg-primary/10 text-primary" : "text-gray-500"}`}
              >
                <Grid size={18} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode("list")}
                className={`rounded-full ${viewMode === "list" ? "bg-primary/10 text-primary" : "text-gray-500"}`}
              >
                <List size={18} />
              </Button>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-grow">
            {/* Enhanced Results Header */}
            <motion.div
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 mb-6"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <motion.p
                    className="text-sm text-gray-600"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    Showing <span className="font-semibold text-gray-900">{filteredProducts.length}</span> results
                    {search && <span> for "<span className="font-semibold text-primary">{search}</span>"</span>}
                  </motion.p>
                </div>
                <div className="hidden lg:flex items-center gap-4">
                  {/* Enhanced Sorting Dropdown */}
                  <div className="flex-grow-0">
                    <SortingDropdown
                      options={sortOptions}
                      value={sortBy}
                      onChange={setSortBy}
                      buttonClassName="w-[200px]"
                    />
                  </div>

                  {/* Animation Style Selector */}
                  <div className="flex-grow-0">
                    <Select
                      value={animationVariant}
                      onValueChange={(value) => setAnimationVariant(value as 'fade' | 'slide' | 'scale' | 'stagger')}
                    >
                      <SelectTrigger className="w-[140px] border-gray-200">
                        <SelectValue placeholder="Animation" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fade">Fade</SelectItem>
                        <SelectItem value="slide">Slide</SelectItem>
                        <SelectItem value="scale">Scale</SelectItem>
                        <SelectItem value="stagger">Stagger</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* View Mode Toggles */}
                  <div className="flex items-center gap-1">
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setViewMode("grid")}
                        className={`rounded-full ${viewMode === "grid" ? "bg-primary/10 text-primary" : "text-gray-500"}`}
                      >
                        <Grid size={18} />
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setViewMode("list")}
                        className={`rounded-full ${viewMode === "list" ? "bg-primary/10 text-primary" : "text-gray-500"}`}
                      >
                        <List size={18} />
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Product Grid/List with Loading and Error States */}
            <AnimatePresence mode="wait">
              {/* Loading State */}
              {isSearching ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <SearchSkeleton count={viewMode === "grid" ? 8 : 4} />
                </motion.div>
              ) : error ? (
                // Error State
                <motion.div
                  key="error"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <SearchError
                    message={error}
                    onRetry={handleRetrySearch}
                  />
                </motion.div>
              ) : filteredProducts.length > 0 ? (
                // Enhanced Results State with Animated Product Grid
                <motion.div
                  key="results"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <AnimatedProductGrid
                    products={filteredProducts.map(product => ({
                      ...product,
                      originalPrice: product.discount > 0 ? product.price : undefined,
                      sentimentScore: product.sentimentScore || 0.7, // Use existing or mock sentiment score
                      reviewCount: product.reviews || 0 // Map reviews to reviewCount
                    }))}
                    viewMode={viewMode}
                    onAddToCompare={(productId) => console.log('Add to compare', productId)}
                    onAddToFavorite={(productId) => console.log('Add to favorite', productId)}
                    animationVariant={animationVariant}
                  />
                </motion.div>
              ) : (
                // Enhanced Empty State with Better Animations
                <motion.div
                  key="empty"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3, type: "spring", stiffness: 300, damping: 25 }}
                  className="text-center py-16 px-8 bg-white rounded-xl border border-gray-100 shadow-sm relative overflow-hidden"
                >
                  {/* Background decoration */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-10 left-10 w-64 h-64 rounded-full bg-primary blur-3xl"></div>
                    <div className="absolute bottom-10 right-10 w-64 h-64 rounded-full bg-secondary blur-3xl"></div>
                  </div>

                  <motion.div
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
                    className="relative z-10"
                  >
                    <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gray-100 mb-6 shadow-inner">
                      <motion.div
                        initial={{ rotate: -10 }}
                        animate={{ rotate: 10 }}
                        transition={{ repeat: Infinity, repeatType: "reverse", duration: 2, ease: "easeInOut" }}
                      >
                        <Info size={32} className="text-gray-400" />
                      </motion.div>
                    </div>

                    <motion.h2
                      className="text-2xl font-display font-bold mb-3 text-gray-900"
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.3, duration: 0.5 }}
                    >
                      No products found
                    </motion.h2>

                    <motion.p
                      className="text-gray-600 max-w-md mx-auto mb-8"
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.5 }}
                    >
                      We couldn't find any products matching your criteria. Try adjusting your filters or searching for something else.
                    </motion.p>

                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.5 }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outline"
                        onClick={resetFilters}
                        className="border-gray-200 hover:bg-primary hover:text-white transition-colors"
                      >
                        Clear All Filters
                      </Button>
                    </motion.div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}

