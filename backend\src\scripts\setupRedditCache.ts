import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import logger from '../utils/logger';

// Load environment variables
dotenv.config();

// Database connection parameters
const dbParams = {
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'productwhisper',
  password: process.env.DB_PASSWORD || 'postgres',
  port: parseInt(process.env.DB_PORT || '5432'),
};

/**
 * Setup Reddit cache tables
 */
async function setupRedditCache() {
  // Connect to our app database
  const appPool = new Pool(dbParams);

  try {
    logger.info('Setting up Reddit cache tables...');

    // Read schema file
    const schemaPath = path.join(__dirname, '../../migrations/002_reddit_cache.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');

    // Execute the SQL script
    await appPool.query(schema);
    
    logger.info('Reddit cache tables created successfully');
    
    // Check if tables were created
    const checkResult = await appPool.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'reddit_cache_requests'
      )`
    );
    
    if (checkResult.rows[0].exists) {
      logger.info('Verified reddit_cache_requests table exists');
    } else {
      logger.error('Failed to create reddit_cache_requests table');
    }
    
    // Create scheduled cleanup function
    await appPool.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM pg_proc WHERE proname = 'scheduled_reddit_cache_cleanup'
        ) THEN
          CREATE OR REPLACE FUNCTION scheduled_reddit_cache_cleanup() RETURNS void AS $$
          BEGIN
            PERFORM cleanup_expired_reddit_cache();
          END;
          $$ LANGUAGE plpgsql;
        END IF;
      END $$;
    `);
    
    logger.info('Scheduled cleanup function created');
    
    // Success message
    logger.info('Reddit cache setup completed successfully');
  } catch (error) {
    logger.error('Error setting up Reddit cache:', error);
    throw error;
  } finally {
    await appPool.end();
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupRedditCache()
    .then(() => {
      logger.info('Reddit cache setup completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Reddit cache setup failed:', error);
      process.exit(1);
    });
}

export default setupRedditCache;
