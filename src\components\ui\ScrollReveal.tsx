import React, { useRef, useEffect, useState } from 'react';
import { motion, useAnimation } from 'framer-motion';
import type { VariantLabels, TargetAndTransition } from 'framer-motion';

interface ScrollRevealProps {
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  delay?: number;
  duration?: number;
  threshold?: number;
  className?: string;
  once?: boolean;
  distance?: number;
  scale?: number;
  opacity?: [number, number];
  staggerChildren?: number;
  staggerDirection?: 'forward' | 'reverse';
  customVariants?: {
    hidden: TargetAndTransition;
    visible: TargetAndTransition;
  };
}

const ScrollReveal: React.FC<ScrollRevealProps> = ({
  children,
  direction = 'up',
  delay = 0,
  duration = 0.5,
  threshold = 0.1,
  className = '',
  once = true,
  distance = 50,
  scale = 1,
  opacity = [0, 1],
  staggerChildren = 0,
  staggerDirection = 'forward',
  customVariants,
}) => {
  const controls = useAnimation();
  const ref = useRef<HTMLDivElement>(null);
  const [isInView, setIsInView] = useState(false);

  // Define animation variants based on direction
  const getVariants = () => {
    if (customVariants) return customVariants;

    const variants = {
      hidden: {
        opacity: opacity[0],
        scale: scale === 1 ? 1 : scale * 0.95,
      },
      visible: {
        opacity: opacity[1],
        scale: scale,
        transition: {
          duration,
          delay,
          ease: 'easeOut',
          when: 'beforeChildren',
          staggerChildren: staggerChildren,
          staggerDirection: staggerDirection === 'forward' ? 1 : -1,
        },
      },
    };

    // Add transform based on direction
    if (direction === 'up') {
      variants.hidden.y = distance;
      variants.visible.y = 0;
    } else if (direction === 'down') {
      variants.hidden.y = -distance;
      variants.visible.y = 0;
    } else if (direction === 'left') {
      variants.hidden.x = distance;
      variants.visible.x = 0;
    } else if (direction === 'right') {
      variants.hidden.x = -distance;
      variants.visible.x = 0;
    }

    return variants;
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          controls.start('visible');
          if (once) {
            observer.disconnect();
          }
        } else if (!once) {
          setIsInView(false);
          controls.start('hidden');
        }
      },
      {
        threshold,
        rootMargin: '0px',
      }
    );

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [controls, once, threshold]);

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={getVariants()}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default ScrollReveal;
