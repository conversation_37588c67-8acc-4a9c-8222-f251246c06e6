import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiMessageSquare,
  FiX,
  FiSend,
  FiUser,
  FiRefreshCw,
  FiMinimize2,
  FiHelpCircle,
  FiChevronDown,
  FiSearch,
  FiBarChart2
} from 'react-icons/fi';
import { useChat } from '../../contexts/ChatContext';
import type { ChatMessage } from '../../contexts/ChatContext';
import faqData from '../../constants/faqData';
import ChatAnalytics from './ChatAnalytics';

interface ChatBoxProps {
  position?: 'bottom-right' | 'bottom-left';
  primaryColor?: string;
  secondaryColor?: string;
}

const ChatBox: React.FC<ChatBoxProps> = ({
  position = 'bottom-right',
  primaryColor = 'primary',
  secondaryColor = 'secondary',
}) => {
  const {
    messages,
    isOpen,
    isTyping,
    toggleChat,
    closeChat,
    sendMessage,
    clearMessages
  } = useChat();

  const [input, setInput] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Suggested questions based on FAQ data
  const suggestedQuestions = useCallback(() => {
    // Get 3 random questions from FAQ data
    const randomQuestions = [...faqData]
      .sort(() => 0.5 - Math.random())
      .slice(0, 3)
      .map(faq => faq.question);

    return randomQuestions;
  }, []);

  // Memoized suggestions
  const [suggestions] = useState(suggestedQuestions);

  // Scroll to bottom of messages when new messages are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Focus input when chat is opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }

    // Show suggestions when chat is opened or cleared
    if (isOpen && messages.length <= 1) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  }, [isOpen, messages.length]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim()) {
      sendMessage(input);
      setInput('');
      setShowSuggestions(false);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (question: string) => {
    sendMessage(question);
    setShowSuggestions(false);
  };

  // Position classes
  const positionClasses = {
    'bottom-right': 'right-6',
    'bottom-left': 'left-6',
  };

  // Message component
  const MessageBubble: React.FC<{ message: ChatMessage }> = ({ message }) => {
    // Safety check for invalid message
    if (!message || typeof message !== 'object') {
      return null;
    }

    // Default values if properties are missing
    const role = message.role || 'assistant';
    const content = message.content || 'No message content';
    const timestamp = message.timestamp instanceof Date ? message.timestamp : new Date();

    const isUser = role === 'user';

    return (
      <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
        <div className={`flex items-start max-w-[80%] ${isUser ? 'flex-row-reverse' : ''}`}>
          <div className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${
            isUser ? 'bg-primary text-white ml-2' : 'bg-gray-200 text-gray-700 mr-2'
          }`}>
            {isUser ? <FiUser size={14} /> : <img src="/ProductWhisper Logo Design.png" alt="Assistant" className="h-6 w-6 rounded-full" />}
          </div>

          <div className={`py-2 px-4 rounded-lg ${
            isUser
              ? 'bg-primary text-white'
              : 'bg-white border border-gray-200 text-gray-800'
          }`}>
            <p className="text-sm whitespace-pre-wrap">{content}</p>
            <span className="text-xs opacity-70 mt-1 block">
              {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Chat Button */}
      <motion.button
        className={`fixed bottom-6 ${positionClasses[position]} z-40 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-dark transition-colors`}
        onClick={toggleChat}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        aria-label={isOpen ? "Close chat" : "Open chat"}
      >
        {isOpen ? <FiMinimize2 size={24} /> : <FiMessageSquare size={24} />}
      </motion.button>

      {/* Analytics panel */}
      <AnimatePresence>
        {showAnalytics && isOpen && (
          <ChatAnalytics onClose={() => setShowAnalytics(false)} />
        )}
      </AnimatePresence>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className={`fixed bottom-24 ${positionClasses[position]} z-40 w-72 sm:w-80 bg-white rounded-xl shadow-2xl overflow-hidden flex flex-col`}
            style={{ height: '450px', maxHeight: 'calc(100vh - 180px)' }}
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            transition={{ type: 'spring', damping: 25 }}
          >
            {/* Header */}
            <div className="p-3 bg-primary text-white flex justify-between items-center">
              <div className="flex items-center">
                <img
                  src="/ProductWhisper Logo Design.png"
                  alt="ProductWhisper"
                  className="h-8 w-8 rounded-full mr-3 object-cover shadow-md border border-white/20"
                />
                <div>
                  <h3 className="font-semibold">Whisperer</h3>
                  {/* <p className="text-xs opacity-80">Ask anything about products</p> */}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowAnalytics(prev => !prev)}
                  className="text-white/80 hover:text-white p-1 rounded-full transition-colors"
                  aria-label="Chat analytics"
                >
                  <FiBarChart2 size={16} />
                </button>
                <button
                  onClick={clearMessages}
                  className="text-white/80 hover:text-white p-1 rounded-full transition-colors"
                  aria-label="Reset conversation"
                >
                  <FiRefreshCw size={16} />
                </button>
                <button
                  onClick={closeChat}
                  className="text-white/80 hover:text-white p-1 rounded-full transition-colors"
                  aria-label="Close chat"
                >
                  <FiX size={18} />
                </button>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 p-3 overflow-y-auto bg-gray-50">
              {messages && messages.length > 0 ? (
                // Map through messages if they exist
                messages.map((message) => (
                  message && message.id ? (
                    <MessageBubble key={message.id} message={message} />
                  ) : null
                ))
              ) : (
                // Show a message if no messages exist
                <div className="text-center text-gray-500 py-4">
                  No messages yet. Start a conversation!
                </div>
              )}

              {/* Typing indicator */}
              {isTyping && (
                <div className="flex justify-start mb-4">
                  <div className="flex items-start max-w-[80%]">
                    <div className="flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center bg-gray-200 text-gray-700 mr-2">
                      <img src="/ProductWhisper Logo Design.png" alt="Assistant" className="h-6 w-6 rounded-full" />
                    </div>
                    <div className="py-3 px-4 rounded-lg bg-white border border-gray-200">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Suggested Questions */}
              {showSuggestions && messages.length <= 1 && !isTyping && (
                <div className="mt-2 mb-1">
                  <div className="text-xs text-gray-500 mb-2 flex items-center">
                    <FiHelpCircle className="mr-1" size={12} />
                    <span>Suggested questions:</span>
                  </div>
                  <div className="space-y-1">
                    {suggestions.map((question, index) => (
                      <motion.button
                        key={index}
                        className="w-full text-left py-1.5 px-2 rounded-lg border border-gray-200 bg-white hover:bg-gray-50 text-sm text-gray-700 transition-colors"
                        onClick={() => handleSuggestionClick(question)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-start">
                          <FiSearch className="mt-0.5 mr-2 text-primary flex-shrink-0" size={14} />
                          <span className="line-clamp-2">{question}</span>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>
              )}

              {/* Scroll anchor */}
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <form onSubmit={handleSubmit} className="p-3 border-t border-gray-200 bg-white">
              <div className="flex items-center">
                <input
                  ref={inputRef}
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1 py-2 px-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/30"
                  disabled={isTyping}
                  onFocus={() => setShowSuggestions(messages.length <= 1)}
                />
                <motion.button
                  type="submit"
                  className="bg-primary text-white p-2 rounded-r-lg hover:bg-primary-dark transition-colors"
                  disabled={!input.trim() || isTyping}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FiSend size={18} />
                </motion.button>
              </div>

              {/* Quick actions */}
              <div className="flex items-center justify-between mt-2">
                <button
                  type="button"
                  onClick={() => setShowSuggestions(!showSuggestions)}
                  className="text-xs text-primary hover:text-primary-dark transition-colors flex items-center"
                >
                  <FiHelpCircle size={12} className="mr-1" />
                  {showSuggestions ? 'Hide suggestions' : 'Show suggestions'}
                </button>

                {/* <div className="text-xs text-gray-500 text-center flex-1">
                  Ask about products, features, or sentiment analysis
                </div> */}

                <button
                  type="button"
                  onClick={clearMessages}
                  className="text-xs text-primary hover:text-primary-dark transition-colors flex items-center"
                >
                  <FiRefreshCw size={12} className="mr-1" />
                  Reset chat
                </button>
              </div>
            </form>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ChatBox;

