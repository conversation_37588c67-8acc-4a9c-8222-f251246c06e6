/**
 * Product interface
 */
export interface Product {
  id: number;
  name: string;
  brand: string;
  category: string;
  description: string;
  price: number;
  originalPrice?: number;
  imageUrl: string;
  rating: number;
  reviewCount: number;
  sentimentScore?: number;
  features?: Record<string, { value: string; score: number }>;
  pros?: string[];
  cons?: string[];
}

/**
 * Review interface
 */
export interface Review {
  id: number;
  userName: string;
  rating: number;
  title: string;
  content: string;
  date: Date;
  helpfulCount: number;
  sentimentScore?: number;
  source: string;
}

/**
 * Search filters interface
 */
export interface SearchFilters {
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  minRating?: number;
}

/**
 * Search result interface
 */
export interface SearchResult {
  products: Product[];
  total: number;
  page: number;
  pageSize: number;
  filters: {
    categories: { name: string; count: number }[];
    brands: { name: string; count: number }[];
    priceRange: [number, number];
  };
}

/**
 * Trending search interface
 */
export interface TrendingSearch {
  query: string;
  count: number;
}

/**
 * Trend data interface
 */
export interface TrendData {
  trend_direction: 'up' | 'down' | 'stable';
  change_percentage: number;
  data_points: {
    date: string;
    value: number;
    count?: number;
  }[];
}

/**
 * Aspect trend data interface
 */
export interface AspectTrendData {
  aspects: {
    aspect: string;
    sentiment: number;
    count: number;
  }[];
}

/**
 * Product comparison interface
 */
export interface ProductComparison {
  products: {
    id: number;
    name: string;
    brand: string;
    price: number;
    rating: number;
    reviewCount: number;
    sentimentScore: number;
    imageUrl: string;
  }[];
  features: {
    name: string;
    values: {
      productId: number;
      value: string;
      score: number;
    }[];
  }[];
}

/**
 * Sentiment comparison interface
 */
export interface SentimentComparison {
  products: {
    id: number;
    name: string;
    brand: string;
    imageUrl?: string;
    reviewCount: number;
    overallSentiment: number;
    analyzedReviews: number;
    averageSentiment: number;
    aspects: {
      aspect: string;
      sentiment: number;
      count: number;
    }[];
  }[];
  commonAspects: {
    aspect: string;
    product1: {
      sentiment: number;
      count: number;
    };
    product2: {
      sentiment: number;
      count: number;
    };
    difference: number;
  }[];
}

/**
 * Chat message interface
 */
export interface ChatMessage {
  id: string;
  sessionId: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: Date;
}

/**
 * Chat response interface
 */
export interface ChatResponse {
  messageId: string;
  text: string;
  sessionId: string;
}

/**
 * Reddit post interface
 */
export interface RedditPost {
  source: string;
  sourceId: string;
  productName: string;
  content: string;
  url: string;
  createdAt: Date;
  score: number;
  numComments: number;
  author: string;
  subreddit: string;
}
