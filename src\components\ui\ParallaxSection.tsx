import React, { useRef, useState, useEffect } from 'react';
import { motion, useScroll, useMotionValue, useSpring, useTransform } from 'framer-motion';

interface ParallaxSectionProps {
  children: React.ReactNode;
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  className?: string;
  offset?: [number, number];
  easing?: (value: number) => number;
}

// Easing functions
const easings = {
  linear: (x: number) => x,
  easeIn: (x: number) => x * x,
  easeOut: (x: number) => 1 - Math.pow(1 - x, 2),
  easeInOut: (x: number) => x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2,
};

const ParallaxSection: React.FC<ParallaxSectionProps> = ({
  children,
  speed = 0.5,
  direction = 'up',
  className = '',
  offset = [-300, 300],
  easing = easings.easeOut,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start'],
  });

  // Create motion values for x and y
  const xMotion = useMotionValue(0);
  const yMotion = useMotionValue(0);

  // Add spring for smoother motion
  const xSpring = useSpring(xMotion, { stiffness: 100, damping: 30 });
  const ySpring = useSpring(yMotion, { stiffness: 100, damping: 30 });

  // Update motion values based on scroll
  useEffect(() => {
    const unsubscribe = scrollYProgress.on("change", (latest) => {
      // Apply easing
      const easedValue = easing(latest);

      // Calculate output value based on direction and offset
      const outputRange = direction === 'down' || direction === 'right'
        ? [offset[0], offset[1]]
        : [offset[1], offset[0]];

      // Linear interpolation between output range values
      const outputValue = outputRange[0] + (outputRange[1] - outputRange[0]) * easedValue;

      // Apply speed factor
      const scaledValue = outputValue * speed;

      // Update the appropriate motion value based on direction
      if (direction === 'left' || direction === 'right') {
        xMotion.set(scaledValue);
      } else if (direction === 'up' || direction === 'down') {
        yMotion.set(scaledValue);
      }
    });

    return () => unsubscribe();
  }, [scrollYProgress, direction, offset, speed, easing, xMotion, yMotion]);

  return (
    <div ref={ref} className={`relative overflow-hidden ${className}`}>
      <motion.div
        style={{
          x: direction === 'left' || direction === 'right' ? xSpring : 0,
          y: direction === 'up' || direction === 'down' ? ySpring : 0
        }}
        className="w-full h-full"
      >
        {children}
      </motion.div>
    </div>
  );
};

export default ParallaxSection;
