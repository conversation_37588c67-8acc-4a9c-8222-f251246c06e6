import React, { useState, useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import SentimentAnalyzer from '../components/SentimentAnalyzer';
import SentimentComparison from '../components/SentimentComparison';
import SentimentVisualizer from '../components/SentimentVisualizer';
import SentimentWordCloud from '../components/SentimentWordCloud';
import { sentimentService } from '../services/sentimentService';
import { apiService } from '../services/api';
import {type Product } from '../types/api';
import type { SentimentResult, SentimentVisualizationData, WordCloudItem, AspectSentiment } from '../types/sentiment';
import {
  AnimatedCircles,
  ScrollFadeIn,
  GradientButton
} from '../components/ui';
import {
  BarChart2,
  MessageSquare,
  ArrowLeftRight,
  PieChart,
  RefreshCw,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';

/**
 * Sentiment Analysis Page
 * Demonstrates sentiment analysis capabilities
 */
const SentimentAnalysisPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'analyzer' | 'comparison' | 'reviews' | 'visualizations'>('analyzer');
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [reviewResults, setReviewResults] = useState<SentimentResult[]>([]);
  const [visualizationData, setVisualizationData] = useState<SentimentVisualizationData | null>(null);
  const [wordCloudData, setWordCloudData] = useState<WordCloudItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch trending products on mount
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const trendingProducts = await apiService.getTrendingProducts(5);
        setProducts(trendingProducts);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      }
    };

    fetchProducts();
  }, []);

  // Analyze product reviews
  const analyzeProductReviews = async (productId: number) => {
    setLoading(true);
    setError(null);
    setReviewResults([]);

    try {
      // Find the selected product
      const product = products.find(p => p.id === productId);
      if (product) {
        setSelectedProduct(product);
      }

      // Analyze reviews
      const results = await sentimentService.analyzeProductReviews(productId);
      setReviewResults(results);

      // Prepare visualization data
      prepareVisualizationData(product, results);
    } catch (err) {
      console.error('Error analyzing product reviews:', err);
      setError('Failed to analyze product reviews. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Prepare data for visualizations
  const prepareVisualizationData = (product: Product | null | undefined, results: SentimentResult[]) => {
    if (!product || !results || !results.length) {
      // Set default visualization data
      setVisualizationData({
        product: product || {
          id: 0,
          name: 'Unknown Product',
          brand: '',
          category: '',
          description: '',
          price: 0,
          rating: 0,
          reviewCount: 0,
          sentimentScore: 0,
          features: {},
          pros: [],
          cons: []
        },
        sentimentDistribution: [
          { name: 'Neutral', score: 0, count: 1 }
        ],
        aspectData: []
      });
      setWordCloudData([]);
      return;
    }

    // Prepare sentiment distribution data for bar chart
    const sentimentDistribution = [
      { name: 'Very Positive', score: 0, count: 0 },
      { name: 'Positive', score: 0, count: 0 },
      { name: 'Neutral', score: 0, count: 0 },
      { name: 'Negative', score: 0, count: 0 },
      { name: 'Very Negative', score: 0, count: 0 }
    ];

    // Prepare aspect data for radar chart
    const aspectData: AspectSentiment[] = [];
    const aspectMap = new Map<string, { total: number, count: number }>();

    // Prepare word cloud data
    const wordMap = new Map<string, { value: number, sentiment: number }>();

    // Process each review result
    results.forEach(result => {
      // Update sentiment distribution
      const score = result.score;
      if (score >= 0.6) {
        sentimentDistribution[0].count++;
        sentimentDistribution[0].score += score;
      } else if (score >= 0.2) {
        sentimentDistribution[1].count++;
        sentimentDistribution[1].score += score;
      } else if (score >= -0.2) {
        sentimentDistribution[2].count++;
        sentimentDistribution[2].score += score;
      } else if (score >= -0.6) {
        sentimentDistribution[3].count++;
        sentimentDistribution[3].score += score;
      } else {
        sentimentDistribution[4].count++;
        sentimentDistribution[4].score += score;
      }

      // Process aspects
      if (result.aspects) {
        result.aspects.forEach(aspect => {
          // Update aspect map
          if (!aspectMap.has(aspect.aspect)) {
            aspectMap.set(aspect.aspect, { total: 0, count: 0 });
          }
          const aspectData = aspectMap.get(aspect.aspect)!;
          aspectData.total += aspect.sentiment;
          aspectData.count++;

          // Add words to word cloud
          const words = aspect.aspect.split(' ');
          words.forEach(word => {
            if (word.length < 3) return; // Skip short words

            const lowerWord = word.toLowerCase();
            if (!wordMap.has(lowerWord)) {
              wordMap.set(lowerWord, { value: 0, sentiment: 0 });
            }
            const wordData = wordMap.get(lowerWord)!;
            wordData.value++;
            wordData.sentiment = (wordData.sentiment * (wordData.value - 1) + aspect.sentiment) / wordData.value;
          });

          // Add adjectives to word cloud
          if (aspect.adjectives) {
            aspect.adjectives.forEach(adj => {
              if (adj.length < 3) return; // Skip short words

              const lowerAdj = adj.toLowerCase();
              if (!wordMap.has(lowerAdj)) {
                wordMap.set(lowerAdj, { value: 0, sentiment: 0 });
              }
              const wordData = wordMap.get(lowerAdj)!;
              wordData.value++;
              wordData.sentiment = (wordData.sentiment * (wordData.value - 1) + aspect.sentiment) / wordData.value;
            });
          }
        });
      }
    });

    // Calculate average scores for sentiment distribution
    sentimentDistribution.forEach(item => {
      if (item.count > 0) {
        item.score = item.score / item.count;
      }
    });

    // Convert aspect map to array
    aspectMap.forEach((data, aspect) => {
      aspectData.push({
        aspect,
        sentiment: data.total / data.count,
        count: data.count
      });
    });

    // Sort aspects by count (descending)
    aspectData.sort((a, b) => b.count - a.count);

    // Convert word map to array for word cloud
    const wordCloudData: WordCloudItem[] = Array.from(wordMap.entries()).map(([text, data]) => ({
      text,
      value: data.value,
      sentiment: data.sentiment
    }));

    // Set visualization data
    setVisualizationData({
      product,
      sentimentDistribution,
      aspectData: aspectData.slice(0, 10) // Top 10 aspects
    });

    // Set word cloud data
    setWordCloudData(wordCloudData);
  };

  // Get sentiment color based on score
  const getSentimentColor = (score: number): string => {
    if (score >= 0.6) return 'text-green-600';
    if (score >= 0.2) return 'text-green-400';
    if (score >= -0.2) return 'text-gray-500';
    if (score >= -0.6) return 'text-red-400';
    return 'text-red-600';
  };

  // Handle tab change
  const handleTabChange = (tab: 'analyzer' | 'comparison' | 'reviews' | 'visualizations') => {
    setActiveTab(tab);
  };

  // References for animations
  const heroRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Check if sections are in view
  const heroInView = useInView(heroRef, { once: false, amount: 0.3 });
  const contentInView = useInView(contentRef, { once: false, amount: 0.3 });

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section with Premium Gradient */}
      <div
        ref={heroRef}
        className="relative bg-gradient-primary rounded-b-premium shadow-premium overflow-hidden mb-6"
      >
        {/* Animated background circles */}
        <AnimatedCircles
          variant="secondary"
          count={8}
          maxOpacity={0.25}
          animationStyle="mixed"
          blurAmount={1.5}
          speed="slow"
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
          <ScrollFadeIn
            direction="up"
            className="text-center"
            threshold={0.2}
            duration={0.7}
            distance={30}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex items-center justify-center mb-3">
                <motion.div
                  className="flex items-center justify-center h-14 w-14 rounded-full bg-white/20 text-white mb-3 relative z-10"
                  whileHover={{ scale: 1.05 }}
                >
                  <BarChart2 className="h-7 w-7" />
                </motion.div>
              </div>
              <h1 className="text-3xl sm:text-4xl font-bold text-white font-display mb-2">Sentiment Analysis</h1>
              <p className="mt-2 text-lg text-white/80 leading-relaxed max-w-2xl mx-auto">
                Analyze text sentiment, compare products, and visualize review sentiments with our advanced AI
              </p>
            </motion.div>
          </ScrollFadeIn>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
        <div className="bg-white rounded-premium shadow-sm p-1 flex flex-wrap">
          <button
            className={`flex items-center py-2 px-4 rounded-md font-medium text-sm transition-all duration-200 ${
              activeTab === 'analyzer'
                ? 'bg-primary text-white shadow-sm'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
            onClick={() => handleTabChange('analyzer')}
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            Text Analyzer
          </button>
          <button
            className={`flex items-center py-2 px-4 rounded-md font-medium text-sm transition-all duration-200 ${
              activeTab === 'reviews'
                ? 'bg-primary text-white shadow-sm'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
            onClick={() => handleTabChange('reviews')}
          >
            <ThumbsUp className="w-4 h-4 mr-2" />
            Review Analysis
          </button>
          <button
            className={`flex items-center py-2 px-4 rounded-md font-medium text-sm transition-all duration-200 ${
              activeTab === 'comparison'
                ? 'bg-primary text-white shadow-sm'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
            onClick={() => handleTabChange('comparison')}
          >
            <ArrowLeftRight className="w-4 h-4 mr-2" />
            Product Comparison
          </button>
          <button
            className={`flex items-center py-2 px-4 rounded-md font-medium text-sm transition-all duration-200 ${
              activeTab === 'visualizations'
                ? 'bg-primary text-white shadow-sm'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
            onClick={() => handleTabChange('visualizations')}
          >
            <PieChart className="w-4 h-4 mr-2" />
            Visualizations
          </button>
        </div>
      </div>

      {/* Content Section */}
      <div ref={contentRef} className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-10">

        {/* Text Analyzer Tab */}
        {activeTab === 'analyzer' && (
          <ScrollFadeIn
            direction="up"
            threshold={0.1}
            duration={0.5}
            distance={30}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={contentInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5 }}
              className="max-w-3xl mx-auto"
            >
              <SentimentAnalyzer />
            </motion.div>
          </ScrollFadeIn>
        )}

        {/* Product Comparison Tab */}
        {activeTab === 'comparison' && (
          <ScrollFadeIn
            direction="up"
            threshold={0.1}
            duration={0.5}
            distance={30}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={contentInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5 }}
            >
              <SentimentComparison />
            </motion.div>
          </ScrollFadeIn>
        )}

        {/* Visualizations Tab */}
        {activeTab === 'visualizations' && (
          <ScrollFadeIn
            direction="up"
            threshold={0.1}
            duration={0.5}
            distance={30}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={contentInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5 }}
            >
              {!visualizationData ? (
                <div className="bg-white rounded-premium shadow-premium p-8 text-center">
                  <div className="w-16 h-16 mx-auto mb-3 bg-gray-50 rounded-full flex items-center justify-center">
                    <PieChart className="w-8 h-8 text-gray-300" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-700 font-display mb-3">No Visualization Data Available</h3>
                  <p className="text-gray-500 mb-6 max-w-md mx-auto">
                    Please analyze a product's reviews first to generate visualization data.
                  </p>
                  <GradientButton
                    variant="primary"
                    onClick={() => handleTabChange('reviews')}
                    className="py-2 px-4"
                    leftIcon={<ThumbsUp className="w-4 h-4 mr-1" />}
                  >
                    Go to Review Analysis
                  </GradientButton>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="bg-white rounded-premium shadow-premium p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-xl font-bold text-gray-900 font-display">
                        Sentiment Visualizations: {visualizationData?.product?.name || 'Product'}
                      </h2>
                      <GradientButton
                        variant="secondary"
                        size="sm"
                        onClick={() => handleTabChange('reviews')}
                        className="flex items-center bg-white/10 text-primary hover:bg-gray-50"
                        leftIcon={<RefreshCw className="w-4 h-4" />}
                      >
                        Analyze Another
                      </GradientButton>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                      {/* Sentiment Distribution */}
                      <div className="bg-white rounded-premium shadow-sm border border-gray-100 p-4">
                        <h3 className="text-lg font-medium mb-3 flex items-center">
                          <BarChart2 className="w-5 h-5 text-primary mr-2" />
                          Sentiment Distribution
                        </h3>
                        <SentimentVisualizer
                          data={visualizationData?.sentimentDistribution || []}
                          type="bar"
                          className="bg-transparent shadow-none"
                        />
                      </div>

                      {/* Sentiment Pie Chart */}
                      <div className="bg-white rounded-premium shadow-sm border border-gray-100 p-4">
                        <h3 className="text-lg font-medium mb-3 flex items-center">
                          <PieChart className="w-5 h-5 text-primary mr-2" />
                          Sentiment Breakdown
                        </h3>
                        <SentimentVisualizer
                          data={reviewResults || []}
                          type="pie"
                          className="bg-transparent shadow-none"
                        />
                      </div>
                    </div>

                    {/* Aspect Radar Chart */}
                    {visualizationData?.aspectData && visualizationData.aspectData.length > 0 && (
                      <div className="mb-6 bg-white rounded-premium shadow-sm border border-gray-100 p-4">
                        <h3 className="text-lg font-medium mb-3 flex items-center">
                          <ArrowLeftRight className="w-5 h-5 text-primary mr-2" />
                          Aspect Sentiment Analysis
                        </h3>
                        <SentimentVisualizer
                          data={[]}
                          aspectData={visualizationData.aspectData}
                          type="radar"
                          className="bg-transparent shadow-none"
                        />
                      </div>
                    )}

                    {/* Word Cloud */}
                    {wordCloudData && wordCloudData.length > 0 && (
                      <div className="bg-white rounded-premium shadow-sm border border-gray-100 p-4">
                        <h3 className="text-lg font-medium mb-3 flex items-center">
                          <MessageSquare className="w-5 h-5 text-primary mr-2" />
                          Sentiment Word Cloud
                        </h3>
                        <SentimentWordCloud words={wordCloudData} maxWords={100} />
                      </div>
                    )}
                  </div>
                </div>
              )}
            </motion.div>
          </ScrollFadeIn>
        )}

        {/* Review Analysis Tab */}
        {activeTab === 'reviews' && (
          <ScrollFadeIn
            direction="up"
            threshold={0.1}
            duration={0.5}
            distance={30}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={contentInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            >
              {/* Left column: Product Selection */}
              <div>
                <div className="bg-white rounded-premium shadow-premium p-6">
                  <div className="flex items-center mb-4">
                    <ThumbsUp className="w-5 h-5 text-primary mr-2" />
                    <h2 className="text-xl font-bold text-gray-900 font-display">Analyze Product Reviews</h2>
                  </div>

                  <div className="mb-4">
                    <label className="block text-gray-700 mb-3 font-medium">Select a product to analyze:</label>
                    <div className="grid grid-cols-1 gap-3">
                      {products.map(product => (
                        <motion.div
                          key={product.id}
                          whileHover={{ y: -2, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
                          className={`border rounded-premium p-3 cursor-pointer transition-all duration-200 hover:border-primary/50 ${
                            selectedProduct?.id === product.id
                              ? 'border-primary bg-primary/5 shadow-md'
                              : 'border-gray-200'
                          }`}
                          onClick={() => analyzeProductReviews(product.id)}
                        >
                          <div className="flex items-center">
                            {product.imageUrl ? (
                              <img
                                src={product.imageUrl}
                                alt={product.name}
                                className="w-16 h-16 object-cover rounded-premium mr-4"
                              />
                            ) : (
                              <div className="w-16 h-16 bg-gray-100 rounded-premium flex items-center justify-center mr-4">
                                <ThumbsUp className="w-8 h-8 text-gray-300" />
                              </div>
                            )}
                            <div>
                              <div className="font-medium text-gray-900">{product.name}</div>
                              <div className="text-sm text-gray-600">{product.brand}</div>
                              <div className="flex items-center mt-1">
                                <div className="flex text-yellow-500 mr-1">
                                  {[...Array(5)].map((_, i) => (
                                    <span key={i} className="text-sm">
                                      {i < Math.round(product.rating) ? '★' : '☆'}
                                    </span>
                                  ))}
                                </div>
                                <span className="text-gray-600 text-sm">{product.reviewCount} reviews</span>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {loading && (
                    <div className="text-center py-6 bg-gray-50 rounded-premium">
                      <div className="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
                      <div className="mt-3 text-gray-600 font-medium">Analyzing reviews...</div>
                    </div>
                  )}

                  {error && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-premium mb-4">
                      {error}
                    </div>
                  )}
                </div>
              </div>

              {/* Right column: Analysis Results */}
              <div>
                {selectedProduct && reviewResults.length > 0 && (
                  <div className="bg-white rounded-premium shadow-premium p-6">
                    <div className="flex items-center mb-4">
                      <BarChart2 className="w-5 h-5 text-primary mr-2" />
                      <h2 className="text-xl font-bold text-gray-900 font-display">
                        Review Analysis: {selectedProduct.name}
                      </h2>
                    </div>

                    <div className="mb-6">
                      <h3 className="text-lg font-medium mb-3 flex items-center">
                        <ThumbsUp className="w-4 h-4 text-primary mr-2" />
                        Overall Sentiment
                      </h3>
                      <div className="bg-gray-50 p-4 rounded-premium border border-gray-100">
                        <div className="flex justify-between items-center mb-3">
                          <span className="font-medium">Average Score:</span>
                          <span className={`font-bold text-lg ${getSentimentColor(selectedProduct.sentimentScore)}`}>
                            {selectedProduct.sentimentScore.toFixed(2)}
                          </span>
                        </div>

                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${Math.abs(selectedProduct.sentimentScore) * 100}%` }}
                            transition={{ duration: 1, ease: "easeOut" }}
                            className={`h-3 rounded-full ${selectedProduct.sentimentScore >= 0 ? 'bg-green-500' : 'bg-red-500'}`}
                          ></motion.div>
                        </div>
                      </div>
                    </div>

                    <h3 className="text-lg font-medium mb-3 flex items-center">
                      <MessageSquare className="w-4 h-4 text-primary mr-2" />
                      Review Sentiments
                    </h3>
                    <div className="space-y-4">
                      {selectedProduct.reviews?.map((review, index) => (
                        <motion.div
                          key={index}
                          className="border border-gray-200 rounded-premium p-4 hover:border-primary/30 hover:shadow-sm transition-all duration-200"
                          whileHover={{ y: -2 }}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <div className="font-medium">{review.user}</div>
                              <div className="text-sm text-gray-600">{review.date}</div>
                            </div>
                            <div className={`font-bold px-2 py-1 rounded-full ${getSentimentColor(reviewResults[index]?.score || 0)} bg-opacity-10`}>
                              {reviewResults[index]?.score.toFixed(2)}
                            </div>
                          </div>

                          <div className="flex items-center mb-2">
                            <div className="flex text-yellow-500 mr-2">
                              {[...Array(5)].map((_, i) => (
                                <span key={i}>{i < review.rating ? '★' : '☆'}</span>
                              ))}
                            </div>
                            <div className="font-medium">{review.title}</div>
                          </div>

                          <p className="text-gray-700">{review.comment}</p>

                          {reviewResults[index]?.aspects && reviewResults[index]?.aspects.length > 0 && (
                            <div className="mt-3 pt-3 border-t border-gray-100">
                              <div className="text-sm font-medium mb-2">Aspects:</div>
                              <div className="flex flex-wrap gap-2">
                                {reviewResults[index].aspects.map((aspect, i) => (
                                  <span
                                    key={i}
                                    className={`text-xs px-2.5 py-1 rounded-full ${getSentimentColor(aspect.sentiment)} bg-opacity-10 border border-opacity-30`}
                                  >
                                    {aspect.aspect}: {aspect.sentiment.toFixed(2)}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </ScrollFadeIn>
        )}
      </div>
    </div>
  );
};

export default SentimentAnalysisPage;



