import React, { createContext, useContext, useState, ReactNode } from 'react';
import { apiService } from '../services/api';

// Product type
interface Product {
  id: number;
  name: string;
  description: string | null;
  category: string | null;
  image_url: string | null;
  brand: string | null;
  price: number | null;
  average_rating: number | null;
  scores: {
    overall: number;
    reddit: number;
    amazon: number;
    youtube: number;
    confidence: number;
    sample_size: number;
  };
  sources: string[];
  tags: string[];
}

// Search filters type
interface SearchFilters {
  minScore?: number;
  sources?: string[];
  minConfidence?: number;
  sortBy?: 'score' | 'confidence' | 'mentions';
  category?: string;
  brand?: string;
  tags?: string[];
  priceMin?: number;
  priceMax?: number;
}

// Recent search type
interface RecentSearch {
  id: number;
  query: string;
  created_at: string;
  results_count: number;
}

// Search context type
interface SearchContextType {
  searchResults: Product[];
  recentSearches: RecentSearch[];
  popularSearches: { query: string; count: number }[];
  favorites: any[];
  loading: boolean;
  error: string | null;
  searchQuery: string;
  filters: SearchFilters;
  search: (query: string, filters?: SearchFilters) => Promise<void>;
  updateFilters: (newFilters: SearchFilters) => void;
  clearFilters: () => void;
  loadRecentSearches: () => Promise<void>;
  loadPopularSearches: () => Promise<void>;
  loadFavorites: () => Promise<void>;
  addFavorite: (productId: number) => Promise<void>;
  removeFavorite: (productId: number) => Promise<void>;
  isFavorite: (productId: number) => boolean;
}

// Create context
const SearchContext = createContext<SearchContextType | undefined>(undefined);

// Search provider props
interface SearchProviderProps {
  children: ReactNode;
}

// Default filters
const defaultFilters: SearchFilters = {
  minScore: 0,
  sortBy: 'score',
};

// Search provider component
export const SearchProvider: React.FC<SearchProviderProps> = ({ children }) => {
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);
  const [popularSearches, setPopularSearches] = useState<{ query: string; count: number }[]>([]);
  const [favorites, setFavorites] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filters, setFilters] = useState<SearchFilters>(defaultFilters);

  // Search function with improved error handling and retry logic
  const search = async (query: string, newFilters?: SearchFilters) => {
    if (!query || query.trim() === '') {
      setError('Please enter a search term');
      return;
    }

    setLoading(true);
    setError(null);
    setSearchQuery(query);

    const searchFilters = newFilters || filters;
    let retryCount = 0;
    const maxRetries = 2;

    const performSearch = async (): Promise<void> => {
      try {
        const data = await apiService.searchProducts(query, searchFilters);

        // Validate response data
        if (!data || !data.results) {
          throw new Error('Invalid response data');
        }

        setSearchResults(data.results);

        // Update filters if new ones were provided
        if (newFilters) {
          setFilters(newFilters);
        }

        // Add to recent searches (this would normally be handled by the backend)
        // This is a client-side simulation for development
        const recentSearch = {
          id: Date.now(),
          query,
          created_at: new Date().toISOString(),
          results_count: data.results.length
        };

        setRecentSearches(prev => {
          // Avoid duplicates by removing any existing search with the same query
          const filtered = prev.filter(s => s.query.toLowerCase() !== query.toLowerCase());
          // Add new search at the beginning and limit to 10 items
          return [recentSearch, ...filtered].slice(0, 10);
        });

      } catch (err: any) {
        console.error('Search error:', err);

        // Retry logic for network errors
        if (retryCount < maxRetries && (err.name === 'NetworkError' || err.message?.includes('network'))) {
          retryCount++;
          console.log(`Retrying search (${retryCount}/${maxRetries})...`);
          await performSearch();
          return;
        }

        // Handle different error types
        if (err.status === 429) {
          setError('Too many requests. Please try again later.');
        } else if (err.status === 404) {
          setError('No results found for your search.');
          setSearchResults([]);
        } else if (err.status >= 500) {
          setError('Server error. Please try again later.');
        } else {
          setError(err.message || 'Search failed. Please try again.');
        }

        setSearchResults([]);
      } finally {
        setLoading(false);
      }
    };

    await performSearch();
  };

  // Update filters
  const updateFilters = (newFilters: SearchFilters) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);

    // If there's an active search query, re-run the search
    if (searchQuery) {
      search(searchQuery, updatedFilters);
    }
  };

  // Clear filters
  const clearFilters = () => {
    setFilters(defaultFilters);

    // If there's an active search query, re-run the search
    if (searchQuery) {
      search(searchQuery, defaultFilters);
    }
  };

  // Load recent searches with improved error handling and caching
  const loadRecentSearches = async () => {
    // Check if we have cached recent searches in localStorage
    try {
      const cachedData = localStorage.getItem('pw_recent_searches');
      if (cachedData) {
        const { data, timestamp } = JSON.parse(cachedData);
        const cacheAge = Date.now() - timestamp;

        // Use cache if it's less than 1 hour old
        if (cacheAge < 60 * 60 * 1000 && Array.isArray(data) && data.length > 0) {
          setRecentSearches(data);
          return;
        }
      }

      // Fetch from API if cache is invalid or expired
      const data = await apiService.getRecentSearches();

      if (Array.isArray(data)) {
        setRecentSearches(data);

        // Cache the results
        localStorage.setItem('pw_recent_searches', JSON.stringify({
          data,
          timestamp: Date.now()
        }));
      }
    } catch (err) {
      console.error('Failed to load recent searches:', err);

      // Try to use cached data even if it's old in case of error
      try {
        const cachedData = localStorage.getItem('pw_recent_searches');
        if (cachedData) {
          const { data } = JSON.parse(cachedData);
          if (Array.isArray(data)) {
            setRecentSearches(data);
          }
        }
      } catch (cacheErr) {
        console.error('Failed to load cached recent searches:', cacheErr);
      }
    }
  };

  // Load popular searches with improved error handling and caching
  const loadPopularSearches = async () => {
    // Check if we have cached popular searches in localStorage
    try {
      const cachedData = localStorage.getItem('pw_popular_searches');
      if (cachedData) {
        const { data, timestamp } = JSON.parse(cachedData);
        const cacheAge = Date.now() - timestamp;

        // Use cache if it's less than 6 hours old
        if (cacheAge < 6 * 60 * 60 * 1000 && Array.isArray(data) && data.length > 0) {
          setPopularSearches(data);
          return;
        }
      }

      // Fetch from API if cache is invalid or expired
      const data = await apiService.getPopularSearches();

      // Transform string array to object array with count
      const formattedData = Array.isArray(data)
        ? data.map((query, index) => ({
            query,
            count: 100 - (index * 10) // Mock count for display purposes
          }))
        : [];

      setPopularSearches(formattedData);

      // Cache the results
      localStorage.setItem('pw_popular_searches', JSON.stringify({
        data: formattedData,
        timestamp: Date.now()
      }));
    } catch (err) {
      console.error('Failed to load popular searches:', err);

      // Try to use cached data even if it's old in case of error
      try {
        const cachedData = localStorage.getItem('pw_popular_searches');
        if (cachedData) {
          const { data } = JSON.parse(cachedData);
          if (Array.isArray(data)) {
            setPopularSearches(data);
          }
        }
      } catch (cacheErr) {
        console.error('Failed to load cached popular searches:', cacheErr);
      }
    }
  };

  // Load favorites
  const loadFavorites = async () => {
    try {
      const data = await apiService.getFavorites();
      setFavorites(data);
    } catch (err) {
      console.error('Failed to load favorites:', err);
    }
  };

  // Add favorite
  const addFavorite = async (productId: number) => {
    try {
      await apiService.addFavorite(productId);
      await loadFavorites();
    } catch (err) {
      console.error('Failed to add favorite:', err);
    }
  };

  // Remove favorite
  const removeFavorite = async (productId: number) => {
    try {
      await apiService.removeFavorite(productId);
      await loadFavorites();
    } catch (err) {
      console.error('Failed to remove favorite:', err);
    }
  };

  // Check if product is in favorites
  const isFavorite = (productId: number): boolean => {
    return favorites.some(fav => fav.product_id === productId);
  };

  // Context value
  const value = {
    searchResults,
    recentSearches,
    popularSearches,
    favorites,
    loading,
    error,
    searchQuery,
    filters,
    search,
    updateFilters,
    clearFilters,
    loadRecentSearches,
    loadPopularSearches,
    loadFavorites,
    addFavorite,
    removeFavorite,
    isFavorite
  };

  return <SearchContext.Provider value={value}>{children}</SearchContext.Provider>;
};

// Custom hook to use search context
export const useSearch = (): SearchContextType => {
  const context = useContext(SearchContext);

  if (context === undefined) {
    throw new Error('useSearch must be used within a SearchProvider');
  }

  return context;
};
