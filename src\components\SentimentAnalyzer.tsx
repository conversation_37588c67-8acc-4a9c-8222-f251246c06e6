import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { sentimentService } from '../services/sentimentService';
import { type SentimentResult } from '../types/sentiment';
import { GradientButton } from '../components/ui';
import { MessageSquare, ThumbsUp, ThumbsDown, Trash2, BarChart2, Tag } from 'lucide-react';

interface SentimentAnalyzerProps {
  initialText?: string;
  onAnalysisComplete?: (result: SentimentResult) => void;
  className?: string;
}

/**
 * Component for analyzing sentiment of text
 */
const SentimentAnalyzer: React.FC<SentimentAnalyzerProps> = ({
  initialText = '',
  onAnalysisComplete,
  className = '',
}) => {
  const [text, setText] = useState(initialText);
  const [result, setResult] = useState<SentimentResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Handle text change
   */
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  };

  /**
   * Analyze sentiment
   */
  const analyzeSentiment = async () => {
    if (!text.trim()) {
      setError('Please enter some text to analyze');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const sentimentResult = await sentimentService.analyzeSentiment(text);
      setResult(sentimentResult);

      if (onAnalysisComplete) {
        onAnalysisComplete(sentimentResult);
      }
    } catch (err) {
      setError('Failed to analyze sentiment. Please try again.');
      console.error('Sentiment analysis error:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get sentiment color based on score
   */
  const getSentimentColor = (score: number): string => {
    if (score >= 0.6) return 'text-green-600';
    if (score >= 0.2) return 'text-green-400';
    if (score >= -0.2) return 'text-gray-500';
    if (score >= -0.6) return 'text-red-400';
    return 'text-red-600';
  };

  /**
   * Get sentiment label based on score
   */
  const getSentimentLabel = (score: number): string => {
    if (score >= 0.6) return 'Very Positive';
    if (score >= 0.2) return 'Positive';
    if (score >= -0.2) return 'Neutral';
    if (score >= -0.6) return 'Negative';
    return 'Very Negative';
  };

  return (
    <div className={`bg-white rounded-premium shadow-premium p-6 ${className}`}>
      <div className="flex items-center mb-4">
        <MessageSquare className="w-5 h-5 text-primary mr-2" />
        <h2 className="text-xl font-bold text-gray-900 font-display">Sentiment Analyzer</h2>
      </div>

      <div className="mb-5">
        <textarea
          className="w-full p-4 border border-gray-200 rounded-premium focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all duration-200 shadow-sm"
          rows={5}
          placeholder="Enter text to analyze sentiment..."
          value={text}
          onChange={handleTextChange}
        />
      </div>

      <div className="flex justify-between items-center mb-5">
        <GradientButton
          variant="primary"
          onClick={analyzeSentiment}
          disabled={loading || !text.trim()}
          className="py-2.5 px-5"
          leftIcon={<BarChart2 className="w-4 h-4 mr-1" />}
        >
          {loading ? 'Analyzing...' : 'Analyze Sentiment'}
        </GradientButton>

        <GradientButton
          variant="secondary"
          onClick={() => setText('')}
          disabled={loading || !text.trim()}
          className="py-2.5 px-4 bg-white/10 text-gray-600 hover:bg-gray-50"
          leftIcon={<Trash2 className="w-4 h-4 mr-1" />}
        >
          Clear
        </GradientButton>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-premium mb-5">
          {error}
        </div>
      )}

      {result && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mt-6 border-t border-gray-100 pt-5"
        >
          <h3 className="text-lg font-bold text-gray-900 font-display mb-4 flex items-center">
            <BarChart2 className="w-5 h-5 text-primary mr-2" />
            Analysis Result
          </h3>

          <div className="bg-gray-50 p-4 rounded-premium border border-gray-100 mb-5">
            <div className="flex items-center mb-3">
              <div className="text-gray-700 font-medium mr-2">
                Sentiment:
              </div>
              <div className={`text-lg font-bold ${getSentimentColor(result.score)}`}>
                {getSentimentLabel(result.score)} ({result.score.toFixed(2)})
              </div>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${Math.abs(result.score) * 100}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
                className={`h-3 rounded-full ${result.score >= 0 ? 'bg-green-500' : 'bg-red-500'}`}
              ></motion.div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="bg-green-50 p-3 rounded-premium border border-green-100"
              >
                <div className="flex items-center mb-1">
                  <ThumbsUp className="w-4 h-4 text-green-500 mr-1" />
                  <div className="text-sm text-green-700">Positive</div>
                </div>
                <div className="text-lg font-semibold text-green-700">{(result.details.pos * 100).toFixed(1)}%</div>
              </motion.div>

              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="bg-gray-50 p-3 rounded-premium border border-gray-200"
              >
                <div className="flex items-center mb-1">
                  <div className="w-4 h-4 rounded-full border-2 border-gray-400 mr-1"></div>
                  <div className="text-sm text-gray-600">Neutral</div>
                </div>
                <div className="text-lg font-semibold text-gray-700">{(result.details.neu * 100).toFixed(1)}%</div>
              </motion.div>

              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.3 }}
                className="bg-red-50 p-3 rounded-premium border border-red-100"
              >
                <div className="flex items-center mb-1">
                  <ThumbsDown className="w-4 h-4 text-red-500 mr-1" />
                  <div className="text-sm text-red-700">Negative</div>
                </div>
                <div className="text-lg font-semibold text-red-700">{(result.details.neg * 100).toFixed(1)}%</div>
              </motion.div>
            </div>
          </div>

          {result.entities && result.entities.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.3 }}
              className="mb-5"
            >
              <h4 className="font-medium mb-3 flex items-center">
                <Tag className="w-4 h-4 text-primary mr-2" />
                Entities
              </h4>
              <div className="grid grid-cols-2 gap-3">
                {result.entities.map((entity, index) => (
                  <motion.div
                    key={index}
                    className="bg-white p-3 rounded-premium border border-gray-200 hover:border-primary/30 hover:shadow-sm transition-all duration-200"
                    whileHover={{ y: -2 }}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                  >
                    <div className="font-medium">{entity.name}</div>
                    <div className="text-sm text-gray-600">{entity.type}</div>
                    <div className={`text-sm font-medium ${getSentimentColor(entity.sentiment)}`}>
                      {entity.sentiment.toFixed(2)}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {result.aspects && result.aspects.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.4 }}
            >
              <h4 className="font-medium mb-3 flex items-center">
                <MessageSquare className="w-4 h-4 text-primary mr-2" />
                Aspects
              </h4>
              <div className="space-y-3">
                {result.aspects.map((aspect, index) => (
                  <motion.div
                    key={index}
                    className="bg-white p-3 rounded-premium border border-gray-200 hover:border-primary/30 hover:shadow-sm transition-all duration-200"
                    whileHover={{ y: -2 }}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                  >
                    <div className="font-medium">{aspect.aspect}</div>
                    <div className={`text-sm font-medium ${getSentimentColor(aspect.sentiment)}`}>
                      {getSentimentLabel(aspect.sentiment)} ({aspect.sentiment.toFixed(2)})
                    </div>
                    <div className="text-sm text-gray-600 mt-1 bg-gray-50 p-2 rounded-md border border-gray-100">
                      "{aspect.sentence}"
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default SentimentAnalyzer;
