.contact-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.contact-hero {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 3rem 2rem;
  margin: 2rem 0;
  text-align: center;
}

.contact-hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.contact-hero p {
  font-size: 1.2rem;
  color: #666;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin: 3rem 0;
}

.contact-info h2,
.contact-form-container h2 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.contact-info p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 2rem;
}

.contact-details {
  margin-top: 2rem;
}

.contact-item {
  margin-bottom: 1.5rem;
}

.contact-item h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.contact-item p {
  margin: 0;
  color: #666;
}

.contact-form {
  background-color: #fff;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group textarea {
  resize: vertical;
}

.submit-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
}

.submit-button:hover {
  background-color: #0056b3;
}

@media (max-width: 768px) {
  .contact-hero {
    padding: 2rem 1rem;
  }
  
  .contact-hero h1 {
    font-size: 2rem;
  }
  
  .contact-content {
    grid-template-columns: 1fr;
  }
}
