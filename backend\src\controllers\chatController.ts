import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { ApiError } from '../middleware/errorHandler';
import * as chatService from '../services/chatService';
import logger from '../utils/logger';

/**
 * Send a message to the chat
 */
export const sendMessage = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { message, sessionId } = req.body;
    
    if (!message || typeof message !== 'string') {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Message is required');
    }
    
    const response = await chatService.processMessage(message, sessionId);
    
    res.status(StatusCodes.OK).json({
      success: true,
      messageId: response.messageId,
      response: response.text,
      sessionId: response.sessionId,
    });
  } catch (error) {
    logger.error('Error in sendMessage controller:', error);
    next(error);
  }
};

/**
 * Send feedback for a chat message
 */
export const sendFeedback = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { messageId, helpful } = req.body;
    
    if (!messageId || typeof messageId !== 'string') {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Message ID is required');
    }
    
    if (typeof helpful !== 'boolean') {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Helpful must be a boolean value');
    }
    
    await chatService.saveFeedback(messageId, helpful);
    
    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Feedback received',
    });
  } catch (error) {
    logger.error('Error in sendFeedback controller:', error);
    next(error);
  }
};
