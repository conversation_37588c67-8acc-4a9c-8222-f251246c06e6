import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { ApiError } from '../middleware/errorHandler';
import * as trendService from '../services/trendService';
import logger from '../utils/logger';
import { getCache, setCache } from '../config/redis';

// Valid period values
const validPeriods = ['day', 'week', 'month', 'quarter', 'year'];

/**
 * Get all trend data for a product
 */
export const getAllTrends = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const productId = parseInt(req.params.productId);
    
    if (isNaN(productId)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Invalid product ID');
    }
    
    const period = (req.query.period as string) || 'month';
    
    if (!validPeriods.includes(period)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, `Invalid period. Must be one of: ${validPeriods.join(', ')}`);
    }
    
    // Try to get from cache first
    const cacheKey = `trends:all:${productId}:${period}`;
    const cachedData = await getCache(cacheKey);
    
    if (cachedData) {
      return res.status(StatusCodes.OK).json(JSON.parse(cachedData));
    }
    
    // Get all trend data in parallel
    const [sentimentTrend, mentionTrend, aspectTrend] = await Promise.all([
      trendService.getSentimentTrend(productId, period),
      trendService.getMentionTrend(productId, period),
      trendService.getAspectTrend(productId, period),
    ]);
    
    const result = {
      success: true,
      productId,
      period,
      sentiment: sentimentTrend,
      mentions: mentionTrend,
      aspects: aspectTrend,
    };
    
    // Cache the result
    await setCache(cacheKey, JSON.stringify(result), 3600); // Cache for 1 hour
    
    res.status(StatusCodes.OK).json(result);
  } catch (error) {
    logger.error(`Error in getAllTrends controller for product ID ${req.params.productId}:`, error);
    next(error);
  }
};

/**
 * Get sentiment trend data for a product
 */
export const getSentimentTrend = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const productId = parseInt(req.params.productId);
    
    if (isNaN(productId)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Invalid product ID');
    }
    
    const period = (req.query.period as string) || 'month';
    
    if (!validPeriods.includes(period)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, `Invalid period. Must be one of: ${validPeriods.join(', ')}`);
    }
    
    // Try to get from cache first
    const cacheKey = `trends:sentiment:${productId}:${period}`;
    const cachedData = await getCache(cacheKey);
    
    if (cachedData) {
      return res.status(StatusCodes.OK).json(JSON.parse(cachedData));
    }
    
    const sentimentTrend = await trendService.getSentimentTrend(productId, period);
    
    const result = {
      success: true,
      productId,
      period,
      data: sentimentTrend,
    };
    
    // Cache the result
    await setCache(cacheKey, JSON.stringify(result), 3600); // Cache for 1 hour
    
    res.status(StatusCodes.OK).json(result);
  } catch (error) {
    logger.error(`Error in getSentimentTrend controller for product ID ${req.params.productId}:`, error);
    next(error);
  }
};

/**
 * Get mention trend data for a product
 */
export const getMentionTrend = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const productId = parseInt(req.params.productId);
    
    if (isNaN(productId)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Invalid product ID');
    }
    
    const period = (req.query.period as string) || 'month';
    
    if (!validPeriods.includes(period)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, `Invalid period. Must be one of: ${validPeriods.join(', ')}`);
    }
    
    // Try to get from cache first
    const cacheKey = `trends:mentions:${productId}:${period}`;
    const cachedData = await getCache(cacheKey);
    
    if (cachedData) {
      return res.status(StatusCodes.OK).json(JSON.parse(cachedData));
    }
    
    const mentionTrend = await trendService.getMentionTrend(productId, period);
    
    const result = {
      success: true,
      productId,
      period,
      data: mentionTrend,
    };
    
    // Cache the result
    await setCache(cacheKey, JSON.stringify(result), 3600); // Cache for 1 hour
    
    res.status(StatusCodes.OK).json(result);
  } catch (error) {
    logger.error(`Error in getMentionTrend controller for product ID ${req.params.productId}:`, error);
    next(error);
  }
};

/**
 * Get aspect trend data for a product
 */
export const getAspectTrend = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const productId = parseInt(req.params.productId);
    
    if (isNaN(productId)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, 'Invalid product ID');
    }
    
    const period = (req.query.period as string) || 'month';
    
    if (!validPeriods.includes(period)) {
      throw new ApiError(StatusCodes.BAD_REQUEST, `Invalid period. Must be one of: ${validPeriods.join(', ')}`);
    }
    
    // Try to get from cache first
    const cacheKey = `trends:aspects:${productId}:${period}`;
    const cachedData = await getCache(cacheKey);
    
    if (cachedData) {
      return res.status(StatusCodes.OK).json(JSON.parse(cachedData));
    }
    
    const aspectTrend = await trendService.getAspectTrend(productId, period);
    
    const result = {
      success: true,
      productId,
      period,
      data: aspectTrend,
    };
    
    // Cache the result
    await setCache(cacheKey, JSON.stringify(result), 3600); // Cache for 1 hour
    
    res.status(StatusCodes.OK).json(result);
  } catch (error) {
    logger.error(`Error in getAspectTrend controller for product ID ${req.params.productId}:`, error);
    next(error);
  }
};
