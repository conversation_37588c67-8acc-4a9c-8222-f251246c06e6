import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { apiService } from '../../services/api';
import { LoadingSpinner, ApiErrorFallback } from '../common';
import { Star, ThumbsUp, ThumbsDown, MessageSquare, BarChart2 } from 'lucide-react';

interface BusinessReview {
  author_name: string;
  author_url?: string;
  profile_photo_url?: string;
  rating: number;
  text: string;
  time: number;
  language: string;
  sentiment_score?: number;
  sentiment_details?: {
    compound: number;
    neg: number;
    neu: number;
    pos: number;
  };
}

interface SentimentMetrics {
  average_score: number;
  positive_count: number;
  negative_count: number;
  neutral_count: number;
  total_analyzed: number;
}

interface BusinessReviewsProps {
  businessId: string;
  source: 'google' | 'facebook';
  limit?: number;
  showSentiment?: boolean;
  className?: string;
}

/**
 * Component to display business reviews with sentiment analysis
 */
const BusinessReviews: React.FC<BusinessReviewsProps> = ({
  businessId,
  source,
  limit = 10,
  showSentiment = true,
  className = '',
}) => {
  const [reviews, setReviews] = useState<BusinessReview[]>([]);
  const [metrics, setMetrics] = useState<SentimentMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<string>('recent');

  useEffect(() => {
    const fetchReviews = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await apiService.get(
          `/business/${source}/${businessId}/reviews`,
          {
            params: {
              limit,
              with_sentiment: showSentiment,
              sort_by: sortBy,
            },
          }
        );

        if (response.data) {
          setReviews(response.data.reviews || []);
          setMetrics(response.data.sentiment_metrics || null);
        } else {
          setError('No review data received');
        }
      } catch (err) {
        console.error('Error fetching business reviews:', err);
        setError('Failed to load reviews. Please try again.');
        
        // Use mock data for development
        setReviews(getMockReviews(5));
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, [businessId, source, limit, showSentiment, sortBy]);

  // Generate mock reviews for development
  const getMockReviews = (count: number): BusinessReview[] => {
    const mockReviews: BusinessReview[] = [];
    
    for (let i = 0; i < count; i++) {
      const rating = Math.floor(Math.random() * 5) + 1;
      const isPositive = rating >= 4;
      const isNeutral = rating === 3;
      const sentimentScore = isPositive ? 0.7 : (isNeutral ? 0 : -0.6);
      
      mockReviews.push({
        author_name: `Mock User ${i + 1}`,
        rating,
        text: `This is a ${isPositive ? 'positive' : (isNeutral ? 'neutral' : 'negative')} mock review for development purposes. ${
          isPositive ? 'Great experience overall!' : (isNeutral ? 'Average experience, nothing special.' : 'Disappointing experience, would not recommend.')
        }`,
        time: Math.floor(Date.now() / 1000) - (i * 86400),
        language: 'en',
        sentiment_score: sentimentScore,
        sentiment_details: {
          compound: sentimentScore,
          neg: isPositive ? 0.1 : 0.6,
          neu: 0.3,
          pos: isPositive ? 0.6 : 0.1
        }
      });
    }
    
    return mockReviews;
  };

  // Handle sort change
  const handleSortChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSortBy(event.target.value);
  };

  // Format timestamp
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString();
  };

  // Get sentiment color
  const getSentimentColor = (score?: number) => {
    if (score === undefined) return 'text-gray-400';
    if (score > 0.2) return 'text-green-500';
    if (score < -0.2) return 'text-red-500';
    return 'text-yellow-500';
  };

  // Get sentiment icon
  const getSentimentIcon = (score?: number) => {
    if (score === undefined) return <MessageSquare className="w-4 h-4" />;
    if (score > 0.2) return <ThumbsUp className="w-4 h-4" />;
    if (score < -0.2) return <ThumbsDown className="w-4 h-4" />;
    return <MessageSquare className="w-4 h-4" />;
  };

  // Render stars for rating
  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`flex justify-center items-center p-8 ${className}`}>
        <LoadingSpinner size="medium" text="Loading reviews..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 ${className}`}>
        <ApiErrorFallback error={error} onRetry={() => window.location.reload()} />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Sentiment metrics */}
      {showSentiment && metrics && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-md p-4 mb-6"
        >
          <h3 className="text-lg font-semibold flex items-center mb-3">
            <BarChart2 className="mr-2 text-primary" />
            Sentiment Analysis
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className={`text-2xl font-bold ${getSentimentColor(metrics.average_score)}`}>
                {metrics.average_score.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500">Average Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">{metrics.positive_count}</div>
              <div className="text-sm text-gray-500">Positive</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">{metrics.neutral_count}</div>
              <div className="text-sm text-gray-500">Neutral</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">{metrics.negative_count}</div>
              <div className="text-sm text-gray-500">Negative</div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Sort controls */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">
          Reviews ({reviews.length})
        </h3>
        <div className="flex items-center">
          <label htmlFor="sort-by" className="mr-2 text-sm text-gray-600">
            Sort by:
          </label>
          <select
            id="sort-by"
            value={sortBy}
            onChange={handleSortChange}
            className="text-sm border rounded-md px-2 py-1"
          >
            <option value="recent">Most Recent</option>
            <option value="oldest">Oldest First</option>
            <option value="rating_high">Highest Rating</option>
            <option value="rating_low">Lowest Rating</option>
            {showSentiment && (
              <>
                <option value="sentiment_high">Most Positive</option>
                <option value="sentiment_low">Most Negative</option>
              </>
            )}
          </select>
        </div>
      </div>

      {/* Reviews list */}
      <AnimatePresence>
        {reviews.length > 0 ? (
          <div className="space-y-4">
            {reviews.map((review, index) => (
              <motion.div
                key={`${review.author_name}-${review.time}-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
                className="bg-white rounded-lg shadow-sm p-4 border border-gray-100"
              >
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center">
                    {review.profile_photo_url ? (
                      <img
                        src={review.profile_photo_url}
                        alt={review.author_name}
                        className="w-10 h-10 rounded-full mr-3"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                        {review.author_name.charAt(0).toUpperCase()}
                      </div>
                    )}
                    <div>
                      <div className="font-medium">{review.author_name}</div>
                      <div className="text-xs text-gray-500">{formatDate(review.time)}</div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    {renderStars(review.rating)}
                    {showSentiment && review.sentiment_score !== undefined && (
                      <div className={`text-xs mt-1 flex items-center ${getSentimentColor(review.sentiment_score)}`}>
                        {getSentimentIcon(review.sentiment_score)}
                        <span className="ml-1">{review.sentiment_score.toFixed(2)}</span>
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-gray-700 mt-2">{review.text}</p>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No reviews available for this business.
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BusinessReviews;
