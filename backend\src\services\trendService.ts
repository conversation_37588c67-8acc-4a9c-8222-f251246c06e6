import { query } from '../config/database';
import { getCache, setCache } from '../config/redis';
import logger from '../utils/logger';
import { redditService } from './redditService';
import { Product, TrendData, AspectTrendData } from '../models/types';
import axios from 'axios';

// Cache TTL in seconds
const TREND_CACHE_TTL = 3600; // 1 hour

/**
 * Get sentiment trend data for a product
 */
export const getSentimentTrend = async (productId: number, period: string): Promise<TrendData> => {
  try {
    // Try to get from cache first
    const cacheKey = `trend:sentiment:${productId}:${period}`;
    const cachedData = await getCache(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Get product details
    const productResult = await query(
      'SELECT name FROM products WHERE id = $1',
      [productId]
    );

    if (productResult.rows.length === 0) {
      throw new Error(`Product with ID ${productId} not found`);
    }

    const productName = productResult.rows[0].name;

    // Get time range based on period
    const timeRange = getTimeRangeForPeriod(period);

    // Get sentiment trend data from database
    const result = await query(
      `SELECT
        date_trunc($1, created_at) as date,
        AVG(sentiment_score) as sentiment,
        COUNT(*) as count
      FROM reviews
      WHERE product_id = $2 AND created_at >= $3
      GROUP BY date_trunc($1, created_at)
      ORDER BY date_trunc($1, created_at)`,
      [getPeriodInterval(period), productId, timeRange.startDate]
    );

    // If no data in database, try to get from Reddit
    if (result.rows.length === 0) {
      const redditData = await getRedditSentimentData(productName, period);

      // Cache the result
      await setCache(cacheKey, JSON.stringify(redditData), TREND_CACHE_TTL);

      return redditData;
    }

    // Process database results
    const trendData: TrendData = {
      trend_direction: 'stable',
      change_percentage: 0,
      data_points: result.rows.map((row: any) => ({
        date: row.date,
        value: parseFloat(row.sentiment),
        count: parseInt(row.count),
      })),
    };

    // Calculate trend direction and change percentage
    if (trendData.data_points.length >= 2) {
      const firstPoint = trendData.data_points[0];
      const lastPoint = trendData.data_points[trendData.data_points.length - 1];

      const change = lastPoint.value - firstPoint.value;
      const changePercentage = (change / Math.abs(firstPoint.value)) * 100;

      trendData.change_percentage = parseFloat(changePercentage.toFixed(2));

      if (changePercentage > 5) {
        trendData.trend_direction = 'up';
      } else if (changePercentage < -5) {
        trendData.trend_direction = 'down';
      }
    }

    // Cache the result
    await setCache(cacheKey, JSON.stringify(trendData), TREND_CACHE_TTL);

    return trendData;
  } catch (error) {
    logger.error(`Error in getSentimentTrend service for product ID ${productId}:`, error);
    throw error;
  }
};

/**
 * Get mention trend data for a product
 */
export const getMentionTrend = async (productId: number, period: string): Promise<TrendData> => {
  try {
    // Try to get from cache first
    const cacheKey = `trend:mentions:${productId}:${period}`;
    const cachedData = await getCache(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Get product details
    const productResult = await query(
      'SELECT name FROM products WHERE id = $1',
      [productId]
    );

    if (productResult.rows.length === 0) {
      throw new Error(`Product with ID ${productId} not found`);
    }

    const productName = productResult.rows[0].name;

    // Get time range based on period
    const timeRange = getTimeRangeForPeriod(period);

    // Get mention trend data from database
    const result = await query(
      `SELECT
        date_trunc($1, created_at) as date,
        COUNT(*) as count
      FROM reviews
      WHERE product_id = $2 AND created_at >= $3
      GROUP BY date_trunc($1, created_at)
      ORDER BY date_trunc($1, created_at)`,
      [getPeriodInterval(period), productId, timeRange.startDate]
    );

    // If no data in database, try to get from Reddit
    if (result.rows.length === 0) {
      const redditData = await getRedditMentionData(productName, period);

      // Cache the result
      await setCache(cacheKey, JSON.stringify(redditData), TREND_CACHE_TTL);

      return redditData;
    }

    // Process database results
    const trendData: TrendData = {
      trend_direction: 'stable',
      change_percentage: 0,
      data_points: result.rows.map((row: any) => ({
        date: row.date,
        value: parseInt(row.count),
      })),
    };

    // Calculate trend direction and change percentage
    if (trendData.data_points.length >= 2) {
      const firstPoint = trendData.data_points[0];
      const lastPoint = trendData.data_points[trendData.data_points.length - 1];

      const change = lastPoint.value - firstPoint.value;
      const changePercentage = (change / Math.max(1, firstPoint.value)) * 100;

      trendData.change_percentage = parseFloat(changePercentage.toFixed(2));

      if (changePercentage > 10) {
        trendData.trend_direction = 'up';
      } else if (changePercentage < -10) {
        trendData.trend_direction = 'down';
      }
    }

    // Cache the result
    await setCache(cacheKey, JSON.stringify(trendData), TREND_CACHE_TTL);

    return trendData;
  } catch (error) {
    logger.error(`Error in getMentionTrend service for product ID ${productId}:`, error);
    throw error;
  }
};

/**
 * Get aspect trend data for a product
 */
export const getAspectTrend = async (productId: number, period: string): Promise<AspectTrendData> => {
  try {
    // Try to get from cache first
    const cacheKey = `trend:aspects:${productId}:${period}`;
    const cachedData = await getCache(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Get product details
    const productResult = await query(
      'SELECT name FROM products WHERE id = $1',
      [productId]
    );

    if (productResult.rows.length === 0) {
      throw new Error(`Product with ID ${productId} not found`);
    }

    const productName = productResult.rows[0].name;

    // Get time range based on period
    const timeRange = getTimeRangeForPeriod(period);

    // Get aspect trend data from database
    const result = await query(
      `SELECT
        aspect,
        AVG(sentiment_score) as sentiment,
        COUNT(*) as count
      FROM review_aspects
      WHERE product_id = $1 AND created_at >= $2
      GROUP BY aspect
      ORDER BY count DESC
      LIMIT 10`,
      [productId, timeRange.startDate]
    );

    // If no data in database, try to get from sentiment analysis service
    if (result.rows.length === 0) {
      const aspectData = await getAspectDataFromSentimentService(productName);

      // Cache the result
      await setCache(cacheKey, JSON.stringify(aspectData), TREND_CACHE_TTL);

      return aspectData;
    }

    // Process database results
    const aspectTrendData: AspectTrendData = {
      aspects: result.rows.map((row: any) => ({
        aspect: row.aspect,
        sentiment: parseFloat(row.sentiment),
        count: parseInt(row.count),
      })),
    };

    // Cache the result
    await setCache(cacheKey, JSON.stringify(aspectTrendData), TREND_CACHE_TTL);

    return aspectTrendData;
  } catch (error) {
    logger.error(`Error in getAspectTrend service for product ID ${productId}:`, error);
    throw error;
  }
};

/**
 * Get Reddit sentiment data for a product
 */
const getRedditSentimentData = async (productName: string, period: string): Promise<TrendData> => {
  try {
    // Get Reddit posts for the product
    const redditPosts = await redditService.searchProducts(productName);

    // Group posts by date
    const postsByDate = new Map<string, { total: number; sum: number; count: number }>();

    for (const post of redditPosts) {
      const date = formatDateByPeriod(post.createdAt, period);

      if (!postsByDate.has(date)) {
        postsByDate.set(date, { total: 0, sum: 0, count: 0 });
      }

      const dateData = postsByDate.get(date)!;

      // Analyze sentiment using the sentiment analysis service
      try {
        const sentimentResponse = await axios.post(
          `${process.env.SENTIMENT_API_URL || 'http://localhost:5000'}/analyze`,
          { text: post.content }
        );

        const sentimentScore = sentimentResponse.data.score;

        dateData.sum += sentimentScore;
        dateData.count += 1;
        dateData.total += 1;
      } catch (error) {
        logger.error('Error analyzing sentiment for Reddit post:', error);
        dateData.total += 1;
      }
    }

    // Convert to data points
    const dataPoints = Array.from(postsByDate.entries())
      .map(([date, data]) => ({
        date,
        value: data.count > 0 ? data.sum / data.count : 0,
        count: data.total,
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Calculate trend direction and change percentage
    let trendDirection = 'stable';
    let changePercentage = 0;

    if (dataPoints.length >= 2) {
      const firstPoint = dataPoints[0];
      const lastPoint = dataPoints[dataPoints.length - 1];

      const change = lastPoint.value - firstPoint.value;
      changePercentage = (change / Math.max(0.01, Math.abs(firstPoint.value))) * 100;

      if (changePercentage > 5) {
        trendDirection = 'up';
      } else if (changePercentage < -5) {
        trendDirection = 'down';
      }
    }

    return {
      trend_direction: trendDirection as 'up' | 'down' | 'stable',
      change_percentage: parseFloat(changePercentage.toFixed(2)),
      data_points: dataPoints,
    };
  } catch (error) {
    logger.error(`Error getting Reddit sentiment data for product "${productName}":`, error);
    throw error;
  }
};

/**
 * Get Reddit mention data for a product
 */
const getRedditMentionData = async (productName: string, period: string): Promise<TrendData> => {
  try {
    // Get Reddit posts for the product
    const redditPosts = await redditService.searchProducts(productName);

    // Group posts by date
    const postsByDate = new Map<string, number>();

    for (const post of redditPosts) {
      const date = formatDateByPeriod(post.createdAt, period);

      if (!postsByDate.has(date)) {
        postsByDate.set(date, 0);
      }

      postsByDate.set(date, postsByDate.get(date)! + 1);
    }

    // Convert to data points
    const dataPoints = Array.from(postsByDate.entries())
      .map(([date, count]) => ({
        date,
        value: count,
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Calculate trend direction and change percentage
    let trendDirection = 'stable';
    let changePercentage = 0;

    if (dataPoints.length >= 2) {
      const firstPoint = dataPoints[0];
      const lastPoint = dataPoints[dataPoints.length - 1];

      const change = lastPoint.value - firstPoint.value;
      changePercentage = (change / Math.max(1, firstPoint.value)) * 100;

      if (changePercentage > 10) {
        trendDirection = 'up';
      } else if (changePercentage < -10) {
        trendDirection = 'down';
      }
    }

    return {
      trend_direction: trendDirection as 'up' | 'down' | 'stable',
      change_percentage: parseFloat(changePercentage.toFixed(2)),
      data_points: dataPoints,
    };
  } catch (error) {
    logger.error(`Error getting Reddit mention data for product "${productName}":`, error);
    throw error;
  }
};

/**
 * Get aspect data from sentiment analysis service
 */
const getAspectDataFromSentimentService = async (productName: string): Promise<AspectTrendData> => {
  try {
    // Get Reddit posts for the product
    const redditPosts = await redditService.searchProducts(productName);

    // Combine all post content
    const combinedContent = redditPosts.map(post => post.content).join(' ');

    // Analyze aspects using the sentiment analysis service
    const response = await axios.post(
      `${process.env.SENTIMENT_API_URL || 'http://localhost:5000'}/analyze-aspects`,
      { text: combinedContent, product_name: productName }
    );

    return {
      aspects: response.data.aspects.map((aspect: any) => ({
        aspect: aspect.aspect,
        sentiment: aspect.sentiment,
        count: aspect.count,
      })),
    };
  } catch (error) {
    logger.error(`Error getting aspect data for product "${productName}":`, error);

    // Return empty aspects if error
    return { aspects: [] };
  }
};

/**
 * Helper function to get time range for a period
 */
const getTimeRangeForPeriod = (period: string) => {
  const now = new Date();
  let startDate = new Date();

  switch (period) {
    case 'day':
      startDate.setDate(now.getDate() - 1);
      break;
    case 'week':
      startDate.setDate(now.getDate() - 7);
      break;
    case 'month':
      startDate.setMonth(now.getMonth() - 1);
      break;
    case 'quarter':
      startDate.setMonth(now.getMonth() - 3);
      break;
    case 'year':
      startDate.setFullYear(now.getFullYear() - 1);
      break;
    default:
      startDate.setMonth(now.getMonth() - 1); // Default to month
  }

  return { startDate, endDate: now };
};

/**
 * Helper function to get PostgreSQL interval for a period
 */
const getPeriodInterval = (period: string): string => {
  switch (period) {
    case 'day':
      return 'hour';
    case 'week':
      return 'day';
    case 'month':
      return 'day';
    case 'quarter':
      return 'week';
    case 'year':
      return 'month';
    default:
      return 'day'; // Default to day
  }
};

/**
 * Helper function to format date by period
 */
const formatDateByPeriod = (date: Date, period: string): string => {
  switch (period) {
    case 'day':
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:00`;
    case 'week':
    case 'month':
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
    case 'quarter':
    case 'year':
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
    default:
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  }
};
