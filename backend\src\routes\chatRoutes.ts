import { Router } from 'express';
import * as chatController from '../controllers/chatController';

const router = Router();

/**
 * @swagger
 * /chat/message:
 *   post:
 *     summary: Send a message to the chat
 *     tags: [Chat]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *             properties:
 *               message:
 *                 type: string
 *                 description: User message
 *               sessionId:
 *                 type: string
 *                 description: Chat session ID
 *     responses:
 *       200:
 *         description: Chat response
 */
router.post('/message', chatController.sendMessage);

/**
 * @swagger
 * /chat/feedback:
 *   post:
 *     summary: Send feedback for a chat message
 *     tags: [Chat]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - messageId
 *               - helpful
 *             properties:
 *               messageId:
 *                 type: string
 *                 description: Message ID
 *               helpful:
 *                 type: boolean
 *                 description: Whether the message was helpful
 *     responses:
 *       200:
 *         description: Feedback received
 */
router.post('/feedback', chatController.sendFeedback);

export default router;
