import { query } from '../config/database';
import { getCache, setCache } from '../config/redis';
import logger from '../utils/logger';
import { Product, Review } from '../models/types';

/**
 * Get all products with pagination
 */
export const getProducts = async (page: number, limit: number): Promise<Product[]> => {
  try {
    const offset = (page - 1) * limit;

    // Try to get from cache first
    const cacheKey = `products:page:${page}:limit:${limit}`;
    const cachedData = await getCache(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Query database
    const result = await query(
      `SELECT
        p.id,
        p.name,
        p.brand,
        p.category,
        p.description,
        p.price,
        p.original_price,
        p.image_url,
        p.rating,
        p.review_count,
        p.sentiment_score
      FROM products p
      ORDER BY p.id
      LIMIT $1 OFFSET $2`,
      [limit, offset]
    );

    const products = result.rows.map((row: any) => ({
      id: row.id,
      name: row.name,
      brand: row.brand,
      category: row.category,
      description: row.description,
      price: row.price,
      originalPrice: row.original_price,
      imageUrl: row.image_url,
      rating: row.rating,
      reviewCount: row.review_count,
      sentimentScore: row.sentiment_score,
    }));

    // Cache the result
    await setCache(cacheKey, JSON.stringify(products), 3600); // Cache for 1 hour

    return products;
  } catch (error) {
    logger.error('Error in getProducts service:', error);
    throw error;
  }
};

/**
 * Get a product by ID
 */
export const getProductById = async (productId: number): Promise<Product | null> => {
  try {
    // Try to get from cache first
    const cacheKey = `product:${productId}`;
    const cachedData = await getCache(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Query database
    const result = await query(
      `SELECT
        p.id,
        p.name,
        p.brand,
        p.category,
        p.description,
        p.price,
        p.original_price,
        p.image_url,
        p.rating,
        p.review_count,
        p.sentiment_score,
        p.features,
        p.pros,
        p.cons
      FROM products p
      WHERE p.id = $1`,
      [productId]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];

    const product: Product = {
      id: row.id,
      name: row.name,
      brand: row.brand,
      category: row.category,
      description: row.description,
      price: row.price,
      originalPrice: row.original_price,
      imageUrl: row.image_url,
      rating: row.rating,
      reviewCount: row.review_count,
      sentimentScore: row.sentiment_score,
      features: row.features,
      pros: row.pros,
      cons: row.cons,
    };

    // Cache the result
    await setCache(cacheKey, JSON.stringify(product), 3600); // Cache for 1 hour

    return product;
  } catch (error) {
    logger.error(`Error in getProductById service for ID ${productId}:`, error);
    throw error;
  }
};

/**
 * Get reviews for a product
 */
export const getProductReviews = async (productId: number, page: number, limit: number): Promise<Review[]> => {
  try {
    const offset = (page - 1) * limit;

    // Try to get from cache first
    const cacheKey = `product:${productId}:reviews:page:${page}:limit:${limit}`;
    const cachedData = await getCache(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Query database
    const result = await query(
      `SELECT
        r.id,
        r.user_name,
        r.rating,
        r.title,
        r.content,
        r.date,
        r.helpful_count,
        r.sentiment_score,
        r.source
      FROM reviews r
      WHERE r.product_id = $1
      ORDER BY r.date DESC
      LIMIT $2 OFFSET $3`,
      [productId, limit, offset]
    );

    const reviews = result.rows.map((row: any) => ({
      id: row.id,
      userName: row.user_name,
      rating: row.rating,
      title: row.title,
      content: row.content,
      date: row.date,
      helpfulCount: row.helpful_count,
      sentimentScore: row.sentiment_score,
      source: row.source,
    }));

    // Cache the result
    await setCache(cacheKey, JSON.stringify(reviews), 3600); // Cache for 1 hour

    return reviews;
  } catch (error) {
    logger.error(`Error in getProductReviews service for ID ${productId}:`, error);
    throw error;
  }
};
