# ProductWhisper Backend Environment Variables
# Copy this file to .env and update the values as needed

# Server Configuration
NODE_ENV=development
PORT=8000
FRONTEND_URL=http://localhost:5173

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=productwhisper
DB_USER=postgres
DB_PASSWORD=your_password
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/productwhisper

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_REFRESH_SECRET=your_super_secret_refresh_key_here
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Reddit API Configuration
REDDIT_CLIENT_ID=H44nTyJUgV4waOKnC9YA9w
REDDIT_CLIENT_SECRET=VROpHb3GSWpRde66dru9X6cRYDwjgQ
REDDIT_USER_AGENT=ProductWhisper/1.0
REDDIT_REDIRECT_URI=http://localhost:8080

# Python Sentiment Analysis Service
SENTIMENT_API_URL=http://localhost:5000

# External APIs
GOOGLE_API_KEY=your_google_api_key
FACEBOOK_API_KEY=your_facebook_api_key

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,https://productwhisper.vercel.app

# Optional: Monitoring and Analytics
# SENTRY_DSN=your_sentry_dsn
# NEW_RELIC_LICENSE_KEY=your_new_relic_key
