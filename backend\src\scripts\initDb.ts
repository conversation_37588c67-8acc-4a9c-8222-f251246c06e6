import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import setupRedditCache from './setupRedditCache';

// Load environment variables
dotenv.config();

// Database connection parameters
const dbParams = {
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'productwhisper',
  password: process.env.DB_PASSWORD || 'postgres',
  port: parseInt(process.env.DB_PORT || '5432'),
};

// Function to create database if it doesn't exist
async function createDatabase() {
  // Connect to postgres database to create our app database
  const pgPool = new Pool({
    ...dbParams,
    database: 'postgres',
  });

  try {
    // Check if database exists
    const checkResult = await pgPool.query(
      `SELECT 1 FROM pg_database WHERE datname = $1`,
      [dbParams.database]
    );

    if (checkResult.rows.length === 0) {
      console.log(`Creating database: ${dbParams.database}`);
      await pgPool.query(`CREATE DATABASE ${dbParams.database}`);
      console.log(`Database ${dbParams.database} created successfully`);
    } else {
      console.log(`Database ${dbParams.database} already exists`);
    }
  } catch (error) {
    console.error('Error creating database:', error);
    throw error;
  } finally {
    await pgPool.end();
  }
}

// Function to run SQL schema file
async function runSchemaFile() {
  // Connect to our app database
  const appPool = new Pool(dbParams);

  try {
    // Read schema file
    const schemaPath = path.join(__dirname, '../../migrations/001_initial_schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');

    console.log('Running schema file...');
    await appPool.query(schema);
    console.log('Schema applied successfully');
  } catch (error) {
    console.error('Error applying schema:', error);
    throw error;
  } finally {
    await appPool.end();
  }
}

// Function to insert sample data
async function insertSampleData() {
  // Connect to our app database
  const appPool = new Pool(dbParams);

  try {
    console.log('Inserting sample data...');

    // Check if products table is empty
    const checkResult = await appPool.query('SELECT COUNT(*) FROM products');

    if (parseInt(checkResult.rows[0].count) > 0) {
      console.log('Sample data already exists, skipping insertion');
      return;
    }

    // Insert sample products
    await appPool.query(`
      INSERT INTO products (name, brand, category, description, price, original_price, image_url, rating, review_count, sentiment_score, features, pros, cons)
      VALUES
        ('Premium Wireless Headphones', 'SoundMaster', 'Electronics', 'Experience crystal-clear audio with our premium wireless headphones.', 249.99, 299.99, 'https://source.unsplash.com/random/600x600/?headphones', 4.7, 1243, 0.85,
          '{"Sound Quality": {"value": "Excellent", "score": 0.92}, "Battery Life": {"value": "30 hours", "score": 0.88}, "Comfort": {"value": "Very comfortable", "score": 0.85}, "Noise Cancellation": {"value": "Active", "score": 0.9}}',
          ARRAY['Exceptional sound clarity', 'Long battery life', 'Comfortable for extended wear', 'Effective noise cancellation'],
          ARRAY['Expensive', 'Occasional Bluetooth connectivity issues']),

        ('Ultra Slim Laptop', 'TechPro', 'Electronics', 'Powerful performance in an ultra-slim design.', 1299.99, 1499.99, 'https://source.unsplash.com/random/600x600/?laptop', 4.5, 876, 0.78,
          '{"Processor": {"value": "Intel i7", "score": 0.9}, "RAM": {"value": "16GB", "score": 0.85}, "Storage": {"value": "512GB SSD", "score": 0.88}, "Display": {"value": "15.6 inch 4K", "score": 0.92}}',
          ARRAY['Lightweight and portable', 'Fast performance', 'Beautiful display', 'Good battery life'],
          ARRAY['Limited ports', 'Runs hot under heavy load']),

        ('Smart Fitness Watch', 'FitTech', 'Wearables', 'Track your fitness goals with precision.', 199.99, 249.99, 'https://source.unsplash.com/random/600x600/?smartwatch', 4.3, 1567, 0.72,
          '{"Heart Rate Monitor": {"value": "Continuous", "score": 0.85}, "GPS": {"value": "Built-in", "score": 0.8}, "Water Resistance": {"value": "50m", "score": 0.9}, "Battery Life": {"value": "7 days", "score": 0.75}}',
          ARRAY['Accurate fitness tracking', 'Long battery life', 'Comfortable to wear', 'Good app integration'],
          ARRAY['Occasional syncing issues', 'Limited third-party app support'])
    `);

    // Insert sample reviews
    await appPool.query(`
      INSERT INTO reviews (product_id, user_name, rating, title, content, date, helpful_count, sentiment_score, source)
      VALUES
        (1, 'AudioEnthusiast', 5, 'Best headphones I''ve ever owned', 'These headphones are absolutely amazing. The sound quality is crystal clear, and the noise cancellation works perfectly. Battery life is impressive too - I can go days without charging.', NOW() - INTERVAL '10 days', 45, 0.95, 'verified_purchase'),
        (1, 'MusicLover123', 4, 'Great sound but minor issues', 'The sound quality is excellent and they''re very comfortable. My only complaint is that the Bluetooth occasionally disconnects for no reason. Otherwise, they''re perfect.', NOW() - INTERVAL '15 days', 32, 0.75, 'verified_purchase'),
        (1, 'TechReviewer', 5, 'Premium quality and performance', 'These headphones deliver on all fronts. The build quality is excellent, sound is immersive, and the noise cancellation is best-in-class. Highly recommended!', NOW() - INTERVAL '5 days', 28, 0.9, 'expert_review'),

        (2, 'LaptopPro', 5, 'Perfect for work and travel', 'This laptop is incredibly lightweight yet powerful. I can work all day on a single charge, and the display is gorgeous. Highly recommended for professionals on the go.', NOW() - INTERVAL '8 days', 37, 0.92, 'verified_purchase'),
        (2, 'TechWriter', 4, 'Almost perfect ultrabook', 'Great performance and beautiful design. The only downside is it gets quite hot when running demanding applications. Otherwise, it''s an excellent machine.', NOW() - INTERVAL '12 days', 25, 0.8, 'expert_review'),
        (2, 'Programmer123', 3, 'Good but overpriced', 'It''s a decent laptop with good specs, but I think it''s overpriced for what you get. There are better options available at this price point.', NOW() - INTERVAL '20 days', 18, 0.5, 'verified_purchase'),

        (3, 'FitnessFreak', 5, 'Perfect fitness companion', 'This watch has transformed my workout routine. The tracking is accurate, battery lasts for days, and the app provides great insights. Worth every penny!', NOW() - INTERVAL '7 days', 42, 0.9, 'verified_purchase'),
        (3, 'RunnerGirl', 4, 'Great for running, some minor issues', 'I love using this watch for my runs. The GPS is accurate and the heart rate monitor works well. Sometimes it has trouble syncing with my phone, but overall it''s great.', NOW() - INTERVAL '14 days', 31, 0.75, 'verified_purchase'),
        (3, 'GadgetGuru', 3, 'Decent but limited functionality', 'It''s good for basic fitness tracking, but the third-party app support is very limited. If you''re looking for a smartwatch with more features, look elsewhere.', NOW() - INTERVAL '25 days', 20, 0.5, 'expert_review')
    `);

    // Insert sample review aspects
    await appPool.query(`
      INSERT INTO review_aspects (product_id, review_id, aspect, sentiment_score)
      VALUES
        (1, 1, 'sound quality', 0.95),
        (1, 1, 'noise cancellation', 0.92),
        (1, 1, 'battery life', 0.9),
        (1, 2, 'sound quality', 0.85),
        (1, 2, 'comfort', 0.8),
        (1, 2, 'bluetooth', 0.4),
        (1, 3, 'build quality', 0.9),
        (1, 3, 'sound', 0.92),
        (1, 3, 'noise cancellation', 0.95),

        (2, 4, 'weight', 0.95),
        (2, 4, 'performance', 0.9),
        (2, 4, 'battery life', 0.92),
        (2, 4, 'display', 0.95),
        (2, 5, 'performance', 0.85),
        (2, 5, 'design', 0.9),
        (2, 5, 'temperature', 0.4),
        (2, 6, 'price', 0.3),
        (2, 6, 'specs', 0.7),

        (3, 7, 'tracking', 0.92),
        (3, 7, 'battery', 0.9),
        (3, 7, 'app', 0.85),
        (3, 8, 'gps', 0.85),
        (3, 8, 'heart rate monitor', 0.8),
        (3, 8, 'syncing', 0.5),
        (3, 9, 'fitness tracking', 0.7),
        (3, 9, 'app support', 0.3)
    `);

    console.log('Sample data inserted successfully');
  } catch (error) {
    console.error('Error inserting sample data:', error);
    throw error;
  } finally {
    await appPool.end();
  }
}

// Main function to initialize database
async function initDb() {
  try {
    console.log('Initializing database...');

    // Create database if it doesn't exist
    await createDatabase();

    // Run schema file
    await runSchemaFile();

    // Insert sample data
    await insertSampleData();

    // Setup Reddit cache tables
    await setupRedditCache();

    console.log('Database initialization completed successfully');
  } catch (error) {
    console.error('Database initialization failed:', error);
    process.exit(1);
  }
}

// Run the initialization
initDb();
