import { Pool, PoolClient } from 'pg';
import logger from '../utils/logger';

// Create a connection pool
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'productwhisper',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'Buzzybone1@',
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 2000, // How long to wait for a connection to become available
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

// Test database connection
export const connectDatabase = async (): Promise<void> => {
  let client: PoolClient | null = null;

  try {
    client = await pool.connect();
    logger.info('PostgreSQL database connection established successfully');
  } catch (error) {
    logger.error('Error connecting to PostgreSQL database:', error);
    throw error;
  } finally {
    if (client) client.release();
  }
};

// Query helper function
export const query = async (text: string, params?: any[]): Promise<any> => {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;

    logger.debug('Executed query', {
      text,
      duration,
      rows: result.rowCount
    });

    return result;
  } catch (error) {
    logger.error('Error executing query', { text, error });
    throw error;
  }
};

// Transaction helper
export const transaction = async <T>(callback: (client: PoolClient) => Promise<T>): Promise<T> => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

export default {
  pool,
  query,
  transaction,
  connectDatabase,
};
