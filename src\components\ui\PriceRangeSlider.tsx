import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Slider } from './slider';

interface PriceRangeSliderProps {
  min: number;
  max: number;
  value: [number, number];
  onChange: (value: [number, number]) => void;
  step?: number;
  formatValue?: (value: number) => string;
  className?: string;
}

const PriceRangeSlider: React.FC<PriceRangeSliderProps> = ({
  min,
  max,
  value,
  onChange,
  step = 1,
  formatValue = (value) => `$${value}`,
  className = '',
}) => {
  const [localValue, setLocalValue] = useState<[number, number]>(value);
  const [isDragging, setIsDragging] = useState(false);

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Handle slider value change
  const handleValueChange = (newValue: number[]) => {
    const typedValue = newValue as [number, number];
    setLocalValue(typedValue);
    
    // Only call onChange when dragging ends to prevent too many updates
    if (!isDragging) {
      onChange(typedValue);
    }
  };

  // Calculate the position of the tooltips
  const getTooltipPosition = (index: number) => {
    const percentage = ((localValue[index] - min) / (max - min)) * 100;
    return `calc(${percentage}% - 20px)`;
  };

  return (
    <div className={`relative pt-6 pb-8 ${className}`}>
      <Slider
        defaultValue={[min, max]}
        value={localValue}
        max={max}
        min={min}
        step={step}
        onValueChange={handleValueChange}
        onValueCommit={() => {
          setIsDragging(false);
          onChange(localValue);
        }}
        onPointerDown={() => setIsDragging(true)}
        className="mt-4"
      />
      
      {/* Min value tooltip */}
      <AnimatePresence>
        <motion.div
          key="min-tooltip"
          initial={{ opacity: 0, y: 10 }}
          animate={{ 
            opacity: isDragging ? 1 : 0.8, 
            y: isDragging ? -5 : 0,
            scale: isDragging ? 1.1 : 1
          }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.2 }}
          className="absolute top-0 px-2 py-1 bg-primary text-white text-xs font-medium rounded-md shadow-md"
          style={{ left: getTooltipPosition(0) }}
        >
          {formatValue(localValue[0])}
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-primary rotate-45"></div>
        </motion.div>
      </AnimatePresence>
      
      {/* Max value tooltip */}
      <AnimatePresence>
        <motion.div
          key="max-tooltip"
          initial={{ opacity: 0, y: 10 }}
          animate={{ 
            opacity: isDragging ? 1 : 0.8, 
            y: isDragging ? -5 : 0,
            scale: isDragging ? 1.1 : 1
          }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.2 }}
          className="absolute top-0 px-2 py-1 bg-primary text-white text-xs font-medium rounded-md shadow-md"
          style={{ left: getTooltipPosition(1) }}
        >
          {formatValue(localValue[1])}
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-primary rotate-45"></div>
        </motion.div>
      </AnimatePresence>
      
      {/* Display range values below slider */}
      <div className="flex items-center justify-between mt-2">
        <motion.div 
          className="px-2 py-1 bg-gray-50 rounded text-xs font-medium text-gray-700"
          animate={{ 
            backgroundColor: isDragging && localValue[0] > min ? "#f0f9ff" : "#f9fafb",
            color: isDragging && localValue[0] > min ? "#0284c7" : "#374151"
          }}
        >
          {formatValue(localValue[0])}
        </motion.div>
        <motion.div 
          className="px-2 py-1 bg-gray-50 rounded text-xs font-medium text-gray-700"
          animate={{ 
            backgroundColor: isDragging && localValue[1] < max ? "#f0f9ff" : "#f9fafb",
            color: isDragging && localValue[1] < max ? "#0284c7" : "#374151"
          }}
        >
          {formatValue(localValue[1])}
        </motion.div>
      </div>
    </div>
  );
};

export default PriceRangeSlider;
