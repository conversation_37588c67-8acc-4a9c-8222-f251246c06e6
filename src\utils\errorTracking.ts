/**
 * Error tracking utilities for ProductWhisper
 * Provides functions for tracking and reporting errors throughout the application
 */

// Types for error tracking
export interface ErrorDetails {
  message: string;
  stack?: string;
  componentStack?: string;
  componentName?: string;
  boundaryName?: string;
  timestamp: string;
  additionalInfo?: Record<string, any>;
}

/**
 * Track an error with detailed information
 * @param error The error object
 * @param componentInfo Optional component information
 * @param additionalInfo Optional additional context information
 */
export const trackError = (
  error: Error, 
  componentInfo?: string,
  additionalInfo?: Record<string, any>
): void => {
  // Log to console with special formatting for visibility
  console.group('%c Application Error Detected', 'color: white; background-color: #e53e3e; padding: 2px 6px; border-radius: 2px;');
  console.error('Error:', error);
  console.error('Component:', componentInfo || 'Unknown');
  console.error('Time:', new Date().toISOString());
  
  if (additionalInfo) {
    console.error('Additional Info:', additionalInfo);
  }
  
  console.groupEnd();
  
  // Collect error details for potential reporting
  const errorDetails: ErrorDetails = {
    message: error.message,
    stack: error.stack,
    componentName: componentInfo,
    timestamp: new Date().toISOString(),
    additionalInfo
  };
  
  // In a real application, you would send this to your error tracking service
  // sendToErrorTrackingService(errorDetails);
};

/**
 * Error handler for ErrorBoundary components
 * @param error The error object
 * @param errorInfo React error info with component stack
 * @param boundaryName Name of the boundary that caught the error
 */
export const errorBoundaryHandler = (
  error: Error, 
  errorInfo: React.ErrorInfo, 
  boundaryName?: string
): void => {
  // Log to console
  trackError(error, boundaryName || 'ErrorBoundary', {
    componentStack: errorInfo.componentStack
  });
  
  // Collect complete error details
  const errorDetails: ErrorDetails = {
    message: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
    boundaryName,
    timestamp: new Date().toISOString()
  };
  
  // Store in session storage for debugging
  storeErrorInSession(errorDetails);
  
  // In a real application, you would send this to your error tracking service
  // sendToErrorTrackingService(errorDetails);
};

/**
 * Store error details in session storage for debugging
 * @param errorDetails Error details to store
 */
const storeErrorInSession = (errorDetails: ErrorDetails): void => {
  try {
    // Get existing errors or initialize empty array
    const storedErrors = sessionStorage.getItem('pw_errors');
    const errors = storedErrors ? JSON.parse(storedErrors) : [];
    
    // Add new error and limit to last 10
    errors.unshift(errorDetails);
    const limitedErrors = errors.slice(0, 10);
    
    // Save back to session storage
    sessionStorage.setItem('pw_errors', JSON.stringify(limitedErrors));
  } catch (e) {
    // Fail silently if session storage is not available
    console.warn('Could not store error in session storage:', e);
  }
};

/**
 * Get all stored errors from session storage
 * @returns Array of stored error details
 */
export const getStoredErrors = (): ErrorDetails[] => {
  try {
    const storedErrors = sessionStorage.getItem('pw_errors');
    return storedErrors ? JSON.parse(storedErrors) : [];
  } catch (e) {
    console.warn('Could not retrieve errors from session storage:', e);
    return [];
  }
};

/**
 * Clear all stored errors from session storage
 */
export const clearStoredErrors = (): void => {
  try {
    sessionStorage.removeItem('pw_errors');
  } catch (e) {
    console.warn('Could not clear errors from session storage:', e);
  }
};

export default {
  trackError,
  errorBoundaryHandler,
  getStoredErrors,
  clearStoredErrors
};
