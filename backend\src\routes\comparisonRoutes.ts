import { Router } from 'express';
import * as comparisonController from '../controllers/comparisonController';

const router = Router();

/**
 * @swagger
 * /compare:
 *   post:
 *     summary: Compare products
 *     tags: [Comparison]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - productIds
 *             properties:
 *               productIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: Array of product IDs to compare
 *     responses:
 *       200:
 *         description: Comparison results
 */
router.post('/', comparisonController.compareProducts);

/**
 * @swagger
 * /compare/sentiment:
 *   post:
 *     summary: Compare product sentiment
 *     tags: [Comparison]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - productIds
 *             properties:
 *               productIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: Array of product IDs to compare
 *     responses:
 *       200:
 *         description: Sentiment comparison results
 */
router.post('/sentiment', comparisonController.compareSentiment);

export default router;
