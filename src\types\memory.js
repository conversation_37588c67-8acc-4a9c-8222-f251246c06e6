/**
 * @typedef {Object} Memory
 * @property {string} id - Unique identifier for the memory
 * @property {string} content - Content of the memory
 * @property {string} [category] - Optional category for the memory
 * @property {Date} createdAt - Date when the memory was created
 * @property {Date} [updatedAt] - Optional date when the memory was last updated
 */

/**
 * Empty export to make this a module
 * @type {null}
 */
export const Memory = null;

export default Memory;
