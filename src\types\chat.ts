export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'system';
  timestamp: Date;
  isError?: boolean;
  isTyping?: boolean;
}

export interface ChatAnalytics {
  totalMessages: number;
  userMessages: number;
  systemMessages: number;
  averageResponseTime: number;
  popularTopics: string[];
  sessionDuration: number;
}

export interface ChatFeedback {
  messageId: string;
  helpful: boolean;
  comment?: string;
  timestamp: Date;
}

export interface ChatContextType {
  messages: ChatMessage[];
  isOpen: boolean;
  isTyping: boolean;
  openChat: () => void;
  closeChat: () => void;
  toggleChat: () => void;
  sendMessage: (content: string) => void;
  clearMessages: () => void;
  clearCache: () => void;
  getAnalytics: () => any;
  submitFeedback: (messageId: string, helpful: boolean) => Promise<boolean>;
}
