import { query } from '../config/database';
import { getCache, setCache } from '../config/redis';
import logger from '../utils/logger';
import { Product, SearchFilters, SearchResult, TrendingSearch } from '../models/types';

/**
 * Search for products
 */
export const searchProducts = async (
  searchQuery: string,
  filters?: SearchFilters,
  page = 1,
  limit = 10
): Promise<SearchResult> => {
  try {
    const offset = (page - 1) * limit;

    // Build cache key including filters
    let cacheKey = `search:${searchQuery}:page:${page}:limit:${limit}`;
    if (filters) {
      cacheKey += `:filters:${JSON.stringify(filters)}`;
    }

    // Try to get from cache first
    const cachedData = await getCache(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Build query
    let sqlQuery = `
      SELECT
        p.id,
        p.name,
        p.brand,
        p.category,
        p.description,
        p.price,
        p.original_price,
        p.image_url,
        p.rating,
        p.review_count,
        p.sentiment_score
      FROM products p
      WHERE (
        p.name ILIKE $1 OR
        p.brand ILIKE $1 OR
        p.description ILIKE $1 OR
        p.category ILIKE $1
      )
    `;

    const queryParams: any[] = [`%${searchQuery}%`];
    let paramIndex = 2;

    // Add filters if provided
    if (filters) {
      if (filters.category) {
        sqlQuery += ` AND p.category = $${paramIndex}`;
        queryParams.push(filters.category);
        paramIndex++;
      }

      if (filters.brand) {
        sqlQuery += ` AND p.brand = $${paramIndex}`;
        queryParams.push(filters.brand);
        paramIndex++;
      }

      if (filters.minPrice !== undefined) {
        sqlQuery += ` AND p.price >= $${paramIndex}`;
        queryParams.push(filters.minPrice);
        paramIndex++;
      }

      if (filters.maxPrice !== undefined) {
        sqlQuery += ` AND p.price <= $${paramIndex}`;
        queryParams.push(filters.maxPrice);
        paramIndex++;
      }

      if (filters.minRating !== undefined) {
        sqlQuery += ` AND p.rating >= $${paramIndex}`;
        queryParams.push(filters.minRating);
        paramIndex++;
      }
    }

    // Count total results
    const countQuery = `SELECT COUNT(*) FROM (${sqlQuery}) AS count_query`;
    const countResult = await query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Add pagination
    sqlQuery += ` ORDER BY p.rating DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limit, offset);

    // Execute query
    const result = await query(sqlQuery, queryParams);

    // Map results to Product objects
    const products = result.rows.map((row: any) => ({
      id: row.id,
      name: row.name,
      brand: row.brand,
      category: row.category,
      description: row.description,
      price: row.price,
      originalPrice: row.original_price,
      imageUrl: row.image_url,
      rating: row.rating,
      reviewCount: row.review_count,
      sentimentScore: row.sentiment_score,
    }));

    // Get filter options
    const filterOptions = await getFilterOptions(searchQuery);

    // Save search query for trending
    await saveSearchQuery(searchQuery);

    const searchResult: SearchResult = {
      products,
      total,
      page,
      pageSize: limit,
      filters: filterOptions,
    };

    // Cache the result
    await setCache(cacheKey, JSON.stringify(searchResult), 3600); // Cache for 1 hour

    return searchResult;
  } catch (error) {
    logger.error(`Error in searchProducts service for query "${searchQuery}":`, error);
    throw error;
  }
};

/**
 * Get filter options for search results
 */
const getFilterOptions = async (searchQuery: string) => {
  try {
    // Get categories
    const categoriesQuery = `
      SELECT category, COUNT(*) as count
      FROM products
      WHERE name ILIKE $1 OR brand ILIKE $1 OR description ILIKE $1 OR category ILIKE $1
      GROUP BY category
      ORDER BY count DESC
    `;

    const categoriesResult = await query(categoriesQuery, [`%${searchQuery}%`]);

    // Get brands
    const brandsQuery = `
      SELECT brand, COUNT(*) as count
      FROM products
      WHERE name ILIKE $1 OR brand ILIKE $1 OR description ILIKE $1 OR category ILIKE $1
      GROUP BY brand
      ORDER BY count DESC
    `;

    const brandsResult = await query(brandsQuery, [`%${searchQuery}%`]);

    // Get price range
    const priceRangeQuery = `
      SELECT MIN(price) as min_price, MAX(price) as max_price
      FROM products
      WHERE name ILIKE $1 OR brand ILIKE $1 OR description ILIKE $1 OR category ILIKE $1
    `;

    const priceRangeResult = await query(priceRangeQuery, [`%${searchQuery}%`]);

    return {
      categories: categoriesResult.rows.map((row: any) => ({ name: row.category, count: parseInt(row.count) })),
      brands: brandsResult.rows.map((row: any) => ({ name: row.brand, count: parseInt(row.count) })),
      priceRange: [
        parseFloat(priceRangeResult.rows[0].min_price) || 0,
        parseFloat(priceRangeResult.rows[0].max_price) || 1000,
      ] as [number, number],
    };
  } catch (error) {
    logger.error(`Error in getFilterOptions for query "${searchQuery}":`, error);
    throw error;
  }
};

/**
 * Save search query for trending
 */
const saveSearchQuery = async (searchQuery: string) => {
  try {
    await query(
      `INSERT INTO search_queries (query, timestamp)
       VALUES ($1, NOW())`,
      [searchQuery]
    );
  } catch (error) {
    logger.error(`Error saving search query "${searchQuery}":`, error);
    // Don't throw error to avoid breaking the search flow
  }
};

/**
 * Get trending search terms
 */
export const getTrendingSearches = async (limit: number): Promise<TrendingSearch[]> => {
  try {
    // Try to get from cache first
    const cacheKey = `trending-searches:${limit}`;
    const cachedData = await getCache(cacheKey);

    if (cachedData) {
      return JSON.parse(cachedData);
    }

    // Get trending searches from the last 7 days
    const result = await query(
      `SELECT
        query,
        COUNT(*) as count
      FROM search_queries
      WHERE timestamp > NOW() - INTERVAL '7 days'
      GROUP BY query
      ORDER BY count DESC
      LIMIT $1`,
      [limit]
    );

    const trendingSearches = result.rows.map((row: any) => ({
      query: row.query,
      count: parseInt(row.count),
    }));

    // Cache the result
    await setCache(cacheKey, JSON.stringify(trendingSearches), 3600); // Cache for 1 hour

    return trendingSearches;
  } catch (error) {
    logger.error(`Error in getTrendingSearches service:`, error);
    throw error;
  }
};
