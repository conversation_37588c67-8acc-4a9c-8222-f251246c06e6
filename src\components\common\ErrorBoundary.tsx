import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, X, ChevronDown, ChevronUp, RefreshCw } from 'lucide-react';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  name?: string; // Identifier for this specific error boundary
  onError?: (error: Error, errorInfo: ErrorInfo, boundaryName?: string) => void; // Callback for custom error handling
  showErrorIndicator?: boolean; // Whether to show the error indicator in production
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showErrorDetails: boolean; // Toggle for showing/hiding error details
  isRecovering: boolean; // Flag for when attempting to recover
}

/**
 * Enhanced ErrorBoundary component to catch JavaScript errors anywhere in the child component tree,
 * log those errors with detailed information, and display a fallback UI with comprehensive error details.
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showErrorDetails: false,
      isRecovering: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      isRecovering: false
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    const boundaryName = this.props.name || 'Unnamed';

    // Enhanced console logging with grouping and formatting
    console.group(`%c ErrorBoundary [${boundaryName}] caught an error`, 'color: white; background-color: #e53e3e; padding: 2px 6px; border-radius: 2px;');
    console.error('Error:', error);
    console.error('Component Stack:', errorInfo.componentStack);
    console.error('Time:', new Date().toISOString());
    console.groupEnd();

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo, boundaryName);
    }

    this.setState({
      error,
      errorInfo,
      showErrorDetails: process.env.NODE_ENV === 'development' // Auto-show details in dev mode
    });
  }

  // Try to recover from the error by resetting the error state
  handleTryAgain = (): void => {
    this.setState({
      isRecovering: true
    });

    // Small delay to allow for any cleanup/reinitialization
    setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        showErrorDetails: false,
        isRecovering: false
      });
    }, 100);
  };

  // Toggle error details visibility
  toggleErrorDetails = (): void => {
    this.setState(prevState => ({
      showErrorDetails: !prevState.showErrorDetails
    }));
  };

  render(): ReactNode {
    const { hasError, error, errorInfo, showErrorDetails, isRecovering } = this.state;
    const { children, fallback, name, showErrorIndicator = true } = this.props;

    // If recovering, show a loading state
    if (isRecovering) {
      return (
        <div className="flex flex-col items-center justify-center p-8">
          <RefreshCw className="h-8 w-8 text-primary animate-spin mb-4" />
          <p className="text-gray-600">Recovering...</p>
        </div>
      );
    }

    // If there's an error, show the error UI
    if (hasError) {
      // If a custom fallback is provided, use it
      if (fallback) {
        return fallback;
      }

      // Default error UI with enhanced error details
      return (
        <div className="relative flex flex-col items-center justify-center p-8 bg-white rounded-xl shadow-sm border border-gray-100 text-center">
          {/* Error boundary identifier (only in dev mode) */}
          {process.env.NODE_ENV === 'development' && name && (
            <div className="absolute top-2 left-2 bg-gray-100 px-2 py-1 rounded text-xs text-gray-500">
              Boundary: {name}
            </div>
          )}

          <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Something went wrong</h2>
          <p className="text-gray-600 mb-4">
            We're sorry, but there was an error loading this content.
          </p>

          {/* Action buttons */}
          <div className="flex flex-wrap gap-3 justify-center mb-4">
            <button
              onClick={this.handleTryAgain}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </button>

            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Reload Page
            </button>

            {/* Toggle error details button - always visible in dev, optional in prod */}
            {(process.env.NODE_ENV === 'development' || showErrorIndicator) && (
              <button
                onClick={this.toggleErrorDetails}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors flex items-center"
              >
                {showErrorDetails ? (
                  <>
                    <ChevronUp className="h-4 w-4 mr-2" />
                    Hide Details
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4 mr-2" />
                    Show Details
                  </>
                )}
              </button>
            )}
          </div>

          {/* Error details section */}
          {showErrorDetails && error && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg text-left w-full overflow-auto border border-gray-200">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-red-700 font-semibold">Error Details:</h3>
                <button
                  onClick={this.toggleErrorDetails}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X size={16} />
                </button>
              </div>

              {/* Error message */}
              <div className="mb-4">
                <h4 className="text-sm text-gray-700 font-medium mb-1">Message:</h4>
                <p className="text-red-600 font-mono text-sm p-2 bg-red-50 rounded border border-red-200">
                  {error.toString()}
                </p>
              </div>

              {/* Error stack trace */}
              {error.stack && (
                <div className="mb-4">
                  <h4 className="text-sm text-gray-700 font-medium mb-1">Error Stack:</h4>
                  <pre className="text-xs text-gray-700 p-2 bg-gray-100 rounded border border-gray-200 overflow-auto max-h-40">
                    {error.stack}
                  </pre>
                </div>
              )}

              {/* Component stack trace */}
              {errorInfo && (
                <div>
                  <h4 className="text-sm text-gray-700 font-medium mb-1">Component Stack:</h4>
                  <pre className="text-xs text-gray-700 p-2 bg-gray-100 rounded border border-gray-200 overflow-auto max-h-40">
                    {errorInfo.componentStack}
                  </pre>
                </div>
              )}
            </div>
          )}
        </div>
      );
    }

    // If there's no error, render children normally
    return children;
  }
}

export default ErrorBoundary;
