{"dev": {"driver": "pg", "host": {"ENV": "DB_HOST"}, "port": {"ENV": "DB_PORT"}, "database": {"ENV": "DB_NAME"}, "user": {"ENV": "DB_USER"}, "password": {"ENV": "DB_PASSWORD"}}, "test": {"driver": "pg", "host": {"ENV": "TEST_DB_HOST"}, "port": {"ENV": "TEST_DB_PORT"}, "database": {"ENV": "TEST_DB_NAME"}, "user": {"ENV": "TEST_DB_USER"}, "password": {"ENV": "TEST_DB_PASSWORD"}}, "production": {"driver": "pg", "host": {"ENV": "DB_HOST"}, "port": {"ENV": "DB_PORT"}, "database": {"ENV": "DB_NAME"}, "user": {"ENV": "DB_USER"}, "password": {"ENV": "DB_PASSWORD"}, "ssl": {"rejectUnauthorized": false}}}