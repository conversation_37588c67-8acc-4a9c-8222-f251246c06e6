import { createClient } from 'redis';
import logger from '../utils/logger';

// Create a mock Redis client for development
class MockRedisClient {
  private data: Map<string, string> = new Map();
  private connected: boolean = false;

  async connect(): Promise<void> {
    this.connected = true;
    return Promise.resolve();
  }

  async get(key: string): Promise<string | null> {
    return this.data.get(key) || null;
  }

  async set(key: string, value: string, options?: any): Promise<void> {
    this.data.set(key, value);
    return Promise.resolve();
  }

  async del(key: string): Promise<void> {
    this.data.delete(key);
    return Promise.resolve();
  }

  async flushAll(): Promise<void> {
    this.data.clear();
    return Promise.resolve();
  }

  on(event: string, callback: Function): void {
    // Do nothing
  }
}

// Determine if we should use real Redis or mock
const useMockRedis = true; // Set to false to use real Redis

// Create Redis client or mock client
const redisClient = useMockRedis
  ? new MockRedisClient() as any
  : createClient({
    url: process.env.REDIS_URL || 'redis://localhost:6379',
  });

// Handle Redis errors
if (!useMockRedis) {
  redisClient.on('error', (error: any) => {
    logger.error('Redis error:', error);
  });
}

// Connect to Redis
export const connectRedis = async (): Promise<void> => {
  try {
    await redisClient.connect();
    logger.info('Redis connection established successfully');
  } catch (error: any) {
    logger.error('Error connecting to Redis:', error);
    logger.info('Continuing without Redis - caching will be disabled');
    // Don't throw error, allow the application to continue without Redis
  }
};

// Cache helper functions
export const getCache = async (key: string): Promise<string | null> => {
  try {
    return await redisClient.get(key);
  } catch (error: any) {
    logger.error('Redis get error:', error);
    return null;
  }
};

export const setCache = async (key: string, value: string, expireSeconds?: number): Promise<void> => {
  try {
    if (expireSeconds) {
      await redisClient.set(key, value, { EX: expireSeconds });
    } else {
      await redisClient.set(key, value);
    }
  } catch (error: any) {
    logger.error('Redis set error:', error);
  }
};

export const deleteCache = async (key: string): Promise<void> => {
  try {
    await redisClient.del(key);
  } catch (error: any) {
    logger.error('Redis delete error:', error);
  }
};

export const flushCache = async (): Promise<void> => {
  try {
    await redisClient.flushAll();
  } catch (error: any) {
    logger.error('Redis flush error:', error);
  }
};

export default {
  redisClient,
  connectRedis,
  getCache,
  setCache,
  deleteCache,
  flushCache,
};
