import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface CircleProps {
  size: number;
  color: string;
  x: number;
  y: number;
  duration: number;
  delay: number;
  opacity?: number;
  animationStyle?: 'float' | 'pulse' | 'bounce' | 'spin';
  blurAmount?: number;
}

const Circle: React.FC<CircleProps> = ({
  size,
  color,
  x,
  y,
  duration,
  delay,
  opacity = 0.5,
  animationStyle = 'float',
  blurAmount = 1
}) => {
  // Memoize the animation values based on animation style
  const animationValues = useMemo(() => {
    switch (animationStyle) {
      case 'pulse':
        return {
          opacity: [opacity * 0.7, opacity, opacity * 0.7],
          scale: [0.9, 1.1, 0.9],
        };
      case 'bounce':
        return {
          opacity: [opacity * 0.8, opacity, opacity * 0.8],
          y: [0, -30, 0],
          scale: [1, 0.9, 1],
        };
      case 'spin':
        return {
          opacity: [opacity * 0.7, opacity, opacity * 0.7],
          rotate: [0, 360],
          scale: [0.9, 1.1, 0.9],
        };
      case 'float':
      default:
        return {
          opacity: [opacity * 0.7, opacity, opacity * 0.6, opacity * 0.7],
          scale: [0.9, 1.1, 0.95, 0.9],
          x: [0, 20, -20, 0],
          y: [0, -20, 20, 0],
        };
    }
  }, [opacity, animationStyle]);

  return (
    <motion.div
      className="absolute rounded-full"
      style={{
        width: size,
        height: size,
        backgroundColor: color,
        top: `${y}%`,
        left: `${x}%`,
        filter: `blur(${size / 10 * blurAmount}px)`, // Dynamic blur based on size and blur amount
      }}
      initial={{ opacity: opacity * 0.5, scale: 0.8 }}
      animate={animationValues}
      transition={{
        duration,
        delay,
        repeat: Infinity,
        repeatType: animationStyle === 'spin' ? 'loop' : 'reverse',
        ease: "easeInOut",
      }}
    />
  );
};

interface AnimatedCirclesProps {
  variant?: 'primary' | 'secondary' | 'accent' | 'mixed' | 'success' | 'warning' | 'error';
  count?: number;
  className?: string;
  maxOpacity?: number;
  animationStyle?: 'float' | 'pulse' | 'bounce' | 'spin' | 'mixed';
  blurAmount?: number;
  speed?: 'slow' | 'medium' | 'fast';
}

const AnimatedCircles: React.FC<AnimatedCirclesProps> = ({
  variant = 'primary',
  count = 15,
  className = '',
  maxOpacity = 0.5,
  animationStyle = 'float',
  blurAmount = 1,
  speed = 'medium',
}) => {
  // Define color schemes based on variant with higher contrast
  const getColors = () => {
    switch (variant) {
      case 'primary':
        return ['#2D5589', '#4682B4', '#6495ED']; // Brighter blues
      case 'secondary':
        return ['#D4AF37', '#FFD700', '#FFC125']; // Brighter golds
      case 'accent':
        return ['#C77DAF', '#DB7093', '#FF69B4']; // Brighter pinks/mauves
      case 'success':
        return ['#2E8B57', '#3CB371', '#00FA9A']; // Brighter greens
      case 'warning':
        return ['#FF8C00', '#FFA500', '#FFD700']; // Brighter oranges/yellows
      case 'error':
        return ['#B22222', '#DC143C', '#FF4500']; // Brighter reds
      case 'mixed':
        return ['#4682B4', '#FFD700', '#DB7093', '#7B68EE', '#20B2AA', '#3CB371', '#FF8C00']; // Brighter mixed colors
      default:
        return ['#2D5589', '#4682B4', '#6495ED'];
    }
  };

  // Determine speed multiplier
  const getSpeedMultiplier = () => {
    switch (speed) {
      case 'slow': return 1.5;
      case 'fast': return 0.7;
      default: return 1;
    }
  };

  // Memoize the circles to prevent regeneration on every render
  const circles = useMemo(() => {
    const colors = getColors();
    const circlesArray = [];
    const speedMultiplier = getSpeedMultiplier();
    const animationStyles = ['float', 'pulse', 'bounce', 'spin'];

    // Limit the count to a reasonable number for performance
    const safeCount = Math.min(count, 25);

    for (let i = 0; i < safeCount; i++) {
      // Adjust size range based on count for better distribution
      const size = Math.random() * (150 - safeCount * 2) + 50;
      const color = colors[Math.floor(Math.random() * colors.length)];
      const x = Math.random() * 100; // Random x position (0-100%)
      const y = Math.random() * 100; // Random y position (0-100%)
      const duration = (Math.random() * 15 + 10) * speedMultiplier; // Random duration adjusted by speed
      const delay = Math.random() * 5; // Random delay between 0-5s
      const circleOpacity = maxOpacity * (0.7 + Math.random() * 0.3); // Varied opacity

      // Determine animation style
      const style = animationStyle === 'mixed'
        ? animationStyles[Math.floor(Math.random() * animationStyles.length)]
        : animationStyle;

      circlesArray.push(
        <Circle
          key={i}
          size={size}
          color={color}
          x={x}
          y={y}
          duration={duration}
          delay={delay}
          opacity={circleOpacity}
          animationStyle={style}
          blurAmount={blurAmount}
        />
      );
    }

    return circlesArray;
  }, [variant, count, maxOpacity, animationStyle, blurAmount, speed]); // Only regenerate when these props change

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {circles}
    </div>
  );
};

export default AnimatedCircles;
