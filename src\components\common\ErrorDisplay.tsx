import React, { useState, useEffect } from 'react';
import { AlertTriangle, X, Trash2, RefreshCw } from 'lucide-react';
import { getStoredErrors, clearStoredErrors, ErrorDetails } from '../../utils/errorTracking';

interface ErrorDisplayProps {
  position?: 'top-right' | 'bottom-right' | 'top-left' | 'bottom-left';
  showOnlyInDevelopment?: boolean;
}

/**
 * ErrorDisplay component for showing stored errors
 * Useful for debugging and monitoring errors in the application
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  position = 'bottom-left',
  showOnlyInDevelopment = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [errors, setErrors] = useState<ErrorDetails[]>([]);
  const [hasNewErrors, setHasNewErrors] = useState(false);

  // Don't render in production unless explicitly allowed
  if (showOnlyInDevelopment && process.env.NODE_ENV !== 'development') {
    return null;
  }

  // Position classes
  const positionClasses = {
    'top-right': 'top-4 right-4',
    'bottom-right': 'bottom-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-left': 'bottom-4 left-4',
  };

  // Load errors from session storage
  const loadErrors = () => {
    const storedErrors = getStoredErrors();
    setErrors(storedErrors);
    setHasNewErrors(storedErrors.length > 0);
  };

  // Clear all errors
  const handleClearErrors = () => {
    clearStoredErrors();
    setErrors([]);
    setHasNewErrors(false);
  };

  // Refresh errors
  const handleRefresh = () => {
    loadErrors();
  };

  // Load errors on mount and set up interval to check for new errors
  useEffect(() => {
    loadErrors();
    
    // Check for new errors every 5 seconds
    const interval = setInterval(() => {
      const storedErrors = getStoredErrors();
      if (storedErrors.length !== errors.length) {
        setErrors(storedErrors);
        setHasNewErrors(storedErrors.length > 0);
      }
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleTimeString();
    } catch (e) {
      return dateString;
    }
  };

  return (
    <>
      {/* Error Display Button */}
      <button
        className={`fixed ${positionClasses[position]} z-50 p-2 rounded-full shadow-lg transition-all duration-300 ${
          isOpen 
            ? 'bg-gray-700 text-white' 
            : hasNewErrors 
              ? 'bg-red-600 text-white animate-pulse' 
              : 'bg-gray-200 text-gray-700'
        }`}
        onClick={() => setIsOpen(!isOpen)}
        title={isOpen ? "Close error display" : "Show application errors"}
      >
        {isOpen ? (
          <X size={20} />
        ) : (
          <AlertTriangle size={20} />
        )}
        {!isOpen && hasNewErrors && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {errors.length}
          </span>
        )}
      </button>

      {/* Error Display Panel */}
      {isOpen && (
        <div 
          className={`fixed ${positionClasses[position]} z-40 w-96 max-w-[90vw] max-h-[80vh] bg-white rounded-lg shadow-xl border border-gray-200 transform translate-y-12 overflow-hidden flex flex-col`}
          style={{ marginBottom: position.includes('bottom') ? '3rem' : '0', marginTop: position.includes('top') ? '3rem' : '0' }}
        >
          {/* Header */}
          <div className="bg-gray-800 text-white p-3 flex justify-between items-center">
            <h3 className="font-medium flex items-center">
              <AlertTriangle size={16} className="mr-2" />
              Application Errors ({errors.length})
            </h3>
            <div className="flex items-center space-x-2">
              <button 
                onClick={handleRefresh}
                className="text-gray-300 hover:text-white p-1 rounded"
                title="Refresh errors"
              >
                <RefreshCw size={14} />
              </button>
              <button 
                onClick={handleClearErrors}
                className="text-gray-300 hover:text-white p-1 rounded"
                title="Clear all errors"
              >
                <Trash2 size={14} />
              </button>
              <button 
                onClick={() => setIsOpen(false)}
                className="text-gray-300 hover:text-white p-1 rounded"
                title="Close"
              >
                <X size={14} />
              </button>
            </div>
          </div>

          {/* Error List */}
          <div className="flex-1 overflow-y-auto">
            {errors.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                No errors recorded
              </div>
            ) : (
              <ul className="divide-y divide-gray-200">
                {errors.map((error, index) => (
                  <li key={index} className="p-3 hover:bg-gray-50">
                    <div className="flex justify-between items-start">
                      <h4 className="font-medium text-red-600 text-sm mb-1">{error.message}</h4>
                      <span className="text-xs text-gray-500">{formatDate(error.timestamp)}</span>
                    </div>
                    
                    {error.boundaryName && (
                      <p className="text-xs text-gray-600 mb-1">
                        <span className="font-medium">Boundary:</span> {error.boundaryName}
                      </p>
                    )}
                    
                    {error.componentName && (
                      <p className="text-xs text-gray-600 mb-1">
                        <span className="font-medium">Component:</span> {error.componentName}
                      </p>
                    )}
                    
                    {error.componentStack && (
                      <details className="mt-1">
                        <summary className="text-xs text-blue-600 cursor-pointer">Component Stack</summary>
                        <pre className="text-xs bg-gray-100 p-2 mt-1 rounded overflow-x-auto max-h-32">
                          {error.componentStack}
                        </pre>
                      </details>
                    )}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default ErrorDisplay;
