export interface Product {
  id: number;
  name: string;
  brand: string;
  category: string;
  price: number;
  originalPrice?: number;
  rating: number;
  reviewCount: number;
  sentimentScore?: number;
  image: string;
  description: string;
  inStock: boolean;
  discount?: number;
  tags?: string[];
  features?: ProductFeature[];
  specifications?: Record<string, string>;
  reviews?: ProductReview[];
}

export interface ProductFeature {
  id: number;
  name: string;
  description: string;
  sentimentScore?: number;
}

export interface ProductReview {
  id: number;
  userId: number;
  userName: string;
  rating: number;
  title: string;
  content: string;
  date: Date;
  helpful: number;
  unhelpful: number;
  sentimentScore?: number;
  verified: boolean;
}

export interface ProductFilter {
  categories?: string[];
  brands?: string[];
  priceRange?: [number, number];
  rating?: number;
  sentimentScore?: [number, number];
  inStock?: boolean;
  tags?: string[];
}

export interface ProductSortOption {
  field: 'price' | 'rating' | 'reviewCount' | 'sentimentScore' | 'name';
  direction: 'asc' | 'desc';
}

export interface ProductComparisonItem {
  product: Product;
  highlightedFeatures?: string[];
  pros?: string[];
  cons?: string[];
}

export interface TrendingProduct {
  id: number;
  name: string;
  description: string;
  brand: string;
  category: string;
  score: number;
  mention_count: number;
}
