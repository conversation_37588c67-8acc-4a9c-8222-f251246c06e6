import { Router } from 'express';
import * as recommendationController from '../controllers/recommendationController';

const router = Router();

/**
 * @swagger
 * /recommendations/trending:
 *   get:
 *     summary: Get trending products
 *     tags: [Recommendations]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of products to return
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Optional category filter
 *     responses:
 *       200:
 *         description: A list of trending products
 */
router.get('/trending', recommendationController.getTrendingProducts);

/**
 * @swagger
 * /recommendations/similar/{productId}:
 *   get:
 *     summary: Get similar products
 *     tags: [Recommendations]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Product ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 5
 *         description: Number of products to return
 *     responses:
 *       200:
 *         description: A list of similar products
 *       404:
 *         description: Product not found
 */
router.get('/similar/:productId', recommendationController.getSimilarProducts);

/**
 * @swagger
 * /recommendations/personalized:
 *   get:
 *     summary: Get personalized product recommendations
 *     tags: [Recommendations]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of products to return
 *     responses:
 *       200:
 *         description: A list of personalized product recommendations
 *       401:
 *         description: Unauthorized - User not logged in
 */
router.get('/personalized', recommendationController.getPersonalizedRecommendations);

export default router;
