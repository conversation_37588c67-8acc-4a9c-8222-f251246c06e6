import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FiTrendingUp, FiMessageCircle, FiExternalLink, FiFilter, FiCalendar, FiAlertCircle } from 'react-icons/fi';
import { apiService } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import useApiRequest from '../../hooks/useApiRequest';

interface RedditTrendPanelProps {
  category?: string;
  initialTimeframe?: 'day' | 'week' | 'month';
  limit?: number;
  showSentiment?: boolean;
}

/**
 * Component for displaying trending Reddit discussions
 */
const RedditTrendPanel: React.FC<RedditTrendPanelProps> = ({
  category,
  initialTimeframe = 'week',
  limit = 10,
  showSentiment = true
}) => {
  const [timeframe, setTimeframe] = useState<'day' | 'week' | 'month'>(initialTimeframe);

  // Use our custom hook for API requests
  const {
    data: trendData,
    loading,
    error,
    execute: fetchTrendData
  } = useApiRequest<any>(
    showSentiment
      ? apiService.getRedditTrendingProducts.bind(apiService)
      : apiService.getRedditTrendingDiscussions.bind(apiService)
  );

  // Fetch trend data when parameters change
  useEffect(() => {
    // Execute the API request with the current parameters
    fetchTrendData(category, timeframe, limit);
  }, [category, timeframe, limit, showSentiment, fetchTrendData]);

  // Helper function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Helper function to get sentiment color
  const getSentimentColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-green-500';
    if (score >= 0.4) return 'text-yellow-500';
    if (score >= 0.2) return 'text-orange-500';
    return 'text-red-500';
  };

  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-dark to-primary p-4 text-white">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold flex items-center">
            <FiTrendingUp className="mr-2" />
            Reddit Trending {showSentiment ? 'Products' : 'Discussions'}
          </h3>

          {/* Timeframe selector */}
          <div className="flex items-center space-x-2 text-sm">
            <FiCalendar className="text-white/80" />
            <select
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value as 'day' | 'week' | 'month')}
              className="bg-white/20 border-0 rounded px-2 py-1 text-white focus:ring-2 focus:ring-white/50"
            >
              <option value="day">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
          </div>
        </div>

        {category && (
          <div className="mt-1 text-sm text-white/80 flex items-center">
            <FiFilter className="mr-1" />
            Category: {category}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-4">
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <LoadingSpinner size="medium" text="Loading Reddit trends..." />
          </div>
        ) : error ? (
          <div className="text-red-500 p-4 text-center flex flex-col items-center">
            <FiAlertCircle className="w-8 h-8 mb-2" />
            <div>{error.message || 'Failed to load Reddit trend data'}</div>
            <button
              onClick={() => fetchTrendData(category, timeframe, limit)}
              className="mt-3 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors text-sm"
            >
              Try Again
            </button>
          </div>
        ) : trendData && trendData.data && trendData.data.length > 0 ? (
          <div className="space-y-4">
            {trendData.data.map((product: any, index: number) => (
              <motion.div
                key={`${product.name || product.product_name}-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="border border-gray-100 rounded-lg p-3 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start">
                  <h4 className="font-medium text-gray-900">{product.name || product.product_name}</h4>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500 flex items-center">
                      <FiMessageCircle className="mr-1" />
                      {product.comments || product.mention_count || product.count || 0}
                    </span>

                    {showSentiment && (product.sentiment_score || product.sentiment) && (
                      <span className={`text-sm font-medium ${getSentimentColor(
                        typeof product.sentiment_score === 'number'
                          ? product.sentiment_score
                          : product.sentiment?.score || 0
                      )}`}>
                        {((typeof product.sentiment_score === 'number'
                          ? product.sentiment_score
                          : product.sentiment?.score || 0) * 100).toFixed(0)}%
                      </span>
                    )}
                  </div>
                </div>

                {/* Top post or URL */}
                {((product.posts && product.posts.length > 0) || product.url) && (
                  <div className="mt-2 text-sm bg-gray-50 rounded p-2">
                    <a
                      href={product.url || (product.posts && product.posts[0].url)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary-dark flex items-center justify-between"
                    >
                      <span className="line-clamp-1">{product.title || (product.posts && product.posts[0].title) || 'View on Reddit'}</span>
                      <FiExternalLink className="ml-1 flex-shrink-0" />
                    </a>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatDate(product.created || product.created_at || (product.posts && product.posts[0].created_at) || new Date().toISOString())}
                    </div>
                  </div>
                )}

                {/* Sentiment aspects */}
                {showSentiment &&
                  ((product.aspects && product.aspects.length > 0) ||
                   (product.sentiment && product.sentiment.aspects && product.sentiment.aspects.length > 0)) && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {(product.aspects || (product.sentiment && product.sentiment.aspects) || []).slice(0, 3).map((aspect: any, i: number) => (
                      <span
                        key={aspect.name || aspect.aspect || `aspect-${i}`}
                        className={`text-xs px-2 py-1 rounded-full ${getSentimentColor(aspect.score || aspect.sentiment)} bg-opacity-10`}
                      >
                        {aspect.name || aspect.aspect}: {((aspect.score || aspect.sentiment) * 100).toFixed(0)}%
                      </span>
                    ))}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-gray-500 p-4 text-center">
            <p>No trending {showSentiment ? 'products' : 'discussions'} found.</p>
            <button
              onClick={() => fetchTrendData(category, timeframe, limit)}
              className="mt-3 px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm"
            >
              Refresh
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default RedditTrendPanel;
