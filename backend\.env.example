# Server Configuration
PORT=8000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=productwhisper
DB_USER=postgres
DB_PASSWORD=postgres

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Reddit API Configuration
REDDIT_USER_AGENT=ProductWhisper/1.0
REDDIT_CLIENT_ID=H44nTyJUgV4waOKnC9YA9w
REDDIT_CLIENT_SECRET=VROpHb3GSWpRde66dru9X6cRYDwjgQ
REDDIT_REDIRECT_URI=http://localhost:8080

# Sentiment Analysis Service
SENTIMENT_API_URL=http://localhost:5000

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=logs/api.log

# CORS
CORS_ORIGIN=http://localhost:3000
