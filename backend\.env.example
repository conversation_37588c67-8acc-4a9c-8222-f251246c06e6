# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=productwhisper
DB_USER=postgres
DB_PASSWORD=your_secure_password_here

# Redis Configuration
REDIS_URL=redis://redis:6379

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Reddit API Configuration
REDDIT_USER_AGENT=ProductWhisper/1.0
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USERNAME=your_reddit_username
REDDIT_PASSWORD=your_reddit_password

# Amazon Product API Configuration
AMAZON_ACCESS_KEY=your_amazon_access_key
AMAZON_SECRET_KEY=your_amazon_secret_key
AMAZON_PARTNER_TAG=your_amazon_partner_tag
AMAZON_REGION=us-east-1

# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key

# Sentiment Analysis Service
SENTIMENT_API_URL=http://nlp-service:5000

# Monitoring Configuration
ENABLE_METRICS=true
