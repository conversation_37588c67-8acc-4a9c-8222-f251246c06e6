import { Router } from 'express';
import * as trendController from '../controllers/trendController';

const router = Router();

/**
 * @swagger
 * /trends/all/{productId}:
 *   get:
 *     summary: Get all trend data for a product
 *     tags: [Trends]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Product ID
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [day, week, month, quarter, year]
 *           default: month
 *         description: Time period for trend data
 *     responses:
 *       200:
 *         description: All trend data for the product
 *       404:
 *         description: Product not found
 */
router.get('/all/:productId', trendController.getAllTrends);

/**
 * @swagger
 * /trends/sentiment/{productId}:
 *   get:
 *     summary: Get sentiment trend data for a product
 *     tags: [Trends]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Product ID
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [day, week, month, quarter, year]
 *           default: month
 *         description: Time period for trend data
 *     responses:
 *       200:
 *         description: Sentiment trend data for the product
 *       404:
 *         description: Product not found
 */
router.get('/sentiment/:productId', trendController.getSentimentTrend);

/**
 * @swagger
 * /trends/mentions/{productId}:
 *   get:
 *     summary: Get mention trend data for a product
 *     tags: [Trends]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Product ID
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [day, week, month, quarter, year]
 *           default: month
 *         description: Time period for trend data
 *     responses:
 *       200:
 *         description: Mention trend data for the product
 *       404:
 *         description: Product not found
 */
router.get('/mentions/:productId', trendController.getMentionTrend);

/**
 * @swagger
 * /trends/aspects/{productId}:
 *   get:
 *     summary: Get aspect trend data for a product
 *     tags: [Trends]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Product ID
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [day, week, month, quarter, year]
 *           default: month
 *         description: Time period for trend data
 *     responses:
 *       200:
 *         description: Aspect trend data for the product
 *       404:
 *         description: Product not found
 */
router.get('/aspects/:productId', trendController.getAspectTrend);

export default router;
