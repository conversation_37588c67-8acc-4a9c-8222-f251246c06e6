import json
import pytest
from app import app

@pytest.fixture
def client():
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

def test_health_check(client):
    """Test health check endpoint"""
    response = client.get('/health')
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['status'] == 'ok'

def test_analyze_sentiment(client):
    """Test sentiment analysis endpoint"""
    # Test with positive text
    positive_text = "This product is amazing! I love it and would recommend it to everyone."
    response = client.post('/analyze', json={'text': positive_text})
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['score'] > 0
    
    # Test with negative text
    negative_text = "This is terrible. I hate it and would never recommend it to anyone."
    response = client.post('/analyze', json={'text': negative_text})
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['score'] < 0
    
    # Test with neutral text
    neutral_text = "This is a product. It has features."
    response = client.post('/analyze', json={'text': neutral_text})
    assert response.status_code == 200
    data = json.loads(response.data)
    assert -0.3 < data['score'] < 0.3  # Roughly neutral

def test_analyze_aspects(client):
    """Test aspect analysis endpoint"""
    text = """
    The sound quality of these headphones is amazing. 
    The battery life is excellent, lasting over 20 hours. 
    However, the comfort could be better for long listening sessions.
    The noise cancellation works very well in most environments.
    """
    
    response = client.post('/analyze-aspects', json={
        'text': text,
        'product_name': 'Wireless Headphones'
    })
    
    assert response.status_code == 200
    data = json.loads(response.data)
    
    # Check that we have aspects
    assert 'aspects' in data
    assert len(data['aspects']) > 0
    
    # Check for expected aspects
    aspect_names = [aspect['aspect'] for aspect in data['aspects']]
    assert any(name in aspect_names for name in ['sound', 'battery', 'comfort', 'noise'])

def test_analyze_batch(client):
    """Test batch analysis endpoint"""
    texts = [
        "This product is amazing! I love it.",
        "This product is terrible. I hate it.",
        "This product is okay. It works as expected."
    ]
    
    response = client.post('/analyze-batch', json={'texts': texts})
    
    assert response.status_code == 200
    data = json.loads(response.data)
    
    # Check that we have results
    assert 'results' in data
    assert len(data['results']) == 3
    assert data['count'] == 3
    
    # Check that scores make sense
    assert data['results'][0]['score'] > 0  # Positive
    assert data['results'][1]['score'] < 0  # Negative
    assert -0.3 < data['results'][2]['score'] < 0.3  # Roughly neutral

def test_compare_texts(client):
    """Test text comparison endpoint"""
    text1 = "This product is amazing! The quality is excellent and it works perfectly."
    text2 = "This product is decent. The quality is good but it has some issues."
    
    response = client.post('/compare', json={'text1': text1, 'text2': text2})
    
    assert response.status_code == 200
    data = json.loads(response.data)
    
    # Check that we have comparison data
    assert 'text1' in data
    assert 'text2' in data
    assert 'difference' in data
    
    # Check that scores make sense
    assert data['text1']['score'] > data['text2']['score']  # text1 is more positive
    assert data['difference'] > 0  # Positive difference
