import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ProductCard from './ProductCard';

interface Product {
  id: number;
  name: string;
  brand: string;
  category: string;
  price: number;
  originalPrice?: number;
  rating: number;
  reviewCount: number;
  sentimentScore?: number;
  image: string;
  description: string;
  inStock: boolean;
  discount?: number;
  tags?: string[];
}

interface AnimatedProductGridProps {
  products: Product[];
  viewMode: 'grid' | 'list';
  onAddToCompare?: (productId: number) => void;
  onAddToFavorite?: (productId: number) => void;
  className?: string;
  animationVariant?: 'fade' | 'slide' | 'scale' | 'stagger';
}

const AnimatedProductGrid: React.FC<AnimatedProductGridProps> = ({
  products,
  viewMode,
  onAddToCompare,
  onAddToFavorite,
  className = '',
  animationVariant = 'stagger',
}) => {
  const [displayedProducts, setDisplayedProducts] = useState<Product[]>([]);
  const [prevViewMode, setPrevViewMode] = useState(viewMode);
  
  // Update displayed products when products or viewMode changes
  useEffect(() => {
    // If viewMode changed, animate out then in
    if (prevViewMode !== viewMode) {
      setDisplayedProducts([]);
      
      // Short delay to allow exit animations to complete
      const timer = setTimeout(() => {
        setDisplayedProducts(products);
        setPrevViewMode(viewMode);
      }, 300);
      
      return () => clearTimeout(timer);
    } else {
      setDisplayedProducts(products);
    }
  }, [products, viewMode, prevViewMode]);
  
  // Define animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: animationVariant === 'stagger' ? 0.1 : 0,
        delayChildren: 0.1,
      },
    },
  };
  
  const itemVariants = {
    fade: {
      hidden: { opacity: 0 },
      visible: { opacity: 1, transition: { duration: 0.5 } },
      exit: { opacity: 0, transition: { duration: 0.3 } },
    },
    slide: {
      hidden: { opacity: 0, y: 50 },
      visible: { opacity: 1, y: 0, transition: { type: 'spring', stiffness: 300, damping: 24 } },
      exit: { opacity: 0, y: -20, transition: { duration: 0.2 } },
    },
    scale: {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1, transition: { type: 'spring', stiffness: 300, damping: 24 } },
      exit: { opacity: 0, scale: 0.8, transition: { duration: 0.2 } },
    },
    stagger: {
      hidden: { opacity: 0, y: 20 },
      visible: { opacity: 1, y: 0, transition: { type: 'spring', stiffness: 300, damping: 24 } },
      exit: { opacity: 0, y: -20, transition: { duration: 0.2 } },
    },
  };
  
  // Get the appropriate item variant based on the animation type
  const getItemVariant = () => itemVariants[animationVariant];

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={`grid gap-4 ${
        viewMode === 'grid'
          ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
          : 'grid-cols-1'
      } ${className}`}
    >
      <AnimatePresence mode="popLayout">
        {displayedProducts.map((product) => (
          <motion.div
            key={product.id}
            variants={getItemVariant()}
            initial="hidden"
            animate="visible"
            exit="exit"
            layout
          >
            <ProductCard
              id={product.id}
              name={product.name}
              brand={product.brand}
              category={product.category}
              price={product.price}
              originalPrice={product.originalPrice}
              rating={product.rating}
              reviewCount={product.reviewCount}
              sentimentScore={product.sentimentScore}
              image={product.image}
              description={product.description}
              inStock={product.inStock}
              discount={product.discount}
              tags={product.tags}
              viewMode={viewMode}
              onAddToCompare={onAddToCompare ? () => onAddToCompare(product.id) : undefined}
              onAddToFavorite={onAddToFavorite ? () => onAddToFavorite(product.id) : undefined}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
};

export default AnimatedProductGrid;
