import os
import json
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
import nltk
from nltk.sentiment.vader import SentimentIntensityAnalyzer
# Disable spaCy import since it's causing issues
# import spacy
from textblob import TextBlob
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('nlp_service.log')
    ]
)
logger = logging.getLogger(__name__)

# Download NLTK data
try:
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    logger.info("NLTK data downloaded successfully")
except Exception as e:
    logger.error(f"Error downloading NLTK data: {e}")

# Disable spaCy model loading
nlp = None
logger.info("spaCy model disabled, using simple NLP fallbacks")

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Initialize sentiment analyzer
sia = SentimentIntensityAnalyzer()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'ok',
        'message': 'Sentiment analysis service is running'
    })

@app.route('/analyze', methods=['POST'])
def analyze_sentiment():
    """Analyze sentiment of text"""
    try:
        data = request.json

        if not data or 'text' not in data:
            return jsonify({
                'error': 'No text provided for analysis'
            }), 400

        text = data['text']
        product_name = data.get('product_name', None)

        # Get sentiment scores
        sentiment_scores = analyze_text_sentiment(text)

        # Extract entities if spaCy is available
        entities = extract_entities(text, product_name) if nlp else []

        # Extract aspects
        aspects = extract_aspects(text, product_name) if nlp else []

        # Create response
        response = {
            'score': sentiment_scores['compound'],
            'details': sentiment_scores,
            'text_sample': text[:200] + '...' if len(text) > 200 else text,
            'entities': entities,
            'aspects': aspects
        }

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error analyzing sentiment: {e}")
        return jsonify({
            'error': 'Error analyzing sentiment',
            'message': str(e)
        }), 500

@app.route('/analyze-aspects', methods=['POST'])
def analyze_aspects():
    """Analyze aspects of a product from text"""
    try:
        data = request.json

        if not data or 'text' not in data:
            return jsonify({
                'error': 'No text provided for analysis'
            }), 400

        text = data['text']
        product_name = data.get('product_name', None)

        # Extract aspects
        aspects = extract_aspects(text, product_name) if nlp else []

        # Create response
        response = {
            'product_name': product_name,
            'aspects': aspects
        }

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error analyzing aspects: {e}")
        return jsonify({
            'error': 'Error analyzing aspects',
            'message': str(e)
        }), 500

@app.route('/analyze-batch', methods=['POST'])
def analyze_batch():
    """Analyze sentiment of multiple texts"""
    try:
        data = request.json

        if not data or 'texts' not in data or not isinstance(data['texts'], list):
            return jsonify({
                'error': 'No texts provided for analysis or invalid format'
            }), 400

        texts = data['texts']
        product_name = data.get('product_name', None)

        results = []
        for text in texts:
            # Get sentiment scores
            sentiment_scores = analyze_text_sentiment(text)

            # Create result
            result = {
                'score': sentiment_scores['compound'],
                'details': sentiment_scores,
                'text_sample': text[:100] + '...' if len(text) > 100 else text
            }

            results.append(result)

        # Create response
        response = {
            'results': results,
            'count': len(results)
        }

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error analyzing batch: {e}")
        return jsonify({
            'error': 'Error analyzing batch',
            'message': str(e)
        }), 500

@app.route('/compare', methods=['POST'])
def compare_texts():
    """Compare sentiment between two texts"""
    try:
        data = request.json

        if not data or 'text1' not in data or 'text2' not in data:
            return jsonify({
                'error': 'Two texts are required for comparison'
            }), 400

        text1 = data['text1']
        text2 = data['text2']

        # Get sentiment scores
        sentiment1 = analyze_text_sentiment(text1)
        sentiment2 = analyze_text_sentiment(text2)

        # Extract aspects if spaCy is available
        aspects1 = extract_aspects(text1) if nlp else []
        aspects2 = extract_aspects(text2) if nlp else []

        # Find common aspects
        common_aspects = []
        aspect1_dict = {a['aspect']: a for a in aspects1}
        aspect2_dict = {a['aspect']: a for a in aspects2}

        all_aspects = set(aspect1_dict.keys()) | set(aspect2_dict.keys())

        for aspect in all_aspects:
            aspect_data = {
                'aspect': aspect,
                'text1': aspect1_dict.get(aspect, {'sentiment': 0, 'count': 0}),
                'text2': aspect2_dict.get(aspect, {'sentiment': 0, 'count': 0}),
                'difference': aspect1_dict.get(aspect, {'sentiment': 0}).get('sentiment', 0) -
                              aspect2_dict.get(aspect, {'sentiment': 0}).get('sentiment', 0)
            }
            common_aspects.append(aspect_data)

        # Sort by absolute difference
        common_aspects.sort(key=lambda x: abs(x['difference']), reverse=True)

        # Create response
        response = {
            'text1': {
                'score': sentiment1['compound'],
                'details': sentiment1
            },
            'text2': {
                'score': sentiment2['compound'],
                'details': sentiment2
            },
            'difference': sentiment1['compound'] - sentiment2['compound'],
            'common_aspects': common_aspects[:10]  # Top 10 aspects with biggest differences
        }

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error comparing texts: {e}")
        return jsonify({
            'error': 'Error comparing texts',
            'message': str(e)
        }), 500

def analyze_text_sentiment(text):
    """Analyze sentiment of text using VADER"""
    if not text or not isinstance(text, str):
        return {'compound': 0, 'neg': 0, 'neu': 1, 'pos': 0}

    # Clean text
    text = clean_text(text)

    # Get VADER sentiment
    sentiment = sia.polarity_scores(text)

    return sentiment

def extract_entities(text, product_name=None):
    """Extract entities from text using spaCy"""
    if not nlp or not text or not isinstance(text, str):
        return []

    # Clean text
    text = clean_text(text)

    # Process text with spaCy
    doc = nlp(text)

    # Extract entities
    entities = []
    for ent in doc.ents:
        if ent.label_ in ['PRODUCT', 'ORG', 'PERSON', 'GPE', 'LOC', 'WORK_OF_ART']:
            # Get sentiment for entity
            entity_sentiment = analyze_text_sentiment(ent.text)

            entities.append({
                'name': ent.text,
                'type': ent.label_,
                'sentiment': entity_sentiment['compound']
            })

    # Add product name if provided and not already in entities
    if product_name and not any(e['name'].lower() == product_name.lower() for e in entities):
        product_sentiment = analyze_text_sentiment(product_name)
        entities.append({
            'name': product_name,
            'type': 'PRODUCT',
            'sentiment': product_sentiment['compound']
        })

    return entities

def extract_aspects(text, product_name=None):
    """Extract aspects and their sentiment from text"""
    if not text or not isinstance(text, str):
        return []

    # Clean text
    text = clean_text(text)

    # Common product aspects
    common_aspects = [
        'quality', 'price', 'value', 'design', 'performance', 'battery', 'screen',
        'display', 'camera', 'sound', 'build', 'comfort', 'durability', 'reliability',
        'speed', 'weight', 'size', 'usability', 'features', 'functionality', 'connectivity',
        'support', 'warranty', 'shipping', 'packaging', 'installation', 'setup'
    ]

    # Add product-specific aspects if product name is provided
    if product_name:
        if 'headphone' in product_name.lower() or 'earphone' in product_name.lower():
            common_aspects.extend(['noise cancellation', 'bass', 'treble', 'audio', 'fit', 'bluetooth'])
        elif 'laptop' in product_name.lower() or 'computer' in product_name.lower():
            common_aspects.extend(['keyboard', 'touchpad', 'processor', 'gpu', 'cooling', 'fan', 'ports'])
        elif 'phone' in product_name.lower() or 'smartphone' in product_name.lower():
            common_aspects.extend(['battery life', 'camera quality', 'storage', 'processor', 'screen'])
        elif 'watch' in product_name.lower() or 'smartwatch' in product_name.lower():
            common_aspects.extend(['fitness tracking', 'heart rate', 'gps', 'battery life', 'water resistance'])

    # Process text with spaCy if available
    if nlp:
        doc = nlp(text)
        sentences = [sent.text for sent in doc.sents]
    else:
        # Fallback to simple sentence splitting
        sentences = re.split(r'[.!?]+', text)

    # Extract aspects and their sentiment
    aspects = {}

    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue

        # Check for common aspects in sentence
        for aspect in common_aspects:
            if aspect.lower() in sentence.lower():
                # Get sentiment for sentence
                sentiment = analyze_text_sentiment(sentence)

                # Add or update aspect
                if aspect not in aspects:
                    aspects[aspect] = {
                        'sentiment_sum': sentiment['compound'],
                        'count': 1,
                        'sentences': [sentence],
                        'adjectives': extract_adjectives(sentence) if nlp else []
                    }
                else:
                    aspects[aspect]['sentiment_sum'] += sentiment['compound']
                    aspects[aspect]['count'] += 1
                    aspects[aspect]['sentences'].append(sentence)
                    if nlp:
                        aspects[aspect]['adjectives'].extend(extract_adjectives(sentence))

    # Format aspects
    formatted_aspects = []
    for aspect, data in aspects.items():
        avg_sentiment = data['sentiment_sum'] / data['count']
        formatted_aspects.append({
            'aspect': aspect,
            'sentiment': avg_sentiment,
            'count': data['count'],
            'sentence': data['sentences'][0],  # Include first sentence as example
            'adjectives': list(set(data['adjectives']))[:5]  # Top 5 unique adjectives
        })

    # Sort by count (most mentioned first)
    formatted_aspects.sort(key=lambda x: x['count'], reverse=True)

    return formatted_aspects

def extract_adjectives(text):
    """Extract adjectives from text using spaCy"""
    if not nlp or not text:
        return []

    doc = nlp(text)
    return [token.text for token in doc if token.pos_ == 'ADJ']

def clean_text(text):
    """Clean text for analysis"""
    if not text:
        return ""

    # Convert to lowercase
    text = text.lower()

    # Remove URLs
    text = re.sub(r'https?://\S+|www\.\S+', '', text)

    # Remove HTML tags
    text = re.sub(r'<.*?>', '', text)

    # Remove special characters and numbers
    text = re.sub(r'[^\w\s]', ' ', text)
    text = re.sub(r'\d+', '', text)

    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    return text

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
