import { query } from '../config/database';
import { redditService } from './redditService';
import logger from '../utils/logger';
import crypto from 'crypto';

/**
 * Reddit Cache Service
 *
 * This service handles caching of Reddit API responses in the database
 * to prevent exceeding Reddit's rate limits.
 */
class RedditCacheService {
  /**
   * Default TTL for cached responses in seconds
   */
  private readonly DEFAULT_TTL = 3600; // 1 hour

  /**
   * Generate a cache key from request parameters
   */
  private generateCacheKey(requestType: string, params: Record<string, any>): string {
    // Sort params to ensure consistent keys
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, key) => {
        acc[key] = params[key];
        return acc;
      }, {} as Record<string, any>);

    // Create a string representation of the request
    const requestString = `${requestType}:${JSON.stringify(sortedParams)}`;

    // Generate MD5 hash of the request string
    return crypto.createHash('md5').update(requestString).digest('hex');
  }

  /**
   * Check if a cached response exists and is valid
   */
  async getCachedResponse(requestType: string, params: Record<string, any>): Promise<any | null> {
    try {
      const cacheKey = this.generateCacheKey(requestType, params);

      // Query the database for the cached response
      const result = await query(
        `SELECT r.id, r.cache_key, r.expires_at, cr.response_data
         FROM reddit_cache_requests r
         JOIN reddit_cache_responses cr ON r.id = cr.request_id
         WHERE r.cache_key = $1 AND r.expires_at > NOW()`,
        [cacheKey]
      );

      // If no cached response found, return null
      if (result.rows.length === 0) {
        return null;
      }

      // Update last accessed timestamp
      await query(
        'SELECT update_reddit_cache_access($1)',
        [cacheKey]
      );

      logger.info(`Using cached Reddit API response for ${requestType}`);
      return result.rows[0].response_data;
    } catch (error) {
      logger.error(`Error getting cached Reddit response: ${error}`);
      return null;
    }
  }

  /**
   * Cache a response in the database
   */
  async cacheResponse(
    requestType: string,
    params: Record<string, any>,
    responseData: any,
    ttlSeconds: number = this.DEFAULT_TTL
  ): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(requestType, params);
      const expiresAt = new Date(Date.now() + ttlSeconds * 1000);

      // Begin transaction
      await query('BEGIN');

      // Insert or update the request
      const requestResult = await query(
        `INSERT INTO reddit_cache_requests
         (cache_key, request_type, parameters, expires_at)
         VALUES ($1, $2, $3, $4)
         ON CONFLICT (cache_key)
         DO UPDATE SET
           parameters = $3,
           expires_at = $4,
           last_accessed_at = NOW(),
           access_count = reddit_cache_requests.access_count + 1
         RETURNING id`,
        [cacheKey, requestType, JSON.stringify(params), expiresAt]
      );

      const requestId = requestResult.rows[0].id;

      // Insert the response
      await query(
        `INSERT INTO reddit_cache_responses
         (request_id, response_data)
         VALUES ($1, $2)`,
        [requestId, JSON.stringify(responseData)]
      );

      // Commit transaction
      await query('COMMIT');

      logger.info(`Cached Reddit API response for ${requestType}`);
    } catch (error) {
      // Rollback transaction on error
      await query('ROLLBACK');
      logger.error(`Error caching Reddit response: ${error}`);
    }
  }

  /**
   * Get trending discussions with caching
   */
  async getTrendingDiscussions(
    category?: string,
    timeframe: string = 'week',
    limit: number = 20
  ): Promise<any> {
    const params = { category, timeframe, limit };

    // Bypass cache and fetch directly from Reddit API
    try {
      const response = await redditService.searchProducts(category || 'electronics', limit);

      // Skip caching for now
      // const ttl = timeframe === 'day' ? 3600 : timeframe === 'week' ? 7200 : 14400; // 1, 2, or 4 hours
      // await this.cacheResponse('trending_discussions', params, response, ttl);

      return response;
    } catch (error) {
      logger.error(`Error fetching Reddit trending discussions: ${error}`);
      throw error;
    }
  }

  /**
   * Get trending products with caching
   */
  async getTrendingProducts(
    category?: string,
    timeframe: string = 'week',
    limit: number = 10
  ): Promise<any> {
    const params = { category, timeframe, limit };

    // Bypass cache and fetch directly from Reddit API
    try {
      const response = await redditService.getTrendingProducts(limit);

      // Skip caching for now
      // const ttl = timeframe === 'day' ? 3600 : timeframe === 'week' ? 7200 : 14400; // 1, 2, or 4 hours
      // await this.cacheResponse('trending_products', params, response, ttl);

      return response;
    } catch (error) {
      logger.error(`Error fetching Reddit trending products: ${error}`);
      throw error;
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpiredCache(): Promise<number> {
    try {
      const result = await query('SELECT cleanup_expired_reddit_cache()');
      const deletedCount = parseInt(result.rows[0].cleanup_expired_reddit_cache);

      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} expired Reddit cache entries`);
      }

      return deletedCount;
    } catch (error) {
      logger.error(`Error cleaning up expired Reddit cache: ${error}`);
      return 0;
    }
  }
}

// Export singleton instance
export const redditCacheService = new RedditCacheService();
