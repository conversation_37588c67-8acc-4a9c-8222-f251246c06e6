{"name": "productwhisper-backend", "private": true, "version": "1.0.0", "type": "module", "description": "ProductWhisper Backend - Node.js/Express API with Python sentiment analysis service", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src/**/*.ts", "test": "jest", "test:watch": "jest --watch", "migrate": "tsx src/database/migrate.ts", "seed": "tsx src/database/seed.ts", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "sentiment:dev": "cd python-nlp-service && python app.py"}, "dependencies": {"express": "^4.21.2", "cors": "^2.8.5", "helmet": "^8.0.0", "express-rate-limit": "^7.4.1", "compression": "^1.7.5", "morgan": "^1.10.0", "dotenv": "^16.4.7", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.13.3", "pg": "^8.13.1", "redis": "^4.7.0", "axios": "^1.9.0", "socket.io": "^4.8.1", "winston": "^3.17.0", "node-cron": "^3.0.3", "uuid": "^11.0.4"}, "devDependencies": {"@types/express": "^5.0.0", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/pg": "^8.11.10", "@types/node": "^22.10.5", "@types/uuid": "^10.0.0", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "eslint": "^9.25.0", "typescript": "~5.8.3", "tsx": "^4.19.2", "jest": "^29.7.0", "@types/jest": "^29.5.14", "ts-jest": "^29.2.5", "nodemon": "^3.1.9"}}