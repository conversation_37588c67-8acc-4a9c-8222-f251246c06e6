# ProductWhisper Sentiment Analysis Service

This is the sentiment analysis microservice for ProductWhisper, built with Python and Flask. It provides natural language processing capabilities for analyzing product reviews and discussions.

## Features

- Sentiment analysis using VADER (Valence Aware Dictionary and sEntiment Reasoner)
- Aspect-based sentiment analysis
- Entity extraction
- Text comparison
- Batch processing

## Technology Stack

- **Python 3.9+**: Core language
- **Flask**: Web framework
- **NLTK**: Natural Language Toolkit for sentiment analysis
- **spaCy**: Advanced NLP for entity recognition and aspect extraction
- **TextBlob**: Additional sentiment analysis capabilities
- **Gunicorn**: WSGI HTTP Server for production

## API Endpoints

### Health Check

```
GET /health
```

Returns the status of the service.

### Analyze Sentiment

```
POST /analyze
```

Analyzes the sentiment of a text.

**Request Body:**
```json
{
  "text": "This product is amazing! I love it and would recommend it to everyone.",
  "product_name": "Wireless Headphones" // Optional
}
```

**Response:**
```json
{
  "score": 0.8316,
  "details": {
    "compound": 0.8316,
    "neg": 0.0,
    "neu": 0.406,
    "pos": 0.594
  },
  "text_sample": "This product is amazing! I love it and would recommend it to everyone.",
  "entities": [
    {
      "name": "Wireless Headphones",
      "type": "PRODUCT",
      "sentiment": 0.8316
    }
  ],
  "aspects": [
    {
      "aspect": "product",
      "sentiment": 0.8316,
      "count": 1,
      "sentence": "This product is amazing! I love it and would recommend it to everyone.",
      "adjectives": ["amazing"]
    }
  ]
}
```

### Analyze Aspects

```
POST /analyze-aspects
```

Extracts aspects and their sentiment from text.

**Request Body:**
```json
{
  "text": "The sound quality is excellent but the battery life is poor.",
  "product_name": "Wireless Headphones" // Optional
}
```

**Response:**
```json
{
  "product_name": "Wireless Headphones",
  "aspects": [
    {
      "aspect": "sound quality",
      "sentiment": 0.6369,
      "count": 1,
      "sentence": "The sound quality is excellent but the battery life is poor.",
      "adjectives": ["excellent"]
    },
    {
      "aspect": "battery life",
      "sentiment": -0.4215,
      "count": 1,
      "sentence": "The sound quality is excellent but the battery life is poor.",
      "adjectives": ["poor"]
    }
  ]
}
```

### Analyze Batch

```
POST /analyze-batch
```

Analyzes sentiment of multiple texts.

**Request Body:**
```json
{
  "texts": [
    "This product is amazing!",
    "This product is terrible.",
    "This product is okay."
  ]
}
```

**Response:**
```json
{
  "results": [
    {
      "score": 0.6369,
      "details": {
        "compound": 0.6369,
        "neg": 0.0,
        "neu": 0.37,
        "pos": 0.63
      },
      "text_sample": "This product is amazing!"
    },
    {
      "score": -0.5423,
      "details": {
        "compound": -0.5423,
        "neg": 0.646,
        "neu": 0.354,
        "pos": 0.0
      },
      "text_sample": "This product is terrible."
    },
    {
      "score": 0.0772,
      "details": {
        "compound": 0.0772,
        "neg": 0.0,
        "neu": 0.74,
        "pos": 0.26
      },
      "text_sample": "This product is okay."
    }
  ],
  "count": 3
}
```

### Compare Texts

```
POST /compare
```

Compares sentiment between two texts.

**Request Body:**
```json
{
  "text1": "This product is amazing! The quality is excellent.",
  "text2": "This product is decent. The quality is good but it has issues."
}
```

**Response:**
```json
{
  "text1": {
    "score": 0.8316,
    "details": {
      "compound": 0.8316,
      "neg": 0.0,
      "neu": 0.37,
      "pos": 0.63
    }
  },
  "text2": {
    "score": 0.1531,
    "details": {
      "compound": 0.1531,
      "neg": 0.186,
      "neu": 0.534,
      "pos": 0.28
    }
  },
  "difference": 0.6785,
  "common_aspects": [
    {
      "aspect": "quality",
      "text1": {
        "sentiment": 0.6369,
        "count": 1
      },
      "text2": {
        "sentiment": 0.4215,
        "count": 1
      },
      "difference": 0.2154
    },
    {
      "aspect": "product",
      "text1": {
        "sentiment": 0.6369,
        "count": 1
      },
      "text2": {
        "sentiment": 0.3182,
        "count": 1
      },
      "difference": 0.3187
    }
  ]
}
```

## Running the Service

### Using Docker

```bash
docker build -t productwhisper-nlp .
docker run -p 5000:5000 productwhisper-nlp
```

### Manual Setup

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Download required NLTK data:

```bash
python -m nltk.downloader vader_lexicon
python -m nltk.downloader punkt
python -m nltk.downloader stopwords
```

3. Download spaCy model:

```bash
python -m spacy download en_core_web_sm
```

4. Run the service:

```bash
python app.py
```

For production, use Gunicorn:

```bash
gunicorn --bind 0.0.0.0:5000 --workers 4 app:app
```

## Testing

Run tests using pytest:

```bash
pytest
```

## Integration with ProductWhisper

This service is designed to be used with the ProductWhisper backend API. The API communicates with this service to analyze product reviews and discussions from various sources.

## License

This project is licensed under the ISC License.
