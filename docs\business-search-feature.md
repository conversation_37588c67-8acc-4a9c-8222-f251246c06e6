# Business Search and Review Analysis Feature

This document provides an overview of the Business Search and Review Analysis feature in ProductWhisper.

## Overview

The Business Search and Review Analysis feature allows users to:

1. Search for businesses across multiple platforms (Google Places, Facebook)
2. View detailed business information including contact details and opening hours
3. Read and analyze customer reviews with sentiment analysis
4. Compare sentiment across different businesses
5. Identify positive and negative aspects of businesses based on reviews

## Components

### Backend Components

1. **Google Places API Integration**
   - Business search functionality
   - Business details retrieval
   - Review collection
   - Photo retrieval

2. **Facebook Graph API Integration**
   - Business page search
   - Business page details
   - Review collection

3. **Sentiment Analysis Service**
   - Review text analysis
   - Sentiment scoring
   - Aspect-based sentiment analysis
   - Entity recognition

4. **Caching System**
   - Redis-based caching
   - Tiered TTL strategy
   - Response optimization

### Frontend Components

1. **Business Search Page**
   - Search form with source selection
   - Results display with filtering options
   - Business card components

2. **Business Details Page**
   - Business information display
   - Photo gallery
   - Contact information
   - Opening hours

3. **Business Reviews Component**
   - Review listing with sentiment indicators
   - Sorting options (recent, rating, sentiment)
   - Sentiment metrics visualization
   - Review filtering

## Usage

### Searching for Businesses

1. Navigate to the Business Search page via the main navigation or by going to `/business/search`
2. Enter a search query (e.g., "electronics store")
3. Optionally select a specific source (Google Places, Facebook, or All)
4. Click "Search" to view results
5. Click on any business card to view detailed information

### Viewing Business Details

1. On the Business Details page, you'll see:
   - Business name, address, and contact information
   - Photo gallery (if available)
   - Opening hours
   - Rating information
   - Sentiment metrics

2. Scroll down to view customer reviews with sentiment analysis
3. Use the "Show/Hide Sentiment" button to toggle sentiment analysis display
4. Use the sort dropdown to organize reviews by different criteria

### Analyzing Review Sentiment

The sentiment analysis provides several insights:

1. **Overall Sentiment Score**
   - Ranges from -1.0 (very negative) to 1.0 (very positive)
   - Color-coded for quick understanding (green for positive, yellow for neutral, red for negative)

2. **Sentiment Distribution**
   - Count of positive, neutral, and negative reviews
   - Percentage breakdown of sentiment

3. **Individual Review Sentiment**
   - Each review has its own sentiment score
   - Highlighted with appropriate colors
   - Sentiment icons for quick visual scanning

## API Endpoints

### Business Search

```
GET /api/business/search
```

Query parameters:
- `query` (required): Search term
- `source` (optional): "google", "facebook", or "all"
- `limit` (optional): Maximum number of results

### Business Details

```
GET /api/business/:source/:id
```

Path parameters:
- `source`: "google" or "facebook"
- `id`: Business ID

Query parameters:
- `with_sentiment` (optional): Include sentiment analysis

### Business Reviews

```
GET /api/business/:source/:id/reviews
```

Path parameters:
- `source`: "google" or "facebook"
- `id`: Business ID

Query parameters:
- `limit` (optional): Maximum number of reviews
- `with_sentiment` (optional): Include sentiment analysis
- `sort_by` (optional): Sorting method

## Configuration

To use this feature, you need to configure the following API keys in the `.env` file:

```
# Google Places API
GOOGLE_PLACES_API_KEY=your_api_key_here

# Facebook Graph API
FACEBOOK_APP_ID=your_app_id_here
FACEBOOK_APP_SECRET=your_app_secret_here
FACEBOOK_ACCESS_TOKEN=your_access_token_here
```

## Testing

You can test the Google Places API integration using the provided test script:

```bash
cd backend
node scripts/test-google-places.js
```

## Future Enhancements

Planned enhancements for this feature include:

1. **Advanced Filtering**
   - Filter businesses by rating, price level, and business type
   - Filter reviews by sentiment, date range, and keywords

2. **Aspect-Based Sentiment Analysis**
   - Extract specific aspects mentioned in reviews (e.g., service, quality, price)
   - Provide sentiment breakdown by aspect

3. **Trend Analysis**
   - Track sentiment changes over time
   - Identify emerging issues or improvements

4. **Review Summarization**
   - Generate concise summaries of review content
   - Highlight key positive and negative points

5. **Competitor Comparison**
   - Side-by-side comparison of businesses
   - Sentiment comparison across competitors
