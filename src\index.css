/* Import Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap");

/* Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Premium color palette */
    --color-primary: 28 52 84; /* Deep Navy Blue */
    --color-primary-light: 45 85 137; /* Lighter Navy */
    --color-primary-dark: 16 32 52; /* Darker Navy */
    --color-secondary: 184 134 11; /* Gold */
    --color-secondary-light: 212 175 55; /* Lighter Gold */
    --color-accent: 142 85 114; /* Mauve */
    --color-neutral: 107 114 128; /* Gray-500 */
    --color-success: 39 124 77; /* Deep Green */
    --color-warning: 193 154 107; /* Soft Gold */
    --color-error: 155 41 21; /* Deep Red */
  }

  body {
    @apply text-gray-800 bg-gray-50 antialiased;
    font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont,
      "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-3xl md:text-4xl mb-6;
  }

  h2 {
    @apply text-2xl md:text-3xl mb-4;
  }

  h3 {
    @apply text-xl md:text-2xl mb-3;
  }

  a {
    @apply text-primary hover:text-primary-dark transition-colors;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-dark focus:ring-primary;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-light focus:ring-secondary;
  }

  .btn-accent {
    @apply bg-accent text-white hover:bg-accent/90 focus:ring-accent;
  }

  .btn-outline {
    @apply border-gray-300 bg-white text-primary hover:bg-gray-50 focus:ring-primary;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }

  .card-header {
    @apply px-4 py-3 border-b border-gray-200 bg-gray-50;
  }

  .card-body {
    @apply p-4;
  }

  .card-footer {
    @apply px-4 py-3 border-t border-gray-200 bg-gray-50;
  }

  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm;
  }

  .form-error {
    @apply mt-1 text-sm text-error;
  }

  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary/10 text-primary-dark;
  }

  .badge-secondary {
    @apply bg-secondary/10 text-secondary;
  }

  .badge-success {
    @apply bg-success/10 text-success;
  }

  .badge-error {
    @apply bg-error/10 text-error;
  }

  .badge-warning {
    @apply bg-warning/10 text-warning;
  }

  .badge-neutral {
    @apply bg-neutral/10 text-neutral;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom overrides */
.bg-gradient-primary {
  min-height: 300px;
  max-height: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #1a365d;
  background-image: none;
  border-radius: 0.75rem;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  padding: 2rem 1rem;
}

.hero-search-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 1.5rem auto 0;
}

.hero-search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 0.5rem;
  border: none;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  backdrop-filter: blur(4px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.hero-search-button {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  padding: 0 1.5rem;
  background-color: #ca8a04;
  color: white;
  border: none;
  border-radius: 0 0.5rem 0.5rem 0;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.hero-search-button:hover {
  background-color: #a16207;
}

.hero-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
}
